import { getTokenMutation } from './graphql/getTokenMutation'
import { graphqlFetch } from '@pylot-data/graphqlFetch'
import type { CustomerToken } from '@pylot-data/fwrdschema'
import { useCart, mergeCarts } from '@corsairitshopify/pylot-cart-manager'
import { useAccount } from './use-account'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { useAuthUI } from './AuthContext'

export type GenerateTokenResponse = {
    data: CustomerToken
}

export type LoginArguments = {
    email: string
    password: string
    cartId?: string
}

export const useLogin = () => {
    const { mutate: fetchAccountInfo } = useAccount()
    const { data: cartData, mutate: updateCart } = useCart()
    const { locale } = useRouter()
    const { loginCallbacks, clearAuthCallback } = useAuthUI()
    const [isLoading, setIsLoading] = useState(false)
    const cartId = cartData?.data?.cart?.id

    return {
        login: async (params: LoginArguments) => {
            setIsLoading(true)
            const res = await graphqlFetch<
                LoginArguments,
                { generateCustomerToken: CustomerToken }
            >({
                query: getTokenMutation,
                variables: { ...params, cartId },
                locale
            })
            if (res.errors) {
                setIsLoading(false)
                throw res.errors
            }

            const cardID =
                window.localStorage.getItem('cart_id') &&
                window.localStorage.getItem('cart_id') !== 'undefined'
                    ? window.localStorage.getItem('cart_id')
                    : ''
            localStorage.setItem(
                'pylot_token',
                res.data!.generateCustomerToken.token!
            )

            const result = await Promise.all([
                fetchAccountInfo(),
                mergeCarts({
                    guestCartId: cardID || '',
                    locale
                }).then((res) => updateCart(res, false))
            ])
            setIsLoading(false)
            //handle login callbacks
            if (loginCallbacks.length) {
                loginCallbacks.forEach((callback) => {
                    if (typeof callback === 'function') {
                        callback()
                    }
                })
                clearAuthCallback('LOGIN_VIEW')
            }
            return result
        },
        isLoading
    }
}
