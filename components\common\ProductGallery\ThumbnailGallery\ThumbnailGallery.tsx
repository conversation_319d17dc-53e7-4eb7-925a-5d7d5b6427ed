import { SwiperProps, SwiperSlideProps } from 'swiper/react'
import SwiperCore, {
    FreeMode,
    Keyboard,
    Mousewheel,
    Navigation,
    Thumbs
} from 'swiper'
import { useTranslation } from 'next-i18next'
import type { Maybe, ProductVideo, ProductImage } from '@pylot-data/pylotschema'
import React, { ReactElement, useState, useEffect } from 'react'
import cn from 'classnames'
import s from './ThumbnailGallery.module.scss'
import { ThumbsEvents, ThumbsOptions } from 'swiper/types'
import dynamic from 'next/dynamic'
import ImageSlide from '../util/ImageSlide'

const FullscreenGallery = dynamic(() => import('../FullscreenGallery'))
import { VideoPlayer } from '@corsairitshopify/corsair-video-player'
import {
    MAIN_RESOLUTION,
    THUMB_RESOLUTION,
    replacingFormat
} from '../util/replacing-format'
import { renderProductImageAlt } from '../util/renderProductImageAlt'
import {
    galleryControlsStatusType,
    updateGalleryControlsStatusFunctionType
} from '../GalleryControls/GalleryControls'
import { galleryControlsLabels } from '../ProductGallery'
import { cloudinaryDimensionsModelType } from '@pylot-data/hooks/contentful/use-content-json'
const CloudinaryAR = dynamic(
    () => import('@components/common/CloudinaryAR/CloudinaryAR'),
    { ssr: false }
)

SwiperCore.use([FreeMode, Keyboard, Mousewheel, Navigation, Thumbs])

const defaultSliderProps = {
    main: {
        spaceBetween: 10,
        navigation: true,
        pagination: {
            clickable: true
        }
    },
    thumbs: {
        spaceBetween: 10,
        slidesPerView: 8
    }
}

type ThumbnailGalleryProps = {
    mediaGalleryEntries: Maybe<ProductVideo | ProductImage>[]
    zoom: boolean
    showFullscreen: boolean
    embedVideoUrl?: string
    cloudinaryDimensionsModel?: cloudinaryDimensionsModelType
    howToLink?: string
    galleryControlsStatus: galleryControlsStatusType
    updateGalleryControlsStatus: updateGalleryControlsStatusFunctionType
    playEmbedVideo: boolean
    setPlayEmbedVideo: (play: boolean) => void
    productTabsVisible?: boolean
    breakpoints?: string
    layout?: 'fill' | 'fixed' | 'intrinsic' | 'responsive' | undefined
    ratio?: string
    showVideo?: boolean
    sliderProps?: {
        main?: {
            allowTouchMove?: boolean
            spaceBetween?: number
            maxSlides?: number
            navigation?: boolean
            pagination?: boolean | { clickable: boolean }
        }
        thumbs?: {
            spaceBetween?: number
            slidesPerView?: number
        }
    }
    thumb?: boolean
    loop?: boolean
}

const ThumbnailGallery = ({
    mediaGalleryEntries,
    zoom,
    embedVideoUrl,
    cloudinaryDimensionsModel,
    howToLink,
    galleryControlsStatus,
    updateGalleryControlsStatus,
    playEmbedVideo,
    setPlayEmbedVideo,
    productTabsVisible,
    breakpoints,
    layout,
    ratio,
    showVideo,
    sliderProps,
    thumb,
    loop
}: ThumbnailGalleryProps): ReactElement => {
    const { t } = useTranslation(['common'])
    const { maxSlides, allowTouchMove } = { ...sliderProps?.main }
    const [isFullscreen, setFullscreen] = useState(false)
    const [sliderIndex, setSliderIndex] = useState(0)
    const [mobileBarPosition, setMobileBarPosition] = useState(0)
    const [swiper, setSwiper] = useState<SwiperCore>()
    const [thumbsSwiper, setThumbsSwiper] = useState<ThumbsEvents>()
    const [indexActive, setIndexActive] = useState<number>(0)
    const [
        SwiperComponent,
        setSwiperComponent
    ] = useState<React.ComponentType<SwiperProps> | null>(null)
    const [
        SwiperSlideComponent,
        setSwiperSlideComponent
    ] = useState<React.ComponentType<SwiperSlideProps> | null>(null)

    useEffect(() => {
        import('swiper/react').then(({ Swiper, SwiperSlide }) => {
            setSwiperComponent(() => Swiper)
            setSwiperSlideComponent(() => SwiperSlide)
        })
    }, [])

    if (!SwiperComponent || !SwiperSlideComponent) {
        const image = replacingFormat(mediaGalleryEntries, MAIN_RESOLUTION)[0]
        return (
            <div className={cn('slider-gallery')}>
                <div
                    className={cn('gallery-container', {
                        ['invisible']: isFullscreen
                    })}
                    role="region"
                    aria-label="carousel"
                >
                    <div />
                    <div
                        className={cn('gallery-slider', {
                            ['invisible']: isFullscreen
                        })}
                    >
                        <div className="swiper swiper-initialized swiper-horizontal swiper-pointer-events">
                            <div
                                className="swiper-wrapper"
                                style={{
                                    transform: 'translate3d(0px, 0px, 0px)'
                                }}
                            >
                                <div className="swiper-slide swiper-slide-active">
                                    <div className="swiper-slide-image-container">
                                        <span>
                                            <button
                                                aria-label={`image 1 of ${mediaGalleryEntries.length}`}
                                                aria-current="true"
                                                tabIndex={-1}
                                            >
                                                {!isFullscreen &&
                                                image?.__typename ===
                                                    'ProductVideo' ? (
                                                    <VideoPlayer
                                                        url={`${image
                                                            .video_content
                                                            ?.video_url!}`}
                                                        index={0}
                                                        image={image?.url}
                                                        isThumbnail={false}
                                                    />
                                                ) : (
                                                    <ImageSlide
                                                        image={image}
                                                        productImageAlt={t(
                                                            'Product Image'
                                                        )}
                                                        layout={layout}
                                                        breakpoints={
                                                            breakpoints
                                                        }
                                                        width={1407}
                                                        height={794}
                                                        index={0}
                                                        zoom={zoom}
                                                        showFullscreen={!0}
                                                        s={s}
                                                        openFullscreen={() =>
                                                            !allowTouchMove &&
                                                            openFullscreen(0)
                                                        }
                                                        ratio={ratio}
                                                    />
                                                )}
                                            </button>
                                        </span>
                                    </div>
                                    <button
                                        className="zoom-in-btn"
                                        tabIndex={0}
                                        aria-label="zoomIn"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    const Swiper = SwiperComponent
    const SwiperSlide = SwiperSlideComponent

    const openFullscreen = (i: number) => {
        setSliderIndex(i)
        setFullscreen(true)
    }

    const syncThumbnail = (swiperCore: SwiperCore, delay: number) => {
        setTimeout(() => swiperCore.thumbs?.update(false), delay)
        setMobileBarPosition(swiperCore.realIndex)
    }

    const handleNextSlide = (e: any, index: number) => {
        if (e.key === 'Enter') {
            swiper?.slideTo(index, 100, true)
            setSliderIndex(index)
        }
    }

    const scrollBelowTheFold = () => {
        const scrollTo = document.getElementById('tabsParent')
        scrollTo?.scrollIntoView({ behavior: 'smooth' })
    }

    const handleStatusUpdate = (index: number) => {
        if (
            galleryControlsStatus.active ===
            galleryControlsStatus.controls[index].label
        ) {
            return
        } else {
            const newStatus = {
                active: galleryControlsStatus.controls[index].label,
                controls: galleryControlsStatus.controls.map((control, i) => ({
                    ...control,
                    active: i === index
                }))
            }
            updateGalleryControlsStatus(newStatus)
        }
    }

    const renderImages = (
        mediaGalleryData: Maybe<ProductVideo | ProductImage>[],
        width = 1050,
        height = 1050,
        showFullscreenGallery = false,
        isThumbnail = false,
        maxSlides = 0,
        ratio?: string
    ) =>
        mediaGalleryData?.map((image, i) => {
            return (
                <div key={image?.url} className="scroll-image-container">
                    <SwiperSlide
                        className={cn(
                            image?.__typename === 'ProductVideo'
                                ? 'video-thumb'
                                : ''
                        )}
                        onClick={() => {
                            handleStatusUpdate(0)
                            swiper?.slideTo(i, 100, true)
                            setSliderIndex(i)
                        }}
                        onKeyPress={(e: any) => handleNextSlide(e, i)}
                    >
                        <div
                            className={cn('swiper-slide-image-container', {
                                'zoom-disabled': allowTouchMove
                            })}
                        >
                            <span>
                                <button
                                    aria-label={`image ${i + 1} of ${
                                        mediaGalleryData?.length
                                    }`}
                                    aria-current={
                                        i === indexActive ? 'true' : 'false'
                                    }
                                    onClick={() => setIndexActive(i)}
                                >
                                    {image?.__typename === 'ProductVideo' ? (
                                        <VideoPlayer
                                            url={`${image.video_content
                                                ?.video_url!}`}
                                            index={i}
                                            playEmbedVideoHandle={
                                                setPlayEmbedVideo
                                            }
                                            playEmbedVideo={playEmbedVideo}
                                            image={image?.url}
                                            isThumbnail={isThumbnail}
                                        />
                                    ) : (
                                        <ImageSlide
                                            image={image}
                                            productImageAlt={renderProductImageAlt(
                                                image
                                            )}
                                            layout={
                                                isThumbnail
                                                    ? 'intrinsic'
                                                    : layout
                                            }
                                            breakpoints={breakpoints}
                                            width={width}
                                            height={height}
                                            index={i}
                                            zoom={zoom}
                                            showFullscreen={
                                                showFullscreenGallery
                                            }
                                            s={s}
                                            openFullscreen={() =>
                                                !allowTouchMove &&
                                                openFullscreen(i)
                                            }
                                            ratio={ratio}
                                        />
                                    )}
                                </button>
                            </span>
                        </div>
                        {image?.__typename !== 'ProductVideo' && (
                            <button
                                className="zoom-in-btn"
                                tabIndex={0}
                                onClick={() => openFullscreen(i)}
                                aria-label={t('zoomIn')}
                            />
                        )}
                    </SwiperSlide>
                </div>
            )
        })

    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
        const html = document.getElementsByTagName('html')[0]
        html.classList.remove('zoom-open')
        if (isFullscreen) html.classList.add('zoom-open')
    }, [isFullscreen])

    return (
        <div className={cn('slider-gallery')}>
            {isFullscreen && (
                <FullscreenGallery
                    closeFullscreen={() => setFullscreen(false)}
                    mediaGalleryEntries={mediaGalleryEntries}
                    sliderIndex={sliderIndex}
                    allowTouchMove={allowTouchMove}
                    embedVideoUrl={embedVideoUrl}
                    cloudinaryDimensionsModel={cloudinaryDimensionsModel}
                    playEmbedVideo={playEmbedVideo}
                    setPlayEmbedVideo={setPlayEmbedVideo}
                    galleryControlsStatus={galleryControlsStatus}
                    updateGalleryControlsStatus={updateGalleryControlsStatus}
                    ratio={ratio}
                />
            )}
            <div
                className={cn(
                    'gallery-container',
                    s['accessory-gallery-container']
                )}
                role="region"
                aria-label="carousel"
            >
                <div
                    className={cn(
                        'gallery-slider',
                        s['accessory-gallery-slider']
                    )}
                >
                    {galleryControlsStatus.active ===
                        galleryControlsLabels.images && (
                        <Swiper
                            {...{
                                ...defaultSliderProps.main,
                                ...sliderProps?.main
                            }}
                            thumbs={{ swiper: thumbsSwiper } as ThumbsOptions}
                            onSwiper={(swiperCore: SwiperCore) => {
                                setSwiper(swiperCore)
                                syncThumbnail(swiperCore, 300)
                            }}
                            onSlideChange={(swiperCore: SwiperCore) =>
                                syncThumbnail(swiperCore, 100)
                            }
                            loop={loop}
                            keyboard
                            allowTouchMove
                        >
                            {renderImages(
                                replacingFormat(
                                    mediaGalleryEntries,
                                    MAIN_RESOLUTION
                                ),
                                1050,
                                1050,
                                true,
                                false,
                                0,
                                ratio
                            )}
                        </Swiper>
                    )}
                    {galleryControlsStatus.active ===
                        galleryControlsLabels.videos &&
                        embedVideoUrl &&
                        showVideo && (
                            <VideoPlayer
                                url={embedVideoUrl}
                                index={0}
                                playEmbedVideoHandle={setPlayEmbedVideo}
                                playEmbedVideo={playEmbedVideo}
                            />
                        )}
                    {galleryControlsStatus.active ===
                        galleryControlsLabels.threeHundredSixty &&
                        cloudinaryDimensionsModel && (
                            <CloudinaryAR
                                modelId={
                                    cloudinaryDimensionsModel.cloudinaryDimensionsModelId
                                }
                                description={
                                    cloudinaryDimensionsModel.arViewerDescription
                                }
                            />
                        )}
                </div>
                {thumb && (
                    <div
                        className={cn(
                            'gallery-thumbnails',
                            s['gallery-thumbnails-vertical']
                        )}
                    >
                        <div className="thumbnails-container accessory-thumbnails">
                            <Swiper
                                onSwiper={setThumbsSwiper}
                                {...{
                                    ...defaultSliderProps.thumbs,
                                    ...sliderProps?.thumbs
                                }}
                                watchSlidesProgress
                                slidesPerView="auto"
                                spaceBetween={22}
                                keyboard
                                mousewheel
                                threshold={20}
                                touchRatio={0.2}
                                direction="vertical"
                            >
                                {renderImages(
                                    replacingFormat(
                                        mediaGalleryEntries,
                                        THUMB_RESOLUTION
                                    ),
                                    106,
                                    106,
                                    false,
                                    true,
                                    maxSlides
                                )}
                            </Swiper>

                            <div className="gallery-link-container flex flex-col ml-4 mt-4">
                                {embedVideoUrl && (
                                    <button
                                        className="flex w-max-content mb-3"
                                        onClick={() => handleStatusUpdate(1)}
                                    >
                                        <svg
                                            width="24"
                                            height="25"
                                            viewBox="0 0 24 25"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <g clipPath="url(#clip0_2123_1448)">
                                                <path
                                                    d="M12 2.29187C6.48 2.29187 2 6.77187 2 12.2919C2 17.8119 6.48 22.2919 12 22.2919C17.52 22.2919 22 17.8119 22 12.2919C22 6.77187 17.52 2.29187 12 2.29187ZM12 20.2919C7.59 20.2919 4 16.7019 4 12.2919C4 7.88187 7.59 4.29187 12 4.29187C16.41 4.29187 20 7.88187 20 12.2919C20 16.7019 16.41 20.2919 12 20.2919ZM9.5 16.7919L16.5 12.2919L9.5 7.79187V16.7919Z"
                                                    fill="#ECE81A"
                                                />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_2123_1448">
                                                    <rect
                                                        width="24"
                                                        height="24"
                                                        fill="white"
                                                        transform="translate(0 0.29187)"
                                                    />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                        <p>{t('PLAY VIDEO')}</p>
                                    </button>
                                )}
                                {cloudinaryDimensionsModel && (
                                    <button
                                        className="flex w-max-content mb-3"
                                        onClick={() => handleStatusUpdate(2)}
                                    >
                                        <svg
                                            width="24"
                                            height="25"
                                            viewBox="0 0 24 25"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <g clipPath="url(#clip0_2112_1455)">
                                                <path
                                                    d="M7.53006 21.7719C4.26006 20.2319 1.92006 17.0519 1.56006 13.2919H0.0600586C0.570059 19.4519 5.72006 24.2919 12.0101 24.2919L12.6701 24.2619L8.86006 20.4519L7.53006 21.7719ZM8.42006 15.2519C8.23006 15.2519 8.05006 15.2219 7.90006 15.1719C7.74006 15.1119 7.61006 15.0419 7.50006 14.9319C7.39006 14.8319 7.30006 14.7119 7.24006 14.5619C7.18006 14.4219 7.15006 14.2619 7.15006 14.0919H5.85006C5.85006 14.4519 5.92006 14.7719 6.06006 15.0419C6.20006 15.3119 6.39006 15.5419 6.62006 15.7319C6.86006 15.9119 7.13006 16.0519 7.44006 16.1419C7.74006 16.2419 8.06006 16.2919 8.40006 16.2919C8.77006 16.2919 9.12006 16.2419 9.43006 16.1419C9.75006 16.0419 10.0301 15.8919 10.2601 15.7019C10.4901 15.5119 10.6801 15.2719 10.8101 14.9819C10.9401 14.6919 11.0101 14.3719 11.0101 14.0119C11.0101 13.8219 10.9901 13.6319 10.9401 13.4519C10.8901 13.2719 10.8201 13.1019 10.7101 12.9419C10.6101 12.7819 10.4701 12.6419 10.3101 12.5119C10.1401 12.3819 9.94006 12.2819 9.70006 12.2019C9.90006 12.1119 10.0701 12.0019 10.2201 11.8719C10.3701 11.7419 10.4901 11.6019 10.5901 11.4519C10.6901 11.3019 10.7601 11.1519 10.8101 10.9919C10.8601 10.8319 10.8801 10.6719 10.8801 10.5119C10.8801 10.1519 10.8201 9.83187 10.7001 9.55187C10.5801 9.27187 10.4101 9.04187 10.1901 8.86187C9.99006 8.67187 9.72006 8.53187 9.42006 8.43187C9.11006 8.34187 8.77006 8.29187 8.40006 8.29187C8.04006 8.29187 7.71006 8.34187 7.40006 8.45187C7.10006 8.56187 6.83006 8.71187 6.61006 8.90187C6.40006 9.09187 6.23006 9.31187 6.10006 9.57187C5.98006 9.83187 5.92006 10.1119 5.92006 10.4219H7.22006C7.22006 10.2519 7.25006 10.1019 7.31006 9.97187C7.37006 9.84187 7.45006 9.72187 7.56006 9.63187C7.67006 9.54187 7.79006 9.46187 7.94006 9.41187C8.09006 9.36187 8.24006 9.33187 8.42006 9.33187C8.82006 9.33187 9.12006 9.43187 9.31006 9.64187C9.50006 9.84187 9.60006 10.1319 9.60006 10.5019C9.60006 10.6819 9.57006 10.8419 9.52006 10.9919C9.47006 11.1419 9.38006 11.2619 9.27006 11.3619C9.16006 11.4619 9.02006 11.5419 8.86006 11.6019C8.70006 11.6619 8.50006 11.6919 8.28006 11.6919H7.51006V12.7219H8.28006C8.50006 12.7219 8.70006 12.7419 8.88006 12.7919C9.06006 12.8419 9.21006 12.9219 9.33006 13.0219C9.45006 13.1319 9.55006 13.2619 9.62006 13.4219C9.69006 13.5819 9.72006 13.7719 9.72006 13.9919C9.72006 14.4019 9.60006 14.7119 9.37006 14.9219C9.14006 15.1519 8.82006 15.2519 8.42006 15.2519ZM16.9701 9.33187C16.6501 9.00187 16.2701 8.74187 15.8301 8.56187C15.3901 8.38187 14.9001 8.29187 14.3601 8.29187H12.0001V16.2919H14.3001C14.8501 16.2919 15.3601 16.2019 15.8101 16.0219C16.2601 15.8419 16.6501 15.5919 16.9701 15.2619C17.2901 14.9319 17.5401 14.5319 17.7101 14.0719C17.8801 13.6019 17.9701 13.0819 17.9701 12.5019V12.1019C17.9701 11.5219 17.8801 11.0019 17.7101 10.5319C17.5401 10.0619 17.2901 9.66187 16.9701 9.33187ZM16.5801 12.4919C16.5801 12.9119 16.5301 13.2819 16.4401 13.6219C16.3401 13.9519 16.2001 14.2419 16.0101 14.4719C15.8201 14.7019 15.5801 14.8819 15.3001 15.0019C15.0101 15.1219 14.6801 15.1819 14.3101 15.1819H13.4001V9.41187H14.3701C15.0901 9.41187 15.6401 9.64187 16.0101 10.1019C16.3901 10.5619 16.5801 11.2219 16.5801 12.0919V12.4919ZM12.0101 0.29187L11.3501 0.32187L15.1601 4.13187L16.4901 2.80187C19.7601 4.35187 22.1001 7.52187 22.4501 11.2819H23.9501C23.4501 5.13187 18.3001 0.29187 12.0101 0.29187Z"
                                                    fill="#ECE81A"
                                                />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_2112_1455">
                                                    <rect
                                                        width="24"
                                                        height="24"
                                                        fill="white"
                                                        transform="translate(0 0.29187)"
                                                    />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                        <p>{t('VIEW IN 3D')}</p>
                                    </button>
                                )}
                                {howToLink && (
                                    <a
                                        href={howToLink}
                                        className="explore-link items-center flex w-max-content mb-3"
                                    >
                                        <svg
                                            className="p-1 bg-yellow"
                                            width="8"
                                            height="10"
                                            viewBox="0 0 8 10"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M7.5 4.13397C8.16667 4.51888 8.16667 5.48113 7.5 5.86603L1.5 9.33013C0.833334 9.71503 5.06336e-07 9.2339 5.39985e-07 8.4641L8.42827e-07 1.5359C8.76476e-07 0.766098 0.833334 0.284973 1.5 0.669873L7.5 4.13397Z"
                                                fill="black"
                                            />
                                        </svg>

                                        <p>{t('How-to videos')}</p>
                                    </a>
                                )}
                                {productTabsVisible && (
                                    <button
                                        className="features flex w-max-content"
                                        onClick={scrollBelowTheFold}
                                    >
                                        <svg
                                            width="24"
                                            height="25"
                                            viewBox="0 0 24 25"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <g clipPath="url(#clip0_2123_1460)">
                                                <rect
                                                    x="3"
                                                    y="3.29187"
                                                    width="18"
                                                    height="18"
                                                    rx="1"
                                                    stroke="#ECE81A"
                                                    strokeWidth="2"
                                                />
                                                <path
                                                    d="M12 5.29187L13.5716 10.1288H18.6574L14.5429 13.1181L16.1145 17.955L12 14.9656L7.8855 17.955L9.4571 13.1181L5.3426 10.1288H10.4284L12 5.29187Z"
                                                    fill="#ECE81A"
                                                />
                                            </g>
                                            <defs>
                                                <clipPath id="clip0_2123_1460">
                                                    <rect
                                                        width="24"
                                                        height="24"
                                                        fill="white"
                                                        transform="translate(0 0.29187)"
                                                    />
                                                </clipPath>
                                            </defs>
                                        </svg>
                                        <p>{t('More Features')}</p>
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                )}
                {galleryControlsStatus.active ===
                    galleryControlsLabels.images && (
                    <div className="mobile-slider-bar accessory-mobile-slider-bar">
                        <span
                            style={{
                                left: `${
                                    (100 / mediaGalleryEntries.length) *
                                    mobileBarPosition
                                }%`,
                                width: `${100 / mediaGalleryEntries.length}%`
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    )
}

export default ThumbnailGallery
