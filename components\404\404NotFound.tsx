import { Component404 } from './404'
import type { Interface404ParsedEntries } from 'pages/404'
import Head from 'next/head'
import { useContentGroup } from '@corsairitshopify/pylot-tag-manager/src/TrackContentGroup'
import { useEffect } from 'react'

export const NotFound = ({
    pageContent
}: Interface404ParsedEntries): JSX.Element | null => {
    const { handleChangeContentGroup } = useContentGroup()!

    useEffect(() => {
        handleChangeContentGroup({
            contentGroup1: 'Corsair',
            contentGroup2: '',
            contentGroup3: ''
        })
    }, [])

    return (
        <>
            {/* CRS-4542 remove duplicate SEO tags. Using SEO tag in the components/common/Head/Head.tsx */}
            {/*<SEO
                titleOverride={t('404|Page not found')}
                socialDefaultContent={socialDefaultContent}
            />*/}
            <Head>
                {/* eslint-disable-next-line i18next/no-literal-string */}
                <title>Page Not Found</title>
            </Head>
            <Component404 pageContent={pageContent} />
        </>
    )
}
