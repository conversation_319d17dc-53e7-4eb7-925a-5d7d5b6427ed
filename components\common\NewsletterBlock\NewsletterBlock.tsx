import cn from 'classnames'
import Link from 'next/link'
import { ChangeEvent, FC, useState } from 'react'
import Newsletter from '../Newsletter/Newsletter'
import styles from './NewsletterBlock.module.scss'
import { NewsletterBlockContent } from './NewsletterBlock.types'

interface Props {
    contents: NewsletterBlockContent
}

const NewsletterBlock: FC<Props> = ({
    contents: {
        actionButtonText,
        agreePolicyText,
        description,
        placeholder,
        policyText,
        privacyLink,
        title
    }
}) => {
    const [isAgreeTerm, setIsAgreeTerm] = useState(false)
    const [formSubmitSuccess, setFormSubmitSuccess] = useState(false)

    const handleUpdateAgreeTerm = (e: ChangeEvent<HTMLInputElement>) => {
        setIsAgreeTerm(e.target.checked)
    }

    const handleFormSubmitSuccess = () => {
        setFormSubmitSuccess(true)
    }

    return (
        <section
            id={title}
            className={cn('scuf-container', styles.NewsletterBlockWrapper)}
        >
            <div className={styles.LeftContent}>
                <h3>{title}</h3>
                <p> {description}</p>
            </div>
            <div className={styles.RightContent}>
                <Newsletter
                    placeholder={placeholder}
                    actionLabel={actionButtonText}
                    disabledSubmit={!isAgreeTerm}
                    cbWhenFormSubmitSuccess={handleFormSubmitSuccess}
                />
                {formSubmitSuccess ? null : (
                    <div className={styles.CheckBoxWrapper}>
                        <div className={styles.InputWrap}>
                            <input
                                type="checkbox"
                                checked={isAgreeTerm}
                                onChange={handleUpdateAgreeTerm}
                            />
                        </div>

                        <p>
                            {agreePolicyText}
                            <Link href={privacyLink}>
                                <a>{policyText}</a>
                            </Link>
                        </p>
                    </div>
                )}
            </div>
        </section>
    )
}

export default NewsletterBlock
