import CorsairImage from '@corsairitshopify/corsair-image'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { ImageTextSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselImageTextCTA.module.scss'
import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { CTA } from '../CTA'

type CarouselImageTextItemProps = {
    product: ImageTextSlideType
}

const CarouselImageTextItem: FC<CarouselImageTextItemProps> = ({ product }) => {
    const { t } = useTranslation(['common'])
    const isMobile = useMediaQuery('(max-width: 767px)')

    const style = {
        background: product?.backgroundColor
            ? product.backgroundColor
            : isMobile && product?.backgroundImageMobile
            ? `url(${product?.backgroundImageMobile?.[0]?.secure_url})` ||
              `url(${product?.backgroundImageDesktop?.[0]?.secure_url})`
            : `url(${product?.backgroundImageDesktop?.[0]?.secure_url})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
    }

    return (
        <div className={s['slider-item-container']} style={style}>
            <div className={s['slider-item-text-block']}>
                <div className={s['inner-text-wrapper']}>
                    {product.heading ? (
                        <h2
                            className={s['slider-item-heading']}
                            style={{ color: product.headingColor }}
                        >
                            {product.heading}
                        </h2>
                    ) : null}
                    {product?.quote ? (
                        <p
                            className={s['slider-item-quote']}
                            style={{ color: product.copyColor }}
                            dangerouslySetInnerHTML={{
                                __html: product?.quote
                            }}
                        />
                    ) : null}
                    {product?.cta ? (
                        <div className="inline-block">
                            <CTA
                                cta={product.cta}
                                className={cn()}
                                containerClassname={cn(
                                    s['hero-standard-cta-container'],
                                    s['slider-item-cta'],
                                    'scuf-button-primary',
                                    'scuf-show-right-arrow'
                                )}
                                rightIcon={
                                    <div className={s['suffix-icon']}>
                                        <ArrowRightIcon />
                                    </div>
                                }
                            />
                        </div>
                    ) : null}
                </div>
            </div>
            <div className={cn(s['slider-item-image-container'])}>
                <CorsairImage
                    keepOrigin
                    src={convertUrlFormat(
                        product.cloudinaryMainImage?.[0]?.secure_url
                    )}
                    alt={t(
                        `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                    )}
                    objectFit="cover"
                    layout="fill"
                />
            </div>
        </div>
    )
}

export default CarouselImageTextItem
