import s from './ImageWithDescription.module.scss'
import { ImageWithDescriptionProps } from '../types'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import CorsairImage from '@corsairitshopify/corsair-image'
import { useTranslation } from 'next-i18next'
const Mobile = ({ content }: ImageWithDescriptionProps): JSX.Element | null => {
    const { t } = useTranslation(['common'])
    const FIRST_IMAGE_INDEX = 0
    const SECOND_IMAGE_INDEX = 1
    return (
        <div className="w-full px-5 md:hidden">
            <div className="left-slide">
                <div className={s['image-container']}>
                    {content?.cloudinaryMobileMedia && (
                        <CorsairImage
                            keepOrigin
                            src={
                                content?.cloudinaryMobileMedia[
                                    FIRST_IMAGE_INDEX
                                ]?.secure_url
                                    ? convertUrlFormat(
                                          content?.cloudinaryMobileMedia[
                                              FIRST_IMAGE_INDEX
                                          ]?.secure_url
                                      )
                                    : ''
                            }
                            alt={correctImageAlt(
                                t(
                                    `alt|${content?.cloudinaryMobileMedia?.[0]?.context?.custom?.alt}`
                                )
                            )}
                            layout="fixed"
                            width={413}
                            height={254}
                        />
                    )}
                </div>
                <div className={s['text-container']}>
                    <div className={s['left-inner']}>
                        <div
                            className={`${s['inner-title']} ${s['label']}`}
                            style={{
                                color: content?.titleFontColor,
                                borderBottomColor: content?.lineBelowColor
                            }}
                        >
                            {content?.title1stImage}
                        </div>
                        <p
                            dangerouslySetInnerHTML={{
                                __html: content?.copy || ''
                            }}
                            className={s['body-copy']}
                        />
                        <div className={s['space-between-copy-disclaimer']} />
                    </div>
                </div>
                <div className={s['right-slide']}>
                    <div className={s['image-container']}>
                        {content?.cloudinaryMobileMedia && (
                            <CorsairImage
                                keepOrigin
                                src={`${
                                    content?.cloudinaryMobileMedia[
                                        SECOND_IMAGE_INDEX
                                    ]?.secure_url
                                        ? convertUrlFormat(
                                              content?.cloudinaryMobileMedia[
                                                  SECOND_IMAGE_INDEX
                                              ]?.secure_url
                                          )
                                        : ''
                                }`}
                                alt={correctImageAlt(
                                    t(
                                        `alt|${content?.cloudinaryMobileMedia?.[0]?.context?.custom?.alt}`
                                    )
                                )}
                                layout="fixed"
                                width={413}
                                height={254}
                            />
                        )}
                    </div>
                    <div className={s['text-container']}>
                        <div className={s['right-inner']}>
                            <div
                                className={`${s['inner-title']} ${s['label']}`}
                                style={{
                                    color: content?.titleFontColor2ndImage,
                                    borderBottomColor:
                                        content?.lineBelowColor2ndImage
                                }}
                            >
                                {content?.title2ndImage}
                            </div>
                            <p
                                dangerouslySetInnerHTML={{
                                    __html: content?.copy2ndImage || ''
                                }}
                                className={s['body-copy']}
                            />
                            <div
                                className={s['space-between-copy-disclaimer']}
                            />
                            <span
                                className={`${s['right-below-text']} ${s['disclaimer-copy']}`}
                            >
                                {content?.disclaimerCopy}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Mobile
