import { useMemo, useRef, useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import s from './GalleryText.module.scss'
import { A11y, Navigation, Pagination } from 'swiper'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import CarouselGalleryItems from './CarouselGalleryItems'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import cn from 'classnames'
import NavigationButtons from './NavigationButtons'
import { generateRandomString } from 'helpers/stringHelper'

interface CarouselGalleryProps {
    galleryImages: CloudinaryImage[]
    useShadow?: boolean
    identifier: string
}

const CarouselGallery: React.FC<CarouselGalleryProps> = ({
    galleryImages = [],
    useShadow = false,
    identifier
}) => {
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const [isEnd, setIsEnd] = useState(false)
    const [isStart, setIsStart] = useState(true)
    const isMobile = useMediaQuery('(max-width: 767px)')

    const formattedName = useMemo(() => {
        return identifier
            ? `${generateRandomString(5)}-${identifier.replace(
                  /[^a-zA-Z]/g,
                  ''
              )}`
            : ''
    }, [identifier])

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }
    const handleSlideChange = (swiper: any) => {
        setIsStart(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
        animatePagination(swiper)
    }
    return (
        <div className={cn(s['slider-container'])}>
            <div className="h-full w-full relative">
                {!isStart && useShadow && (
                    <div
                        className={cn(s['faded-left'], {
                            [s['is-last-item']]: isEnd
                        })}
                    />
                )}
                {!isEnd && useShadow && <div className={s['faded-right']} />}
                <div className="md-max:hidden">
                    <NavigationButtons
                        formattedName={formattedName}
                        isEnd={isEnd}
                        isStart={isStart}
                        position="default"
                        key={formattedName}
                    />
                </div>
                <Swiper
                    className={cn(
                        s['swiper-container'],
                        isEnd && s['last-item-reached'],
                        (!isEnd || isStart) && s['first-item-reached']
                    )}
                    modules={[Navigation, Pagination, A11y]}
                    mousewheel
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    speed={500}
                    spaceBetween={20}
                    slidesPerView={useShadow ? 1.1 : 1}
                    slidesPerGroup={1}
                    centeredSlides
                    navigation={{
                        prevEl: `.${formattedName}-prev`,
                        nextEl: `.${formattedName}-next`,
                        disabledClass: '.disabled:opacity-50'
                    }}
                    onBreakpoint={(swiper) => {
                        animatePagination(swiper)
                    }}
                    onSlideChange={(swiper) => {
                        animatePagination(swiper)
                        handleSlideChange(swiper)
                    }}
                    // rewind
                    pagination={{
                        el: `.${formattedName}-pagination`,
                        clickable: true,
                        renderBullet: (idx, className) => {
                            return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                        }
                    }}
                    onSwiper={(swiper) => {
                        setIsEnd(swiper.isEnd)
                    }}
                    lazy
                    style={{
                        height: '100%',
                        width: '100%'
                    }}
                >
                    {galleryImages &&
                        galleryImages.length > 0 &&
                        galleryImages.map((prod, key) => (
                            <SwiperSlide key={key}>
                                <CarouselGalleryItems galleryImages={prod} />
                            </SwiperSlide>
                        ))}
                </Swiper>
            </div>
            <div className={cn(s['pagination-container'])}>
                <div className="inline-block relative">
                    <div
                        ref={paginationRef}
                        className={`${formattedName}-pagination flex gap-4`}
                    />
                    <div className="dots-line" />
                    <div
                        ref={activeDotRef}
                        className={cn('active-animated-dot')}
                    />
                </div>
            </div>
        </div>
    )
}

export default CarouselGallery
