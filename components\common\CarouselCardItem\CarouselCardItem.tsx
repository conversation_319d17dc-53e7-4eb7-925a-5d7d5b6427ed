import Link from 'next/link'
import { ChevronRight } from 'react-feather'

import { CTAButtonType } from '@pylot-data/hooks/contentful/use-content-json'

import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

import s from './CarouselCardItem.module.scss'

interface CarouselCardItemProps {
    title: string
    description?: string
    image: CloudinaryImage[]
    button?: CTAButtonType
}

const PRIMARY_COLOR = 'var(--primary)'

const CarouselCardItem: React.FC<CarouselCardItemProps> = (props) => {
    const { image, description = '', title, button } = props

    return (
        <div className={s.MosaicGalleryItem}>
            <div className={s.ImageWrapper}>
                <img
                    src={image?.[0]?.secure_url}
                    alt={image?.[0]?.context?.custom?.alt || ''}
                    loading="lazy"
                />
            </div>
            <div className={s.Content}>
                <h5 aria-label={title}>{title}</h5>
                {!!description && <p aria-label={description}>{description}</p>}
                {button && (
                    <Link href={button?.url}>
                        <a>
                            <span> {button?.displayText} </span>
                            <ChevronRight
                                color={PRIMARY_COLOR}
                                className={s.Icon}
                                size={15}
                            />
                        </a>
                    </Link>
                )}
            </div>
        </div>
    )
}

export default CarouselCardItem
