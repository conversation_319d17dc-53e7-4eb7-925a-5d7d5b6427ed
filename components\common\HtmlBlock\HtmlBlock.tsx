import React, { useMemo } from 'react'
import { IHTML<PERSON>lock, JsCssScript } from '../types'
import s from './styles/index.module.scss'
import { useScript } from './hooks/useScript'
import decode from '../../../lib/utils/htmlencoder'
import cn from 'classnames'

interface HtmlBlockProps {
    props: IHTMLBlock
}

const getTemplateNames = (linkedScript?: JsCssScript[]): string =>
    linkedScript
        ?.reduce((total: string, currentValue) => {
            return currentValue?.templateName
                ? `${total} ${currentValue.templateName}`
                : total
        }, '')
        ?.trim() || ''

const HtmlBlock = ({ props }: HtmlBlockProps) => {
    //inject linked js to render in order

    useScript(props.linkedScript)

    const templateNames = useMemo(() => getTemplateNames(props?.linkedScript), [
        props.linkedScript
    ])

    const encodedHtml = props.markup
        ? decode(props.markup)
        : props.longDescription

    const undoStyling = props.undoMigrationStyling

    const externalCss: string[] = []
    if (props.linkedScript?.length) {
        props.linkedScript?.forEach((data) => {
            if (data.linkedCss?.length) {
                data.linkedCss?.forEach((link) => {
                    if (externalCss.indexOf(link) === -1) {
                        externalCss.push(link)
                    }
                })
            }
        })
    }
    const linkedCss = externalCss.map((link) => (
        <link key={link} href={link} rel="stylesheet" type="text/css" />
    ))

    return (
        <>
            <style>
                {`

                    #fp-nav {
                        z-index: 1000 !important;
                    }

                    #fp-nav .hidden {
                        visibility: hidden;
                        opacity: 0;
                    }
                `}
            </style>
            <div>{linkedCss}</div>
            {undoStyling ? (
                <div
                    className={cn(
                        props.customClassName || 'main-content',
                        templateNames
                    )}
                >
                    <div dangerouslySetInnerHTML={{ __html: encodedHtml }} />
                </div>
            ) : (
                <div id={s['HtmlBlock']}>
                    <div className="pdpWrapper" id="pdp-corsair">
                        <section id="panel2">
                            <div className="content" id="tab-overview">
                                <div id="pdpWrapper">
                                    <div
                                        className={cn(
                                            props.customClassName ||
                                                'main-content',
                                            templateNames
                                        )}
                                    >
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: encodedHtml
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            )}
        </>
    )
}

export default HtmlBlock
