.thumb,
.thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
}

.thumb {
    @apply absolute h-0 pointer-events-none w-full;
    outline: none;
}

.slider {
    @apply relative w-full;
}

.slider-track,
.slider-range {
    position: relative;
    border-radius: 3px;
}

.slider-track {
    @apply w-full z-1;
    height: 5px;
    background-color: #dbdbdb;
    top: -1px;
}

.slider-range {
    @apply z-2;
    height: 5px;
    background-color: #ff5708;
    top: -7px;
}

/* Removing the default appearance */
.thumb,
.thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
}

/* For Chrome browsers */
.thumb::-webkit-slider-thumb {
    top: 0px;
    width: 16px;
    height: 16px;
    pointer-events: all;
    background-color: #ff5708;
    @apply rounded-full border-none cursor-pointer relative;
}

/* For Firefox browsers */
.thumb::-moz-range-thumb {
    top: 12px;
    width: 16px;
    height: 16px;
    pointer-events: all;
    background-color: #ff5708;
    right: 14px;
    @apply rounded-full border-none cursor-pointer relative;
}

.slider-left-value {
    left: 6px;
}

.slider-right-value {
    right: -4px;
}

.MultiRangeWrapper {
    .AccordionWrapper {
        @apply border-none;

        .MainSliderWrapper {
            margin: 5px 8px;

            & > span {
                font-size: 12px;
                color: var(--secondary-off-black);
                @apply font-sofiaSans font-light;
            }
        }

        > div {
            padding: 0;
        }

        .RangeWrapper {
            margin-top: 15px;
            height: 8px;
            @apply relative;
        }

        [class*='accordion-wrapper'] {
            border-top: none;
            border-left: none;
            border-right: none;
            border-bottom: 1px solid var(--steel-gray20);

            div[class*='accordion-body'] {
                padding: 0px 8px 5px 8px;
                max-height: 250px;
                @apply overflow-y-auto;
            }

            div[class*='accordion-header'] {
                margin-bottom: 0px;
                padding-block: 8px;
                [class*='accordion-header-title'] {
                    font-size: 16px;
                    line-height: 24px;
                    color: var(--secondary-off-black);
                    @apply font-sofiaSans uppercase mb-0 font-medium;
                }
            }
        }

        [class*='Accordion_active'] {
            div[class*='accordion-body'] {
                margin-bottom: 0px;
            }
        }
    }

    .PriceDescription {
        font-size: 12px;
        line-height: 24px;
        color: var(--secondary-off-black);
        @apply font-sofiaSans font-light flex;
    }
}
