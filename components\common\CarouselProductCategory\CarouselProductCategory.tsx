/* eslint-disable i18next/no-literal-string */

import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import { generateRandomString } from 'helpers/stringHelper'
import { useMemo, useRef, useState } from 'react'
import { A11y, Navigation, Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'
import { CtaButton } from '../Carousel/Carousel'
import {
    CarouselWrapperType,
    ProductsSlideType
} from '../CarouselWrapper/CarouselWrapper.types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { JsonToCss } from '../HeroBanner/Util/JsonToCss'
import { IMeta } from '../types'
import s from './CarouselProductCategory.module.scss'
import CarouselProductCategoryItem from './CarouselProductCategoryItem'
import NavigationButtons from './NavigationButtons'

export type CslData = ProductsSlideType[]

type CarouselProductCategoryProps = {
    content: CarouselWrapperType
}

type DesktopMobileImage = {
    desktopProductTitle: string
    desktopProductDescription: string
    mobileProductTitle: string
    desktop: ImageComponent
    mobile: ImageComponent
    ctaButton?: CtaButton
}

type ImageComponent = {
    title: string
    image: ImageType
    url: string
    cloudinaryImage?: CloudinaryImage[]
}

export type CSLObject = {
    productImages: DesktopMobileImage[]
    identifier: string
    heading: string
    cloudinaryLogo: CloudinaryImage[]
    cloudinaryMainImage: CloudinaryImage[]
}

export type CslContainerTiles = {
    identifier: string
    productSlide: CSLObject[]
    copyBlock?: { subHeading?: string; heading?: string; text?: string }
    meta: IMeta<'carouselppContainer'>
    displayType: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryMobileBackgroundImage?: CloudinaryImage[]
    backgroundColor?: string
    desktopPaddingTop?: string
    desktopPaddingBottom?: string
    mobilePaddingTop?: string
    mobilePaddingBottom?: string
}

const CarouselProductCategory = ({
    content: carouselContainer
}: CarouselProductCategoryProps): JSX.Element | null => {
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const [isStart, setIsStart] = useState(true)
    const [isEnd, setIsEnd] = useState(false)
    const isMobile = useMediaQuery('(max-width: 767px)')
    const { navigationButtonsPosition = 'default' } = carouselContainer

    const paddingsClassheading =
        (carouselContainer?.heading &&
            carouselContainer?.heading?.replace(/[^a-zA-Z0-9]/g, '')) ??
        'BlockImage'

    const formattedName = useMemo(() => {
        return generateRandomString(8)
    }, [])

    if (!carouselContainer) return null

    const productSlides: CslData = carouselContainer.productSlide as ProductsSlideType[]

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: any) => {
        setIsStart(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
        animatePagination(swiper)
    }

    const stylesDesktop = {
        ...(carouselContainer?.desktopPaddingTop && {
            'padding-top': `${carouselContainer?.desktopPaddingTop}rem;`
        }),
        ...(carouselContainer?.desktopPaddingBottom && {
            'padding-bottom': `${carouselContainer?.desktopPaddingBottom}rem;`
        }),
        ...(carouselContainer?.cloudinaryBackgroundImage && {
            background: `no-repeat center center / cover url(${carouselContainer?.cloudinaryBackgroundImage?.[0].secure_url});`,
            'background-size': 'cover'
        }),
        ...(carouselContainer?.backgroundColor && {
            background: `${carouselContainer?.backgroundColor};`
        })
    }

    const stylesMobile = {
        ...(carouselContainer?.mobilePaddingTop && {
            'padding-top': `${carouselContainer?.mobilePaddingTop}rem;`
        }),
        ...(carouselContainer?.mobilePaddingBottom && {
            'padding-bottom': `${carouselContainer?.mobilePaddingBottom}rem;`
        }),
        ...(carouselContainer?.cloudinaryMobileBackgroundImage && {
            background: `no-repeat center center / cover url(${carouselContainer?.cloudinaryMobileBackgroundImage?.[0].secure_url});`,
            'background-size': 'cover'
        }),
        ...(carouselContainer?.backgroundColor && {
            background: `${carouselContainer?.backgroundColor};`
        })
    }

    return (
        <div
            id={carouselContainer?.identifier}
            className={cn(`verticalPaddings-${paddingsClassheading}`)}
        >
            <style jsx>{`
                @media screen and (min-width: 768px){
                    .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                stylesDesktop
            )}
                }
                @media screen and (max-width: 767px) {
                    .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                stylesMobile
            )}
                }
            `}</style>

            <div
                className={cn(s['product-category-container'], {
                    [s['bp-1366']]: navigationButtonsPosition == 'top'
                })}
            >
                <div className={s['product-tiles-top-container']}>
                    {carouselContainer?.heading && (
                        <h3
                            className={cn(s['header'], {
                                [s['large-heading']]:
                                    navigationButtonsPosition == 'default'
                            })}
                            style={{ color: carouselContainer?.headingColor }}
                        >
                            {carouselContainer?.heading}
                        </h3>
                    )}
                    {navigationButtonsPosition == 'top' && (
                        <div className={s['nav-buttons']}>
                            <NavigationButtons
                                formattedName={formattedName}
                                isEnd={isEnd}
                                isStart={isStart}
                                position="top"
                                key={carouselContainer.identifier}
                            />
                        </div>
                    )}
                </div>

                <div
                    className={cn(
                        s['product-tiles-swiper-container'],
                        'overflow-hidden'
                    )}
                >
                    {navigationButtonsPosition === 'default' && (
                        <>
                            {!isStart && (
                                <div
                                    className={cn(s['faded-left'], {
                                        [s['is-last-item']]: isEnd
                                    })}
                                />
                            )}
                            {!isEnd && <div className={s['faded-right']} />}
                            <div className="md-max:hidden">
                                <NavigationButtons
                                    formattedName={formattedName}
                                    isEnd={isEnd}
                                    isStart={isStart}
                                    position="default"
                                    key={carouselContainer.identifier}
                                />
                            </div>
                        </>
                    )}

                    <Swiper
                        className={cn(
                            s['swiper-container'],
                            'swiper-container',
                            isEnd &&
                                navigationButtonsPosition === 'default' &&
                                s['last-item-reached'],
                            (!isEnd || isStart) &&
                                navigationButtonsPosition === 'default' &&
                                s['first-item-reached']
                        )}
                        modules={[Navigation, Pagination, A11y]}
                        mousewheel
                        key={
                            isMobile
                                ? `${formattedName}-mobile`
                                : `${formattedName}-desktop`
                        }
                        speed={500}
                        spaceBetween={20}
                        slidesPerGroup={1}
                        slidesPerView={1.2}
                        // slidesOffsetBefore={20}
                        // slidesOffsetAfter={20}
                        navigation={{
                            prevEl: `.${formattedName}-prev`,
                            nextEl: `.${formattedName}-next`,
                            disabledClass: '.disabled:opacity-50'
                        }}
                        onBreakpoint={(swiper) => {
                            animatePagination(swiper)
                        }}
                        onSlideChange={(swiper) => {
                            animatePagination(swiper)
                            handleSlideChange(swiper)
                        }}
                        // rewind
                        pagination={{
                            el: `.${formattedName}-pagination`,
                            clickable: true,
                            renderBullet: (idx, className) => {
                                return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                            }
                        }}
                        breakpoints={{
                            500: {
                                slidesPerView:
                                    navigationButtonsPosition === 'default'
                                        ? 1.1
                                        : 2,
                                slidesPerGroup: 1
                            },
                            768: {
                                slidesPerView:
                                    navigationButtonsPosition === 'default'
                                        ? 2.2
                                        : 3,
                                slidesPerGroup: 1
                            },
                            1024: {
                                slidesPerView:
                                    navigationButtonsPosition === 'default'
                                        ? 4.2
                                        : 4,
                                slidesPerGroup: 1
                                // slidesOffsetBefore: 80,
                                // slidesOffsetAfter: 80
                            }
                        }}
                        onSwiper={(swiper) => {
                            setIsEnd(swiper.isEnd)
                        }}
                        lazy
                    >
                        {productSlides.map((prod, key) => (
                            <SwiperSlide key={key}>
                                <CarouselProductCategoryItem product={prod} />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
                {!(isStart && isEnd) && (
                    <div
                        className={cn(s['pagination-container'], {
                            [s['hide-pagination']]:
                                navigationButtonsPosition == 'default'
                        })}
                        ref={containerRef}
                    >
                        <div className="inline-block relative">
                            <div
                                ref={paginationRef}
                                className={`${formattedName}-pagination flex gap-4`}
                            />
                            <div className="dots-line" />
                            <div
                                ref={activeDotRef}
                                className={cn('active-animated-dot', {
                                    [s['default-dot']]:
                                        navigationButtonsPosition == 'default'
                                })}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default CarouselProductCategory
