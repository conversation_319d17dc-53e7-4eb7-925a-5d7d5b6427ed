import { default as CorsairImage } from '@corsairitshopify/corsair-image'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { Interface404ParsedEntries } from 'pages/404'
import s from './NotFound.module.scss'

const c = {
    background: `${s.background}`,
    copy: `${s.copy} font-tomorrow font-medium`,
    cta: `${s.cta} text-black inline-block uppercase text-center font-semibold`,
    notFound: `${s.notFound} relative text-center`,
    heading: `${s.heading} font-tomorrow text-center`,
    subheading: `${s.subheading} text-yellow text-base tracking-wider`,
    logo: `${s.logo} mx-auto my-0`,
    wrapper: `${s.wrapper} w-full text-white z-10 flex flex-col justify-center align-center absolute top-1/2 left-1/2`
}

export const Component404 = ({
    pageContent
}: Interface404ParsedEntries): JSX.Element | null => {
    const { t } = useTranslation(['common'])

    if (!pageContent) {
        return null
    }
    const {
        copy,
        ctaText,
        ctaUrl,
        cloudinaryDesktopBackgroundImage,
        heading,
        subheading,
        cloudinaryLogoImage,
        cloudinaryMobileBackgroundImage
    } = pageContent

    return (
        <div className={c.notFound}>
            <div className={c.background}>
                {cloudinaryDesktopBackgroundImage?.[0]?.secure_url && (
                    <div className="hidden lg:block">
                        <CorsairImage
                            keepOrigin
                            alt={correctImageAlt(
                                t(
                                    `alt|${cloudinaryDesktopBackgroundImage?.[0]?.context?.custom?.alt}`
                                )
                            )}
                            src={convertUrlFormat(
                                cloudinaryDesktopBackgroundImage?.[0]
                                    ?.secure_url
                            )}
                            layout="fill"
                            objectFit="cover"
                        />
                    </div>
                )}

                {cloudinaryMobileBackgroundImage?.[0]?.secure_url && (
                    <div className="block lg:hidden">
                        <CorsairImage
                            keepOrigin
                            alt={correctImageAlt(
                                t(
                                    `alt|${cloudinaryMobileBackgroundImage?.[0]?.context?.custom?.alt}`
                                )
                            )}
                            src={convertUrlFormat(
                                cloudinaryMobileBackgroundImage?.[0]?.secure_url
                            )}
                            layout="fill"
                            objectFit="cover"
                        />
                    </div>
                )}
            </div>

            <div className={c.wrapper}>
                {cloudinaryLogoImage?.[0]?.secure_url && (
                    <div className={c.logo}>
                        <CorsairImage
                            keepOrigin
                            alt={correctImageAlt(
                                t(
                                    `alt|${cloudinaryLogoImage[0].context?.custom?.alt}`
                                )
                            )}
                            src={convertUrlFormat(
                                cloudinaryLogoImage[0].secure_url
                            )}
                            layout="responsive"
                            width={134}
                            height={134}
                        />
                    </div>
                )}
                {subheading && (
                    <h2 className={c.subheading}>{t(subheading)}</h2>
                )}
                {heading && <h1 className={c.heading}>{t(heading)}</h1>}
                {copy && (
                    <p
                        className={c.copy}
                        dangerouslySetInnerHTML={{
                            __html: t(copy).replace(/\n/g, '<br />')
                        }}
                    />
                )}
                {ctaUrl && ctaText && (
                    <div className={c.cta}>
                        <Link href={ctaUrl}>{t(ctaText)}</Link>
                    </div>
                )}
            </div>
        </div>
    )
}
