.header {
    @apply m-auto text-white font-semibold text-center;
    max-width: 80vw;
    .subheading {
        @apply text-sm mb-0;
        color: #ece81a;
        letter-spacing: 0.05rem;
        line-height: 1rem;
        /*Mobile Query*/
        @media only screen and (max-width: 480px) {
            @apply text-xs;
        }
    }
    .heading {
        @apply text-6xl my-0;
        font-size: 3.75rem;
        letter-spacing: 0;
        /*Tablet Query*/
        @media only screen and (min-width: 481px) and (max-width: 768px) {
            font-size: 3rem;
        }
        /*Mobile Query*/
        @media only screen and (max-width: 480px) {
            font-size: 2.7rem;
        }
    }
}
.cta{
    @apply my-4;
    @screen md{
        margin: 30px 0;
    }
    & a {
        @apply inline-flex items-center;
    }
}

.imageTwoTile{
    padding-bottom: 10vh;
    @screen md{
        padding-bottom: 20vh;
    }
    &-subHeading{
        font-size: 1rem;
        letter-spacing: 0.2rem;

        @screen md {
            font-size: 14px;
        }
    }
    &-Description{
        letter-spacing: .0625rem;
    }

    &-BackgroundImage{
        margin: 0 auto;
    }

}

.Heading{
    letter-spacing: 0.05rem;
    font-size:30px;
    @screen lg{
        font-size: 4.5rem;
    }
}

.Background-Gradient{
    position: relative;
    background: linear-gradient(135deg, rgba(24, 46, 40, 0), #000 33%, #000 66%, transparent);
    background-attachment: fixed;
}

.has-animate {
    @apply opacity-0;
    transform: translateY(50px);
    transition: 250ms transform ease-in, 350ms opacity ease-in;

    &.onScreen {
        @apply opacity-100;
        transform: translateY(0);
    }
}
.Image-Col-two{
    &.has-animate-image{ 
        transition-property: transform;
        transition-duration: .3s;

        &.onScreen{            
            transform: translateY(5vh);
            transition: all .3s ease-out;

        }
    }
}

@screen sm {
    .imageTwoTile{
        max-width: 640px;
    }
}
@screen md {
    .imageTwoTile{
        max-width: 768px;
    }
    .imageTwoTile-Description{
        font-size: 1.75rem;
    }
    .imageTwoTile-imageContainer{
        .Image-Col-one,.Image-Col-two{
            flex:0 1 50%;
        }
        
    }
}
@screen lg {
    .imageTwoTile{
        max-width: 1024px;
        margin: 0 auto;
    }
}
@screen xl {
    .imageTwoTile{
        max-width: 1280px;
    }
}
@screen 2xl{
    .imageTwoTile{
        max-width: 1440px;
    }
}