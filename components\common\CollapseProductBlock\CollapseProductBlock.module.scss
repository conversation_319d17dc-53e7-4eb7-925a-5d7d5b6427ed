.collapse-product-block {
    margin: 2rem auto 4rem;
    max-width: 1760px;
    width: 90%;

    @media (max-width: 1366px) {
        padding: 0 2rem;
        width: 100%;
    }

    @media (max-width: 501px) {
        padding: 1rem;
        width: 100%;
    }

    &__header {
        @apply w-full flex flex-col md:flex-row items-start md:items-center justify-between;
        margin: 40px 0;
    }

    &__title {
        @apply font-tomorrow text-white uppercase;
        font-size: 48px;
        line-height: 48px;

        @screen md-max {
            font-size: 36px;
            line-height: 36px;
        }
    }

    &__cta {
        @apply text-yellow;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 1.2px;
        line-height: 30px;
        margin-top: 12px;

        &-arrow {
            svg {
                width: 20px;
                stroke-width: 3px;
                margin-bottom: 2px;
            }
        }
    }

    &__wrapper {
        grid-gap: 3rem;
        grid-template-columns: repeat(4, 1fr);

        @media (max-width: 850px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media (max-width: 480px) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    &__item {
        padding-bottom: 1px;
        border: 2px solid #333435;
        background: #272829;

        &-sku {
            @apply text-white uppercase md-max:px-5;
            font-size: 12px;
        }

        &-title {
            @apply font-semibold font-primary;
            font-size: 14px;
            line-height: 18px;
            color: #CCC;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            display: -webkit-box;
            overflow: hidden;
        }

        &-image {
            @apply p-4 lg:p-8;
            background: linear-gradient(135deg, #1B1B1B 0%, #000 100%);
            box-sizing: border-box;
            margin-bottom: 12px;

            img {
                height: 172px;
                width: 172px;
                margin: 0 auto
            }
        }
    }

    &__see-more {
        @apply text-white;
        background-color: #000;
        font-family: Aktiv Grotesk Bold, sans-serif;
        font-size: 16px;
        letter-spacing: 1.25px;
        margin-top: 48px;
        position: relative;
        text-align: center;
        text-transform: uppercase;
        width: 100%;

        @screen md-max {
            margin-top: 40px;
        }

        &:focus {
            outline: none;
        }

        &::before {
            content: '';
            background-color: white;
            height: 1px;
            left: 0;
            position: absolute;
            top: 40%;
            width: 100%;
            z-index: 1;
        }
    }

    // Global style for custom style pricing

    :global(.product-price) {
        @apply font-secondary flex text-2xl justify-start w-full md-max:px-5 md-max:mb-5;
        letter-spacing: 0.24px;
        line-height: 24px;
        margin-top: 16px;
        width: unset;
        flex-wrap: wrap;
        min-height: 48px;
    }

    @screen md-max {
        :global(.product-price) {
            padding: 0;
            min-height: 62px;
        }
    }

    :global(.compare-price) {
        @apply font-secondary relative inline-block line-through;
        color: #9D9D9D;
        font-size: 18px;
        letter-spacing: 0.18px;
        line-height: 24px;
    }

    :global(.discount-details) {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: column;
        align-items: baseline;

        @media (min-width: 767px) {
            flex-direction: row;
            align-items: center;
        }
    }

    :global(.discount-price) {
        color: #73D9E5;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        line-height: 13px;
        /* 92.857% */
        letter-spacing: 0.28px;
        float: right;
    }

    @media (max-width: 1899px) {
        :global(.discount-price-label) {
            display: none;
        }
    }

    @media (min-width: 1900px) {
        :global(.discount-price-minus) {
            display: none;
        }
    }

    &__add-to-cart-button {
        @apply text-yellow font-semibold self-start border-transparent bg-transparent hover:bg-transparent;
        font-size: 14px;
        padding: 0;
        line-height: 18px;
        letter-spacing: 1.4px;

        @media (max-width: 1368px) {
            font-size: 13px;
        }

        @media (max-width: 900px) {
            font-size: 12px;
        }

        @screen md-max {
            background-color: transparent !important;
            border: none;
        }

        &:hover {
            border-color: transparent;
            color: #ece81a !important;
        }

        @media (hover: hover) {
            &:hover {
                opacity: 50%;
            }
        }

        &:active {
            opacity: 50%;
            transform: scale(0.98);
        }

        &:focus {
            outline-offset: unset;
            outline: none;
        }
    }

    &__icon-wrapper {
        align-items: center;
        display: flex;
        margin-left: -6px;
        padding-right: 3px;

        svg {
            padding-right: 4px;
            transform: translateY(-2px);
        }
    }
}

.collapse-bundle-block {
    &__price {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    &__wrapper {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 4rem;

        @media (max-width: 850px) {
            grid-template-columns: repeat(1, 1fr);
            grid-gap: 3rem;
        }

        @screen md-max {
            grid-template-columns: repeat(1, 1fr);
            grid-gap: 2.5rem;
        }

        .shop-now-wrapper {
            font-size: 13.6px;
        }

        .shop-now-button {
            text-transform: uppercase;
            border: 0px solid;
            padding: 0.95rem 6.4rem;
            display: inline-block;
            background: #ece81a;
            color: #000;
            margin: 1em 0;
            white-space: normal !important;
            text-align: center;
            font-size: 14px;
            letter-spacing: 0.1em;
            line-height: 18px;
            text-transform: uppercase;
            font-weight: 600;

            @media (max-width: 1368px) {
                padding: 1rem 2.5rem;
            }

            @screen md-max {
                padding-bottom: 0.95rem;
                padding-top: 0.95rem;
                width: 100%;
                text-align: center;
            }
        }
    }

    &__item {
        width: 242px;

        &-title {
            @apply text-white overflow-y-hidden;
            font-size: 24px;
            line-height: 26px;
            font-weight: 500;
            height: 52px;
            max-width: 296px;

            @screen md-max {
                padding: 0 !important;
                font-size: 18px;
                line-height: 24px;
            }

            &-sku {
                @apply text-white;
                margin: 1rem auto;
                font-size: 12px;
                text-transform: uppercase;
            }
        }

        &-image {
            background-color: #262626;
            padding-top: 32px;
            padding-bottom: 32px;
            width: 232px;
            height: 232px;
            margin: 0 auto;
            margin-right: 32px;

            @screen md-max {
                max-height: 345px;
                width: 100%;
                height: 100%;
                margin-bottom: 20px;
                margin-right: 0;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }
}