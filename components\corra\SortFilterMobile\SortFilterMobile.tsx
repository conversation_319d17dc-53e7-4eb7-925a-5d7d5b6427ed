import CheckMarkIcon from '@components/icons/CheckMarkIcon'
import ChevronDown from '@components/icons/ChevronDown'
import FilterIcon from '@components/icons/FilterIcon'
import { CloseSVGIcon } from '@components/icons/VideoPlayer/CloseSVGIcon'
import { PLPManager } from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { SortFields } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { cloneDeep } from 'lodash'
import debounce from 'lodash.debounce'
import { useTranslation } from 'next-i18next'
import { FC, HTMLAttributes, useMemo, useState } from 'react'
import {
    ProductListFilter,
    ProductListFilterProps
} from '../ProductListFilter/ProductListFilter'
import { updatePlpManagerState } from '../ProductListFilter/util/transform'
import styles from './SortFilterMobile.module.scss'
import {
    ActiveFilterView,
    MobileFilterQueryParams
} from './SortFilterMobile.types'
import SortGroup from './components/SortGroup'

interface Props extends Pick<HTMLAttributes<HTMLDivElement>, 'className'> {
    plpManager: PLPManager
    activeViewType: ActiveFilterView
    updateFilterViewType: (nextView: ActiveFilterView) => void
    sortFields: SortFields
    isNonTransactional?: boolean
    mobileFilterProps: Omit<ProductListFilterProps, 'plpManager'>
}

export const SORT_FILTER_MOBILE_ID = 'sort-filter-mobile'

const SortFilterMobile: FC<Props> = ({
    plpManager,
    className,
    activeViewType,
    updateFilterViewType,
    sortFields,
    isNonTransactional,
    mobileFilterProps,
    ...props
}) => {
    const {
        plpState: { appliedFilterData, appliedSearchCritieria, filters },
        setSearchCriteria,
        setFilter,
        setFiltersVisible
    } = plpManager
    const { t } = useTranslation('plp')
    const [queryParams, setQueryParams] = useState<MobileFilterQueryParams>({
        appliedFilterData,
        appliedSearchCritieria,
        filters
    })

    const handleUpdateViewType = (nextView: ActiveFilterView) => () => {
        if (nextView === ActiveFilterView.None) {
            setFiltersVisible(false)
        }
        updateFilterViewType(
            nextView === activeViewType ? ActiveFilterView.None : nextView
        )
    }

    const btnContent = useMemo(() => {
        const defaultVal = { icon: null, text: '' }
        const output = {
            left: cloneDeep(defaultVal),
            right: cloneDeep(defaultVal)
        }
        const closeIcon = <CloseSVGIcon />
        const arrowDownIcon = <ChevronDown />
        const filterIcon = <FilterIcon />
        switch (activeViewType) {
            case ActiveFilterView.None:
                return output
            case ActiveFilterView.SortView:
                return {
                    left: { icon: closeIcon, text: t('Close Sort') },
                    right: { icon: arrowDownIcon, text: t('Sort by') }
                }
            case ActiveFilterView.FilterView:
                return {
                    left: { icon: closeIcon, text: t('Close Filters') },
                    right: { icon: filterIcon, text: t('Filters') }
                }
            default:
                return output
        }
    }, [activeViewType, t])

    const handleApplyFilter = () => {
        setSearchCriteria(queryParams.appliedSearchCritieria)
        setFilter({
            appliedFilterData: queryParams.appliedFilterData,
            filters: queryParams.filters
        })
        handleUpdateViewType(ActiveFilterView.None)()
    }

    const handleResetFilter = () => {
        updatePlpManagerState(plpManager, {
            appliedFilterData: [],
            appliedSearchCritieria: [],
            filters: {}
        })
        handleUpdateViewType(ActiveFilterView.None)()
    }

    const handleUpdateAppliedFilter = debounce(
        (newVal: MobileFilterQueryParams) => {
            setQueryParams(newVal)
        },
        100
    )

    return (
        <aside
            id={SORT_FILTER_MOBILE_ID}
            className={cn(styles.SortFilterMobileWrapper, className)}
            {...props}
        >
            <div className={styles.ButtonGroupSection}>
                <button
                    onClick={handleUpdateViewType(ActiveFilterView.None)}
                    aria-label={btnContent.left.text}
                >
                    {btnContent.left.text}
                </button>
                <button
                    aria-label={btnContent.right.text}
                    className={cn([
                        {
                            'flex-row-reverse':
                                btnContent.right.text !== t('Sort by')
                        }
                    ])}
                >
                    {btnContent.right.text}
                </button>
            </div>
            <div className={styles.MainContent}>
                {activeViewType === ActiveFilterView.SortView ? (
                    <SortGroup
                        plpManager={plpManager}
                        isNonTransactional={isNonTransactional}
                        sortFields={sortFields}
                        updateFilterViewType={handleUpdateViewType}
                    />
                ) : (
                    <ProductListFilter
                        {...mobileFilterProps}
                        plpManager={plpManager}
                        updateAppliedFilters={handleUpdateAppliedFilter}
                        appliedFilters={queryParams}
                        showFilterCount
                        showCount
                    />
                )}
            </div>
            {activeViewType === ActiveFilterView.FilterView && (
                <div className={styles.Footer}>
                    <button
                        className={cn(styles.ApplyBtn, 'scuf-button-primary')}
                        aria-label={t('Apply')}
                        onClick={handleApplyFilter}
                    >
                        <span>
                            {t('Apply')}{' '}
                            {queryParams.appliedFilterData.length > 0
                                ? `(${queryParams.appliedFilterData.length})`
                                : null}
                        </span>
                        <CheckMarkIcon
                            color="var(--secondary-off-black)"
                            width={16}
                            height={13}
                        />
                    </button>
                    <button
                        className={styles.ClearAllBtn}
                        aria-label={t('Clear All')}
                        onClick={handleResetFilter}
                    >
                        {t('Clear All')}
                    </button>
                </div>
            )}
        </aside>
    )
}

export default SortFilterMobile
