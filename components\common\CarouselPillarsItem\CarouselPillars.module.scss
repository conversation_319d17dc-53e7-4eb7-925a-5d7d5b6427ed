.slider-item-container {
    @apply flex flex-col;

    @screen xl-1280 {
        max-width: 429px;
        // height: 668px;
    }

    @media screen and (max-width: 1500px) {
        // height: 668px;
    }

    @screen md-max {
        // height: 644px;
    }

    .slider-item-text-block {
        padding: 40px 34px;

        @screen md-max {
            padding: 40px 24px;
        }

        .slider-item-heading {
            margin-bottom: 24px;
            font-size: 30px;
            line-height: 120%;
            letter-spacing: 0px;
            @apply font-primary font-medium align-middle;
        }

        .slider-item-display-text {
            font-size: 16px;
            line-height: 20.26px;
            @apply font-bold;
        }

        .slider-item-description {
            font-size: 16px;
            line-height: 150%;
            letter-spacing: 0;
            vertical-align: middle;
            @apply font-sofiaSans #{!important};
            @apply font-light;
        }
    }

    .slider-item-image-container {
        height: 344px;
        aspect-ratio: 1 / 1;
        @screen md-max {
            height: 307px;
        }
        @apply relative w-full;
    }
}
