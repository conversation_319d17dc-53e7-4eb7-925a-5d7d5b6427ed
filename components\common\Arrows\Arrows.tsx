/* eslint-disable i18next/no-literal-string */
import { useTranslation } from 'next-i18next'
import { ChevronLeft, ChevronRight } from 'react-feather'
import { CustomArrowProps } from 'react-slick'

interface ArrowProps extends CustomArrowProps {
    direction: 'left' | 'right'
    s?: any
}

const Arrows = ({ onClick, direction, s }: ArrowProps): JSX.Element | null => {
    const { t } = useTranslation(['common'])

    if (!s) return null

    return (
        // eslint-disable-next-line
        <>
            {direction === 'left' ? (
                <button
                    className={`${s['left-arrow']}`}
                    onClick={onClick}
                    aria-label={t('previous')}
                >
                    <ChevronLeft color="#fff" className={s['chevron']} />
                </button>
            ) : (
                <button
                    className={`${s['right-arrow']}`}
                    onClick={onClick}
                    aria-label={t('previous')}
                >
                    <ChevronRight color="#fff" className={s['chevron']} />
                </button>
            )}
        </>
    )
}

export default Arrows
