import { Aggregation, Maybe, AggregationOption } from '@pylot-data/fwrdschema'

export type AggregationValueList = {
    [key: string]: string[]
}

export const getAvailableAggregationValues = (
    aggregations?: Maybe<Array<Maybe<Aggregation>>>
): AggregationValueList => {
    if (!aggregations || aggregations.length === 0) {
        return {}
    }
    const valueMap: AggregationValueList = {}
    aggregations.forEach((item) => {
        if (item && item.attribute_code && Array.isArray(item.options)) {
            valueMap[
                item.attribute_code
            ] = item.options.map((option: Maybe<AggregationOption>) =>
                String(option?.value)
            )
        }
    })
    return valueMap
}
