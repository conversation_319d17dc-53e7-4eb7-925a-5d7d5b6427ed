.section-container {
    --padding-section-desktop: 16px;
    --padding-section-mobile: 10px;
    --tile-desktop-height: 686px;
    --tile-mobile-height: 493px;
    // max-width: 1920px;
    padding-inline: 16px;
    padding-block: var(--padding-section-desktop);

    @screen md-max {
        padding-inline: 0;
        padding-block: var(--padding-section-mobile);
    }

    @apply bg-secondaryOffBlack;
}

.section-heading {
    @apply text-5xl md:text-7xl text-white text-center mb-2;
}

.section-subheading {
    @apply text-xl md:text-2xl text-yellow text-center font-normal mb-4;
}

.section-copy {
    @apply text-lg text-white text-center px-4 mx-auto mb-4 md:mb-8 max-w-7xl;
}

.section-heading {
    @apply text-5xl md:text-7xl text-white text-center mb-2;
}

.section-subheading {
    @apply text-xl md:text-2xl text-yellow text-center font-normal mb-4;
}

.section-copy {
    @apply text-lg text-white text-center px-4 mx-auto mb-4 md:mb-8 max-w-7xl;
}

.bento-container {
    --item-gap: 8px;
    @apply relative mx-auto;
}

.bento-disclaimer-info {
    @apply block md:hidden;
    padding: 12px 20px 20px;
    color: #ffffff;
    font-size: 1rem;
    line-height: 1.5rem;

    p {
        @apply font-sofiaSans #{!important};
    }
}

.bento-additional-info {
    @apply hidden mx-auto w-fit md:block;
    padding: 40px 0 25px;
    color: #ffffff;
    font-size: 1rem;
    line-height: 1.5rem;

    p {
        @apply font-sofiaSans #{!important};
    }

    a,
    a:visited {
        color: var(--primary);
        text-decoration: none;
    }
}

.bento-content-item {
    background-color: transparent;
    @apply w-full flex-col mx-auto;
    max-width: 1920px;
    transition: 500ms cubic-bezier(0.75, 0, 0.45, 1);

    .bento-tile {
        @apply content-end;
        box-shadow: inset 0px -40px 50px #00000080;
    }

    &[data-content-position='bottom-left'] .bento-tile {
        @apply content-end;
    }

    &[data-content-position='top-left'] .bento-tile {
        @apply content-start;
    }

    &.top-center .bento-tile {
        @apply flex flex-col items-center text-center;
    }

    &[data-layout='2'] {
        .text-wrapper {
            @screen md-max {
                width: 100% !important;
            }
        }
    }

    &[data-layout='2-3'] {
        .text-wrapper {
            @screen md-max {
                width: min(61%, 195px) !important;
            }
        }
    }
}

.button-container {
    @apply flex justify-center gap-20 md:gap-8 mb-8 mx-4 md:mx-0;

    button {
        @apply font-medium pb-2 text-2xl md:text-4xl font-tomorrow;
        letter-spacing: 0.05rem;
        color: #a0a0a0;
        transition: 200ms;
        border-bottom: 2px solid transparent;
        line-height: 1.1;

        &.active {
            @apply text-white;
            border-bottom: 2px solid #ece81a;
        }

        &:hover,
        &:focus-visible {
            @apply text-white;
        }
    }
}

.bento-tiles {
    gap: var(--item-gap);
    transition: 500ms cubic-bezier(0.75, 0, 0.45, 1);
    @apply w-full text-white flex flex-col;

    .row {
        gap: var(--item-gap);
        grid-template-columns: repeat(
            var(--bento-column),
            minmax(0, var(--item-width))
        );
        @apply grid justify-center;
        @screen md-max {
            grid-template-columns: repeat(1, minmax(0, 1fr));
            gap: var(--item-gap);
        }
    }

    .bento-tile {
        --content-width: 100%;
        height: var(--tile-mobile-height);
        max-width: 100%;
        padding: 24px;
        background-color: rgba(239, 239, 239, 1);

        @screen md {
            height: var(--tile-desktop-height);
            padding: 48px 40px;
        }

        @apply relative overflow-hidden z-1;

        .text-wrapper {
            @apply relative z-1;
            width: var(--content-width);
            max-width: 100%;

            .tile-heading {
                font-size: 1.5rem;
                font-weight: 500;
                line-height: 120%;
                letter-spacing: 0px;
                margin-bottom: 16px;

                @screen md {
                    font-size: 2rem;
                    margin-bottom: 12px;
                }
                @apply font-primary text-white uppercase;
            }

            .tile-description {
                font-size: 1rem;
                font-weight: 400;
                font-family: 'Sofia Sans Semi Condensed' !important;
                line-height: 150%;
                letter-spacing: 0px;
                margin-bottom: 8px;

                @screen md {
                    font-size: 1.25rem;
                    margin-bottom: 24px;
                }
                @apply text-white;
            }

            .link-text {
                gap: 16px;
                font-size: 1rem;
                line-height: 1.5rem;
                letter-spacing: 0.25px;
                color: var(--primary);
                font-family: Tomorrow, sans-serif !important;
                @apply flex font-medium items-center;
            }
        }

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #00000033;
            background-image: var(--background-image);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-blend-mode: darken;
            transition: transform 750ms;
            z-index: 0;
        }

        &:hover,
        &:focus-visible {
            &::before {
                transform: scale(1.05);
            }
        }
    }
}
