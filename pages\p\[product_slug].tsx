/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Layout } from '@components/common'
import CompatibilityBlock from '@components/common/CompatibilityBlock/CompatibilityBlock'
import { ContentPage } from '@components/common/ContentPage/ContentPage'
import CrosssellCarousel from '@components/common/CrosssellCarousel'
import KlarnaWrapper from '@components/common/KlarnaWrapper/KlarnaWrapper'
import { ATC_HEADER_ID } from '@components/common/Layout/Layout'
import ProductAwards from '@components/common/ProductAwards'
import ProductCopyBlock from '@components/common/ProductCopyBlock'
import ProductFamily from '@components/common/ProductFamily'
import { ProductFeatures } from '@components/common/ProductFeatures/ProductFeatures'
import ProductGallery, {
    GalleryTypeEnum
} from '@components/common/ProductGallery/ProductGallery'
import ProductPromo from '@components/common/ProductPromo/ProductPromo'
import ProductsRelatedTiles from '@components/common/ProductsRelatedTiles'
import { IItemAdded } from '@components/common/ProductsRelatedTiles/ProductsRelatedTiles'
import { ShippingInfoProps } from '@components/common/ShippingInfo/ShippingInfo'
import { GigyaAccount } from '@components/common/SignInOptions/SignInOptions'
import { ETabVariant, filterEmptyTabs } from '@components/common/Tabs'
import type { TypeComponentsTab } from '@components/common/Tabs/ProductTabContent'
import { PromoCampaignModal } from '@components/common/ThirdPartyPromo/PromoCampaignModal'
import { PromoCampaignSection } from '@components/common/ThirdPartyPromo/PromoCampaignSection'
import type {
    IProductBlocks,
    IProductContentfulResponse,
    ITabItem,
    IThirdPartyPromoMessage,
    PreviewData
} from '@components/common/types'
import { VariantSelector } from '@components/common/VariantSelector'
import { AddToCart } from '@components/corra/AddToCart'
import { ProductInfo } from '@components/corra/ProductInfo'
import CustomizeIcon from '@components/icons/PDP/CustomizeIcon'
import { LoadingIndicator } from '@components/LoadingIndicator'
import { NotifyMeModal } from '@components/NotifyMeModal'
import { useStoreConfig } from '@config/hooks/useStoreConfig'
import {
    SocialDefaultContent,
    socialDefaultContent
} from '@config/seo/defaultContents'
import { Breadcrumbs } from '@corsairitshopify/corsair-breadcrumbs'

import { SEO } from '@corsairitshopify/corsair-seo'
import { useUser } from '@corsairitshopify/pylot-auth-manager'
import { useCart } from '@corsairitshopify/pylot-cart-manager/src/use-cart'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { GiftCardProduct } from '@corsairitshopify/pylot-product-giftcard'
import { usePdpTracking } from '@corsairitshopify/pylot-tag-manager'
import { useContentGroup } from '@corsairitshopify/pylot-tag-manager/src/TrackContentGroup'
import { getIsSignedIn } from '@corsairitshopify/pylot-tag-manager/src/TrackUrlChange/TrackUrlChange'
import { Button, Container } from '@corsairitshopify/pylot-ui'
import { deepValue } from '@corsairitshopify/pylot-utils'
import {
    getYotpoBottomLineApi,
    yotpoJsonLd
} from '@corsairitshopify/pylot-yotpo'
import { convertProductContentfulToContentPage } from '@lib/utils/ConvertProductContentfulToContentPage'
import {
    checkDuplicatePDPEvent,
    getCookie,
    mappingPdpData
} from '@lib/utils/dataLayer'
import {
    TEN_MINUTES_IN_SECONDS,
    TWO_MINUTES_IN_SECONDS
} from '@lib/utils/pageTtls'
import s from '@pagestyles/PDP.module.scss'
import getContentJson from '@pylot-data/api/operations/get-content-json'
import getProduct from '@pylot-data/api/operations/get-product'
import getStatusBackInStockNotification, {
    getStatusBackInStockNotificationResult
} from '@pylot-data/api/operations/get-status-backin-stock-ntf'
import { ClickFrom } from '@pylot-data/enums/ClickFromEnum.d'
import { ProductStockStatus } from '@pylot-data/enums/ProductStockStatus.d'
import { BackendStatus, PageType } from '@pylot-data/enums/VisibilityEnum'
import type { ProductInterface } from '@pylot-data/fwrdschema'
import { useContentJson } from '@pylot-data/hooks/contentful/use-content-json'
import { useProductFetch } from '@pylot-data/hooks/product/use-product-fetch'
import {
    ButtonLabel,
    ProductVariant,
    useProductUI
} from '@pylot-data/hooks/product/use-product-ui'
import {
    Products,
    useRelatedProductsFetch
} from '@pylot-data/hooks/product/use-related-products-fetch'
import { getMediaGalleryEntries } from '@pylot-data/hooks/product/utils/getMediaGalleryEntries'
import { useGigyaLogin } from '@pylot-data/hooks/sso-login/use-gigya-login'
import { useBusinessUnit } from '@pylot-data/hooks/use-business-unit'
import {
    shouldProductVisible,
    useVisibility
} from '@pylot-data/hooks/use-visibility'
import {
    CartItemPrices,
    ConfigurableProduct,
    ConfigurableVariant,
    PriceRange,
    ProductInterface as PylotProductInterface
} from '@pylot-data/pylotschema'
import { serverSideTranslations } from '@pylot-data/serverSideTranslations'
import cn from 'classnames'
import {
    DEFAULT_FORMAT_DATE_BY_REGION,
    getDeliveryByDate,
    isTransactionalView
} from 'helpers'
import { processBackorderData } from 'helpers/backorderHelper'
import { getPDPContentgroup } from 'helpers/categoriesHelper'
import { convertImageFormat } from 'helpers/cloudinaryHelper'
import { isAccessoryProduct } from 'helpers/productCategoryHelper'
import { useCheckScrollEnd } from 'hooks/useCheckScrollEnd'
import { LANGUAGE_CODES_MAP } from 'localesConfig'
import type {
    GetStaticPaths,
    GetStaticProps,
    GetStaticPropsContext,
    InferGetStaticPropsType
} from 'next'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { response } from 'pages/account/account-information'
import { createPortal } from 'preact/compat'
import React, {
    ReactElement,
    useCallback,
    useEffect,
    useMemo,
    useState
} from 'react'
const Tabs = dynamic(() => import('@components/common/Tabs/Tabs'))
const UnsubscribeNotifyMeModal = dynamic(
    import(
        '@components/common/UnsubscribeNotifyMeModal/UnsubcribeNotifyMeModal'
    )
)
const ProductContentCarousel = dynamic(
    () =>
        import(
            '@components/common/ProductContentCarousel/ProductContentCarousel'
        )
)
const ProductBlocksWrapper = dynamic(
    () => import('@components/common/ProductBlocks/ProductBlocksWrapper')
)
const FAQModuleCollection = dynamic(
    () => import('@components/common/FAQModule/FAQModuleCollection')
)
const FeaturedBundles = dynamic(
    import('@components/common/FeaturedBundles/FeaturedBundles')
)
const CarouselHow = dynamic(
    import('@components/common/CarouselHow/CarouselHow')
)
const VerticalGraphDoubleBars = dynamic(
    import('@components/common/Graphs/VerticalGraphDoubleBars')
)
const HorizontalGraphSingleBars = dynamic(
    import('@components/common/Graphs/HorizontalGraphSingleBars')
)
const VerticalGraphSingleBars = dynamic(
    import('@components/common/Graphs/VerticalGraphSingleBars')
)
const BannerIntereactiveCallout = dynamic(
    import('@components/common/BannerIntereactiveCallout')
)
const TwotilewithCalloutMedia = dynamic(
    import('@components/common/TwotilewithCallout')
)
const ImageTwoTile = dynamic(import('@components/common/ImageTwoTile'))
const HorizontalLightingGallery = dynamic(
    import('@components/common/HorizontalLightingGallery')
)
const VerticalLightingGallery = dynamic(
    import('@components/common/VerticalLightingGallery')
)
const NotFound = dynamic(
    () => import('@components/404').then((mod) => mod.NotFound) as any
)

const SubstituteItem = dynamic(import('@components/common/SubstituteItem'))

const attachCorsairBehaviors = () => {
    if (window.Corsair?.behaviors) {
        Object.values(window.Corsair.behaviors)?.forEach((b) => b.attach())
    }
}

export const PRODUCT_CONTENT_TYPE = 'product'
export const BDS_WAIT_CLEAR_CACHE = 'BDS'
export const DEFAULT_USD_THRESHOLD = 79

export const getStaticPaths: GetStaticPaths = async () => {
    // Disabled preloading
    return {
        paths: [],
        fallback: 'blocking'
    }
}

export const grabAllSkus = (skusList: Array<Array<string>>): Array<string> => {
    return skusList?.flat(2)?.filter((sku) => sku) || []
}

export const isMemorySkuAndSetUpDeliveryBy = (product: any) => {
    if (!product) return false
    const { categories, deliverBy } = product
    const isMemoryProduct = categories?.some(
        (category: any) => category?.url_path === 'memory'
    )
    return isMemoryProduct && deliverBy
}

export const getProductsBySkus = (
    products: Products[],
    skusList: Array<string[] | null>
): Array<Products[]> =>
    skusList.map((skus) =>
        products?.filter((product) => skus?.includes(product?.productSku))
    )

const REGION_UNAVAILABLE_HREFLANG = ['WW', 'EU', 'LM']
const rel = 'alternate'
export const getStaticProps: GetStaticProps = async ({
    locale,
    params,
    preview,
    previewData
}: GetStaticPropsContext) => {
    const productUrl = (params!.product_slug as string).replace('.html', '')
    const previewDate = previewData as PreviewData

    try {
        const product = await getProduct({
            url_key: params!.product_slug as string,
            locale: locale || '',
            isPreview: preview,
            previewDate: previewDate?.time
        })

        if (!product || !product?.productDetail?.items?.length) {
            return preview
                ? { props: {} }
                : {
                      notFound: true,
                      revalidate: TWO_MINUTES_IN_SECONDS
                  }
        }

        const productDetailItem = product?.productDetail?.items[0]

        const variantIndex =
            (productDetailItem?.variants as ConfigurableVariant[])?.findIndex(
                (variant) =>
                    variant!.product!.sku?.toLowerCase() ===
                    productDetailItem?.sku?.toLowerCase()
            ) ?? ProductVariant.NOT_SELECTED

        const mediaGalleryEntries = getMediaGalleryEntries(
            productDetailItem,
            variantIndex
        )

        const productSku = product?.productDetail?.items[0].sku as string
        const { data: productContentful } = await getContentJson<
            IProductContentfulResponse<TypeComponentsTab>
        >({
            queryVariables: {
                identifier: [productSku],
                contentType: PRODUCT_CONTENT_TYPE,
                options: {
                    queryField: 'sku',
                    level: 6
                }
            },
            locale: locale
        })

        return {
            props: {
                url_key: productUrl,
                productData: product,
                mediaGalleryEntries,
                productDataContentful: {
                    ...productContentful
                },
                ...(await serverSideTranslations(locale!, [
                    'common',
                    'pdp',
                    'cms'
                ]))
            },
            revalidate: TEN_MINUTES_IN_SECONDS
        }
    } catch (e) {
        console.log('PDP GETSTATICPROPS ERROR')
        console.log(e)
        return {
            props: {
                url_key: productUrl,
                ...(await serverSideTranslations(locale!, ['common']))
            },
            revalidate: TWO_MINUTES_IN_SECONDS
        }
    }
}

export default function PDP({
    productData,
    productDataContentful,
    mediaGalleryEntries = []
}: InferGetStaticPropsType<typeof getStaticProps>): ReactElement {
    if (!productData) {
        return <LoadingIndicator />
    }
    const { isScrollEndOfBlock: endOfHeadInfoContainer } = useCheckScrollEnd(
        '#container-classes'
    )
    const [isNotifyMeModalOpen, setIsNotifyMeModalOpen] = useState(false)
    const [isUnsubscribeMeModalOpen, setIsUnsubscribeMeModalOpen] = useState(
        false
    )
    const [isApplyPromoCampaign, setApplyPromoCampaign] = useState<boolean>(
        false
    )
    const [
        isNtfExist,
        setIsNtfExist
    ] = useState<getStatusBackInStockNotificationResult>({
        success: false,
        exist: false,
        isSendEmail: false,
        message: ''
    })
    const router = useRouter()
    const { t } = useTranslation(['common', 'pdp'])
    const { data: dataCart } = useCart()
    const [priceBundle, setPriceBundle] = useState<
        PriceRange | CartItemPrices
    >()
    const [relatedProductsData, setRelatedProductsData] = useState<
        PylotProductInterface[]
    >([])
    const [itemAdded, setItemAdded] = useState<IItemAdded[]>([])
    const [indexItemSelecting, setIndexItemSelecting] = useState<number>()
    const [isDisable, setIsDisable] = useState<boolean>(false)
    const [isChecked, setIsChecked] = useState<boolean>(false)
    const url_key = router.query.product_slug as string
    const category_slug =
        productData?.productDetail?.items[0].categories[0].url_path
    const { locale, asPath } = router
    const queryID = productData?.productDetail?.queryID
    const { customer } = useUser()
    const [productTabs, setproductTabs] = useState<boolean>(true)
    const { handleChangeContentGroup } = useContentGroup()!
    const currentLang = locale?.split('-')[0] || ''
    const currentRegion = locale?.split('-')[1] || ''
    let path = asPath
    if (path.includes('?')) {
        path = path.substring(0, path.indexOf('?'))
    }

    if (path.endsWith('/')) {
        path = path.slice(0, -1)
    }
    const [itemsCrosselling, setItemsCrosselling] = useState<
        PylotProductInterface[]
    >([])
    const [resetItemsCrossell, setResetItemsCrossell] = useState<boolean>(false)
    const [priceCrosselling, setPriceCrosselling] = useState<
        PriceRange | CartItemPrices
    >()

    let shopifyId = ''
    if (customer && customer?.id) {
        shopifyId = String(customer?.id)
            ?.split('/')[4]
            ?.replace(/[^\w\s]/gi, '')
    }
    const {
        base: { regionMapLanguages, url, dateFormatByRegion, freeShipping }
    } = useStoreConfig()
    const origin = url.baseUrl

    useEffect(() => {
        if (!document.getElementById('tabsParent')) {
            setproductTabs(false)
        }
    }, [])

    useEffect(() => {
        router.events.on('routeChangeComplete', attachCorsairBehaviors)
        return () => {
            router.events.off('routeChangeComplete', attachCorsairBehaviors)
        }
    }, [])

    useEffect(() => {
        const memoryProductClicked = window.localStorage.getItem(
            'productMemoryClicked'
        )
        if (memoryProductClicked) {
            pushToDataLayer(JSON.parse(memoryProductClicked))
            window.localStorage.removeItem('productMemoryClicked')
        }
        return () => {
            window.localStorage.removeItem('productMemoryClicked')
        }
    }, [])
    const { deliverBy, excludedDeliveryDate, sku: productSku } =
        productData.productDetail.items?.[0] || {}
    const { data } = useContentJson<
        IProductContentfulResponse<TypeComponentsTab>
    >(
        {
            identifier: [productSku],
            contentType: PRODUCT_CONTENT_TYPE,
            options: {
                queryField: 'sku',
                level: 5
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            initialData: { data: productDataContentful }
        }
    )
    const productContentful = data
        ? data.find(
              (item) =>
                  item.identifier.toUpperCase() === productSku.toUpperCase()
          )?.parsedEntries
        : null

    const {
        product,
        isSupportedProductType,
        isConfig,
        isGiftCard,
        isValidating,
        error
    } = useProductFetch(
        { productUrl: url_key },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            initialData: { data: productData }
        }
    )
    const productSKU = product?.sku || ''
    const { businessUnit, dataMegaMenu } = useBusinessUnit(
        product?.categories[0]?.url_path,
        data,
        product?.category_hierarchies,
        category_slug
    )
    const contentGroup = useMemo(() => {
        return product?.sku && businessUnit
            ? {
                  contentGroup1: 'Corsair',
                  contentGroup2: 'PDP',
                  contentGroup3: businessUnit,
                  contentGroup4: product?.categories[0]?.name
              }
            : null
    }, [product, businessUnit])

    useEffect(() => {
        const isDuplicatedPDPEvent = checkDuplicatePDPEvent(productSKU)
        const { contentGroup3, contentGroup4 } = getPDPContentgroup(
            category_slug,
            product?.category_hierarchies
        )
        if (businessUnit && !isDuplicatedPDPEvent) {
            handleChangeContentGroup({
                contentGroup2: 'PDP',
                contentGroup3,
                contentGroup4
            })
        }
        return () => {
            handleChangeContentGroup(null)
        }
    }, [
        businessUnit,
        category_slug,
        product.categories,
        product?.category_hierarchies,
        productSKU
    ])

    const review = getYotpoBottomLineApi({
        id: product?.id
    })
    const isAccessory = useMemo(() => isAccessoryProduct(product?.categories), [
        product?.categories
    ])
    const { isSignedIn } = useUser()
    const isLoggedIn = getIsSignedIn()
    const { gigyaLogin } = useGigyaLogin()

    const initializeGigya = (screen: string) => {
        let eventAccountVerification = { event: '', accountVerification: {} }
        return gigya?.accounts?.showScreenSet({
            screenSet: 'Default-RegistrationLogin',
            startScreen: `gigya-${screen}-screen`,
            onAfterScreenLoad: (eventObj: any) => {
                if (eventObj?.currentScreen === 'gigya-login-screen') {
                    pushToDataLayer({
                        event: 'accountLoginOpen'
                    })
                }
                if (eventObj?.currentScreen === 'gigya-register-screen') {
                    pushToDataLayer({
                        event: 'accountRegistrationOpen'
                    })
                }
            },
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onAfterSubmit: (eventObj: any) => {
                const eventAccountLogin = {
                    event: 'accountLogin',
                    accountLogin: {
                        customerEmail: eventObj?.response?.profile?.email, //email from successful login
                        customerId: eventObj?.response?.user?.UID //id from successful login
                    }
                }
                eventAccountVerification = {
                    event: 'accountVerification',
                    accountVerification: {
                        customerEmail: eventObj?.response?.profile?.email, //email from successful login
                        customerId: eventObj?.response?.UID, //id from successful login
                        firstName: eventObj?.response?.user?.firstName
                            ? eventObj?.response?.user?.firstName
                            : eventObj?.response?.userInfo?.firstName,
                        lastName: eventObj?.response?.user?.lastName
                            ? eventObj?.response?.user?.lastName
                            : eventObj?.response?.userInfo?.lastName,
                        country: eventObj?.profile?.country,
                        isVerified: eventObj?.response?.isVerified
                            ? eventObj?.response?.isVerified
                            : eventObj.response.status.toLowerCase() ===
                              response.STATUS
                            ? true
                            : false
                    }
                }
                if (eventObj?.form === 'gigya-login-form') {
                    if (
                        eventObj.response.status.toLowerCase() ===
                        response.STATUS
                    ) {
                        pushToDataLayer(eventAccountVerification)
                        pushToDataLayer(eventAccountLogin)
                    }
                }
                if (eventObj?.form === 'gigya-register-form') {
                    pushToDataLayer({
                        event: 'accountRegistration',
                        accountRegistration: {
                            customerEmail: eventObj?.response?.profile?.email, //email from successful account creation
                            customerID: eventObj?.response?.userInfo?.UID,
                            firstName: eventObj?.response?.userInfo?.firstName,
                            lastName: eventObj?.response?.userInfo?.lastName,
                            country: eventObj?.profile?.country
                        }
                    })
                }
                if (
                    eventObj?.subscriptions?.marketing?.optin?.email
                        ?.isSubscribed &&
                    eventObj?.form === 'gigya-register-form'
                ) {
                    pushToDataLayer({
                        event: 'newsletterSignup',
                        newsletterSignup: {
                            customerEmail: eventObj?.response?.profile?.email,
                            registrationLocation: 'account registration',
                            marketingConsent: true,
                            location: {
                                siteLanguage: locale,
                                siteCountry: locale?.split('-')[1],
                                userLanguage:
                                    typeof window !== 'undefined'
                                        ? window.navigator.language
                                        : '',
                                userCountry: getCookie('Country_Code')
                            }
                        }
                    })
                }
                if (eventObj?.form === 'gigya-otp-update-form') {
                    if (eventObj?.response?.isVerified) {
                        pushToDataLayer(eventAccountVerification)
                        pushToDataLayer(eventAccountLogin)
                    }
                }
            },
            onError: (eventObj: any) => {
                if (
                    eventObj?.response?.info?.form === 'gigya-otp-update-form'
                ) {
                    pushToDataLayer({
                        ...eventAccountVerification,
                        accountVerification: {
                            ...eventAccountVerification.accountVerification,
                            isVerified: false
                        }
                    })
                }
            },
            onAfterValidation: (eventObj: any) => {
                if (
                    eventObj?.form === 'gigya-otp-update-form' &&
                    eventObj?.formData.code === ''
                ) {
                    pushToDataLayer({
                        ...eventAccountVerification,
                        accountVerification: {
                            ...eventAccountVerification.accountVerification,
                            isVerified: false
                        }
                    })
                    return
                }
            }
        })
    }

    const addGigyaEventHandlers = () => {
        return gigya?.accounts?.addEventHandlers({
            onLogin: async (account: GigyaAccount) => {
                const { UID: uid, profile } = account
                const { email } = profile

                const user = { uid, email }

                await gigyaLogin(user)
            },
            onError: (error: string) => {
                console.error({ error })
            }
        })
    }
    const handleGigyaClick = (screen: string) => {
        const gigyaScriptTag = document.getElementById('gigya')

        if (!gigyaScriptTag) return

        initializeGigya(screen)
        addGigyaEventHandlers()
    }

    const outOfStockHandler = async () => {
        if (!isSignedIn) {
            handleGigyaClick('login')
        } else {
            const { isSendEmail, exist } = isNtfExist
            if (!isSendEmail && exist) setIsUnsubscribeMeModalOpen(true)
            else setIsNotifyMeModalOpen(true)
        }
    }

    useEffect(() => {
        ;(async () => {
            if (isSignedIn) {
                const localeArr = locale?.split('-')
                await getStatusBackInStockNotification({
                    customerId: localStorage.getItem('gigya_uid') || '',
                    email: customer?.email || '',
                    sku: sku,
                    region: localeArr?.[1] || ''
                }).then((res: any) => {
                    setIsNtfExist(res.data?.getStatusBackInStockNotification)
                })
            }
        })()
    }, [isSignedIn, isNotifyMeModalOpen, isUnsubscribeMeModalOpen])

    const {
        setSelectedOptions,
        selectedOptions,
        options,
        variant,
        buttonLabel: buttonLabelWithoutAdding,
        isButtonDisabled,
        selectedOptionUIDs,
        isOutOfStock
    } = useProductUI(product, {
        preselectedOptions: {},
        ...(product?.backend_status &&
            BackendStatus.pilot.includes(
                product?.backend_status?.toLowerCase()
            ) &&
            product?.inventory <= 0 &&
            product?.backorder?.available && {
                buttonActionLabel: ButtonLabel.PRE_ORDER
            }),
        sku: productData?.productDetail?.items[0].sku
    })

    useEffect(() => {
        //handle datalayer for PDP event
        const gigyaID = localStorage.getItem('gigya_uid')
        const customer = dataCart?.data?.cart?.email || ''
        const getUserLanguage = window.navigator.language
        const userLanguage = (getUserLanguage && getUserLanguage.includes('-')
            ? getUserLanguage.split('-')[0]
            : getUserLanguage
        ).toLowerCase()
        if (
            product &&
            !isValidating &&
            contentGroup &&
            url_key &&
            ((isLoggedIn && dataCart?.data?.cart?.id) || !isLoggedIn) &&
            !checkDuplicatePDPEvent(product?.sku)
        ) {
            pushToDataLayer({
                ...mappingPdpData(product),
                contentGroup,
                location: {
                    siteLanguage: locale?.split('-')[0].toLowerCase(),
                    siteCountry: locale?.split('-')[1].toLowerCase(),
                    userLanguage: userLanguage,
                    userCountry: getCookie('Country_Code')?.toLowerCase()
                },
                userLogState: {
                    customerId: isLoggedIn ? gigyaID : '',
                    customerLoggedIn: !!isLoggedIn,
                    customerEmail: isLoggedIn ? customer : '',
                    shopifyId: isLoggedIn && shopifyId ? shopifyId : '',
                    shopifyEmail: isLoggedIn ? customer : ''
                }
            })
        }
    }, [
        isValidating,
        contentGroup,
        product,
        url_key,
        dataCart?.data?.cart?.id,
        customer
    ])

    usePdpTracking(product, isValidating)

    //route fallback
    if (router.isFallback) {
        return (
            <Container clean className={s['container-classes']}>
                <div>
                    {t('Page for this product was not generated beforehand.')}
                    <br />
                    {t('Loading...')}
                </div>
            </Container>
        )
    }

    if (!product) {
        return <NotFound />
    }

    //Error handler for PDP page
    if (!product && error) {
        return (
            <Container clean className={s['container-classes']}>
                <h1>{t('Error!!!')}</h1>
            </Container>
        )
    }

    //Warning if the product type is not supported
    if (!isSupportedProductType) {
        return (
            <Container clean className={s['container-classes']}>
                <h1>{`${product?.__typename} ${t('is not supported')}`}</h1>
            </Container>
        )
    }
    const handleWaitClearCache = (nameClear: string): boolean => {
        if (nameClear === BDS_WAIT_CLEAR_CACHE) return true
        else return false
    }

    const {
        name,
        description,
        price_range: productPriceRange,
        badge,
        cross_sell_skus,
        show_variants,
        not_sellable,
        bundle_products,
        backorder,
        backend_status,
        bundle_and_save_skus,
        bundle_and_save_informations,
        substitute_item: substituteItem,
        image,
        categories,
        featured_bundles_skus,
        promo_campaigns,
        related_accessories_skus,
        edgekv
    } = product

    const edgekvMap: any[] = []
    const edgekvRegionAvailables: string[] = []
    try {
        const edgekvDB = JSON.parse(edgekv)
        if (Array.isArray(edgekvDB)) {
            edgekvMap.push(...edgekvDB)
            edgekvDB.forEach((value: any) => {
                if (value?.available_countries) {
                    value?.available_countries.forEach((region: string) => {
                        if (!edgekvRegionAvailables.includes(region)) {
                            edgekvRegionAvailables.push(region)
                        }
                    })
                }
            })
        }
    } catch (error) {
        console.warn(`EdgeKV not exist or invalid JSON: `, error)
    }

    const findSKUFromEdgeKVMap = (region: string) => {
        const skus = edgekvMap.filter((item) =>
            item?.available_countries?.includes(region.toUpperCase())
        )
        return skus.length ? skus[0] : null
    }

    const {
        sku,
        price_range: variantPriceRange,
        name: variantName,
        badge: variantBadge
    } = variant
    const [
        transformBundleAndSaveInformations,
        setTransformBundleAndSaveInformations
    ] = useState<any>(bundle_and_save_informations)

    const skus: string[] | null | undefined = bundle_and_save_informations?.map(
        (bundle: any) => bundle?.bundle_item_sku
    )

    const allSkus = [
        skus,
        featured_bundles_skus,
        cross_sell_skus,
        related_accessories_skus
    ]

    const productsData = useRelatedProductsFetch(
        grabAllSkus(allSkus),
        null,
        locale || ''
    )

    const [
        bundle_product_data,
        featured_bundles_product_data,
        cross_sell_skus_data,
        related_accessories_data
    ] = getProductsBySkus(productsData, allSkus)

    const productsBundle = bundle_product_data?.filter((productBundle) => {
        const productData = productBundle?.productData[0]
        const visibleProduct = shouldProductVisible(productData)
        return (
            productData?.stock_status !== ProductStockStatus.OutOfStock &&
            visibleProduct
        )
    })

    const productsFeaturedBundles = featured_bundles_product_data?.filter(
        (itemProduct) => {
            const productData = itemProduct?.productData[0]
            const visibleProduct = shouldProductVisible(productData)
            return (
                !!productData?.bundle_products?.length &&
                productData?.stock_status !== ProductStockStatus.OutOfStock &&
                visibleProduct
            )
        }
    )

    const data3rdPartyPromo = (promo_campaigns || []).length
        ? useContentJson<IThirdPartyPromoMessage>(
              {
                  identifier: [(promo_campaigns || [])[0]?.campaign],
                  contentType: '3rdPartyPromoConfiguration',
                  options: {
                      queryField: 'campaignName',
                      level: 2
                  }
              },
              {
                  revalidateOnFocus: false,
                  revalidateOnMount: true
              }
          )
        : undefined

    const dataThirdPartyPromoMessage = data3rdPartyPromo?.data
        ? data3rdPartyPromo?.data[0]?.parsedEntries
        : undefined

    const displayCopyBlock = productContentful?.copyBlock?.text
        ? productContentful?.copyBlock?.text
        : undefined

    const getRelatedProductsData = useCallback(() => {
        if (!productsBundle) return []
        const productsData: PylotProductInterface[] = []
        const productsDataIgnoreItemCart: PylotProductInterface[] = []
        const productsDataRaw: PylotProductInterface[] = []
        const transform_bundle_and_save_informations: any[] = []
        // @ts-ignore
        productsBundle?.forEach((productData: any) => {
            const index = productData?.productData?.findIndex(
                (product: any, i: number) =>
                    product?.sku === productData.productData[i]?.sku
            )
            const matchBundleAndSaveSku: any = bundle_and_save_informations.find(
                (bundle: any) =>
                    bundle.bundle_item_sku ===
                    productData.productData[index].sku
            )
            const priceRange =
                productData.productData[index]?.price_range?.maximum_price
                    ?.final_price?.value ?? 0
            const priceMinus =
                (priceRange * matchBundleAndSaveSku.discount_percent) / 100
            const finalPrice = priceRange - Number(priceMinus)

            const originalProductData = productData?.productData?.[index]

            const canShowBundleAndSave =
                index != null &&
                matchBundleAndSaveSku &&
                originalProductData?.stock_status ===
                    ProductStockStatus.InStock &&
                originalProductData?.price_range?.maximum_price?.discount
                    ?.amount_off === 0 &&
                (product.price_range?.maximum_price?.discount?.amount_off ===
                    0 ||
                    matchBundleAndSaveSku?.stackable)

            if (canShowBundleAndSave) {
                transform_bundle_and_save_informations.push({
                    bundle_item_sku: matchBundleAndSaveSku.bundle_item_sku,
                    price: {
                        c:
                            productData.productData[index]?.price_range
                                ?.maximum_price?.final_price?.currency,
                        p:
                            productData.productData[index]?.price_range
                                ?.maximum_price?.final_price?.value ?? 0,
                        fp: finalPrice,
                        d: priceRange - finalPrice,
                        sp: false
                    },
                    discount_percent: matchBundleAndSaveSku.discount_percent,
                    stackable: matchBundleAndSaveSku.stackable
                })
                productsDataRaw.push(productData.productData[index])
            }
        })
        setTransformBundleAndSaveInformations(
            transform_bundle_and_save_informations
        )
        // loop and sort bundle item array bundle and save informations sections
        product?.bundle_and_save_informations?.filter((bundle: any) => {
            const matchBundle = productsDataRaw.find(
                (productDataRaw: ProductInterface) => {
                    if (productDataRaw?.sku === bundle.bundle_item_sku)
                        return productDataRaw
                }
            )
            if (matchBundle) {
                productsData.push(matchBundle)
            }
        })
        // check bundle item in cart => if true => ignore display on pdp page
        productsData.filter((product: PylotProductInterface) => {
            const matchItemCart = dataCart?.data?.cart?.items?.find(
                (item) => item?.product.sku === product?.sku
            )
            if (!matchItemCart) {
                productsDataIgnoreItemCart.push(product)
            }
        })
        const hasProductXInCart = dataCart?.data?.cart?.items!.some(
            (item: any) => item?.product?.sku === productSKU
        )!
        setIsDisable(
            hasProductXInCart &&
                productsDataIgnoreItemCart?.length < productsData?.length
        )
        return productsData
    }, [
        bundle_and_save_informations,
        bundle_product_data,
        dataCart?.data?.cart?.items,
        product?.bundle_and_save_informations
    ])
    useEffect(() => {
        if (!bundle_and_save_informations || !locale) return
        const productsData = getRelatedProductsData()
        setRelatedProductsData(productsData)
        setIndexItemSelecting(7)
        setItemAdded([])
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        bundle_and_save_informations,
        locale,
        router.query?.product_slug,
        dataCart?.data?.cart?.items?.length
    ])

    useEffect(() => {
        let priceIncrement = productPriceRange || variantPriceRange
        if (itemsCrosselling?.length) {
            itemsCrosselling.forEach((item) => {
                const priceItem = {
                    regular_price:
                        item?.price_range?.maximum_price?.regular_price
                            ?.value ||
                        item?.price_range?.minimum_price?.regular_price?.value,
                    final_price:
                        item?.price_range?.maximum_price?.final_price?.value ||
                        item?.price_range?.minimum_price?.final_price?.value
                }
                priceIncrement = handlePriceIncrement(
                    priceIncrement,
                    priceItem?.regular_price,
                    priceItem?.final_price
                )
            })
        }
        if (itemAdded?.length) {
            itemAdded.forEach((item) => {
                const priceBundlePrice = transformBundleAndSaveInformations.find(
                    (information: any) =>
                        information.bundle_item_sku ===
                        relatedProductsData[item.key ?? 0].sku
                )
                if (priceBundlePrice && priceIncrement) {
                    priceIncrement = handlePriceIncrement(
                        priceIncrement,
                        priceBundlePrice?.price?.p,
                        priceBundlePrice?.price?.fp
                    )
                }
            })
        }
        if (itemsCrosselling?.length) {
            setPriceBundle(undefined)
            setPriceCrosselling(priceIncrement)
        } else {
            setPriceCrosselling(undefined)
            setPriceBundle(priceIncrement)
        }
    }, [itemsCrosselling, itemAdded])

    const handlePriceIncrement = (
        priceIncrement: PriceRange,
        regularPrice: any,
        finalPrice: any
    ) => {
        return {
            ...priceIncrement,
            maximum_price: {
                ...priceIncrement.maximum_price,
                discount: {
                    __typename: 'ProductDiscount',
                    amount_off:
                        priceIncrement.maximum_price?.regular_price.value +
                        regularPrice -
                        (priceIncrement.maximum_price?.final_price.value +
                            finalPrice)
                },
                final_price: {
                    ...priceIncrement.maximum_price?.final_price,
                    value:
                        priceIncrement.maximum_price?.final_price.value +
                        finalPrice
                },
                regular_price: {
                    ...priceIncrement.maximum_price?.regular_price,
                    value:
                        priceIncrement.maximum_price?.regular_price.value +
                        regularPrice
                }
            },
            minimum_price: {
                ...priceIncrement.minimum_price,
                discount: {
                    __typename: 'ProductDiscount',
                    amount_off:
                        priceIncrement.minimum_price?.regular_price.value +
                        regularPrice -
                        (priceIncrement.minimum_price?.final_price.value +
                            finalPrice)
                },
                regular_price: {
                    ...priceIncrement.minimum_price?.regular_price,
                    value:
                        priceIncrement.minimum_price?.regular_price.value +
                        regularPrice
                },
                final_price: {
                    ...priceIncrement.minimum_price?.final_price,
                    value:
                        priceIncrement.minimum_price?.final_price.value +
                        finalPrice
                }
            }
        }
    }

    const productPrice = deepValue(variantPriceRange || productPriceRange, [
        'minimum_price',
        'final_price',
        'value'
    ])

    const { seoImage } = socialDefaultContent
    const socialContent: SocialDefaultContent = {
        ...socialDefaultContent,
        seoImage: productContentful?.socialDefaultImage || seoImage
    }

    // const expectedDate = backorder?.date
    //     ? new Date(backorder?.date).toLocaleDateString()
    //     : ''
    const { backorder_date_range, translation_entry } = processBackorderData(
        dateFormatByRegion ?? DEFAULT_FORMAT_DATE_BY_REGION,
        backorder,
        backend_status
    )

    const priceRange: PriceRange = variantPriceRange || productPriceRange
    const currentCurrency =
        priceRange?.minimum_price?.final_price?.currency?.toUpperCase() ?? 'USD'

    const parsedPrice = ((priceBundle as PriceRange) ?? priceRange)
        ?.minimum_price.regular_price?.value
    const currentFreeShippingThreshold = freeShipping?.find(
        ({ currency }) => currency === currentCurrency
    )
    const freeShippingThreshold = currentFreeShippingThreshold
        ? currentFreeShippingThreshold.amount ?? DEFAULT_USD_THRESHOLD
        : DEFAULT_USD_THRESHOLD
    const free_shipping = (parsedPrice ?? 0) > freeShippingThreshold

    const shippingInfo: ShippingInfoProps = useMemo(
        () => ({
            productCategories: categories,
            freeShipping: free_shipping,
            expectedDateRange: backorder_date_range,
            translationEntry: translation_entry,
            backorderEnabled:
                backorder?.available && !isMemorySkuAndSetUpDeliveryBy(product),
            isOutOfStock,
            isBundle: productData.productDetail.items[0].bundle_products.length
                ? true
                : false
        }),
        [
            backorder?.available,
            backorder_date_range,
            categories,
            free_shipping,
            isOutOfStock,
            productData.productDetail.items,
            translation_entry
        ]
    )
    let newStockStatus = ''
    if (
        product?.backend_status &&
        BackendStatus.pilot.includes(product?.backend_status?.toLowerCase()) &&
        product?.inventory <= 0 &&
        product?.backorder?.available
    ) {
        newStockStatus = 'preorder'
    } else if (
        product?.backend_status &&
        product?.inventory <= 0 &&
        product?.backorder?.available
    ) {
        newStockStatus = 'backorder'
    } else if (product?.inventory > 0) {
        newStockStatus = 'in-stock'
    } else {
        newStockStatus = 'out-of-stock'
    }

    const trueStockStatus = newStockStatus

    useEffect(() => {
        router.beforePopState(({ url, as, options }) => {
            const entries = data && data[0] ? JSON.parse(data[0].entries) : null
            let isMarkupContent = 0
            if (entries) {
                entries['tabs'].forEach((tab: ITabItem<TypeComponentsTab>) => {
                    if (tab?.['content']) {
                        tab?.['content'].forEach((ele: any) => {
                            if (ele?.['meta']['contentType'] === 'htmlMarkup') {
                                isMarkupContent += 1
                            }
                        })
                    }
                })
            }
            if (as?.includes('/p/') && isMarkupContent > 0) {
                router.reload()
                return false
            }
            return true
        })
        return () => {
            router.beforePopState(() => true)
        }
    }, [router])

    useEffect(() => {
        setItemsCrosselling([])
    }, [sku])

    const { stock_status, variants, pdp_cta, price } = useVisibility({
        page_type: PageType.PDP,
        product
    })

    const handleAddMoreItem = (
        product: PylotProductInterface,
        checked: boolean
    ) => {
        if (checked) {
            setItemsCrosselling([...itemsCrosselling, product])
        } else {
            const newItemCrosselling = itemsCrosselling.filter(
                (item) => item?.sku !== product?.sku
            )
            setItemsCrosselling(newItemCrosselling)
        }
    }

    const renderAddToCartBtn =
        isTransactionalView('addToCart', locale, customer) &&
        pdp_cta.visible ? (
            <AddToCart
                relatedProductsData={relatedProductsData}
                itemAdded={itemAdded}
                priceRange={variantPriceRange || productPriceRange}
                isButtonDisabled={isButtonDisabled}
                product={{
                    ...product,
                    promo_apply_product: isApplyPromoCampaign
                        ? promo_campaigns?.[0]
                        : undefined,
                    promo_message: isApplyPromoCampaign
                        ? dataThirdPartyPromoMessage?.message
                        : ''
                }}
                buttonLabel={buttonLabelWithoutAdding}
                variant={variant}
                selectedOptionUIDs={selectedOptionUIDs}
                isGiftCard={isGiftCard}
                queryID={queryID}
                priceBundle={
                    priceBundle ?? (variantPriceRange || productPriceRange)
                }
                isOutOfStockHandler={outOfStockHandler}
                isOutOfStock={isOutOfStock}
                isNtfExist={isNtfExist}
                productTabsVisible={productTabs}
                itemCrosselling={itemsCrosselling}
                priceCrosselling={priceCrosselling}
                clickFrom={ClickFrom.PDP}
                setItemsCrosselling={setItemsCrosselling}
                setResetItemsCrossell={setResetItemsCrossell}
                showPrice
            />
        ) : null

    const renderFeaturedBundles = productsFeaturedBundles?.length ? (
        <FeaturedBundles
            title={productContentful?.featuredBundlesTitle}
            description={t(
                'pdp||featuredbundles|Exclusive bundles from CORSAIR will save you money.'
            )}
            ctaButtonText={t('pdp||featuredbundles|View All Bundles')}
            featuredBundlesProductData={productsFeaturedBundles}
        />
    ) : null

    const deliveryByDate = getDeliveryByDate({
        deliverBy,
        excludedDeliveryDate,
        sku: productSku,
        dateFormatByRegion
    })

    const showDeliveryByDate =
        deliveryByDate &&
        (!backorder?.available || isMemorySkuAndSetUpDeliveryBy(product)) &&
        pdp_cta.visible

    const variantsSelector = useMemo(() => {
        return !isGiftCard ? (
            isConfig && (show_variants || variants.visible) && (
                <VariantSelector
                    categories={product?.categories}
                    notSellable={not_sellable}
                    disableAll={
                        (product as ConfigurableProduct).stock_status ===
                        ProductStockStatus.OutOfStock
                    }
                    variants={
                        (product?.variants || []) as ConfigurableVariant[]
                    }
                    options={options}
                    selectedOptions={selectedOptions}
                    setSelectedOptions={setSelectedOptions}
                    variant={variant}
                    isAccessory={isAccessory}
                />
            )
        ) : (
            <GiftCardProduct product={product} />
        )
    }, [
        isGiftCard,
        isConfig,
        show_variants,
        variants,
        product,
        not_sellable,
        options,
        selectedOptions,
        isAccessory
    ])

    const productDetails = React.useMemo(() => {
        return (
            <section id="productDetails" className={s['product-main']}>
                <h1 className={s['product-name']}>{name}</h1>
                {isAccessory && variantsSelector}
                <div className={s['product-main-content-wrapper']}>
                    <div className={s['product-details']}>
                        <div className={s['product-topinfo']}>
                            <div className={s['product-info']}>
                                <ProductInfo
                                    description={{
                                        html: productContentful?.longDescription
                                    }}
                                    bundles={bundle_products}
                                    promotionMessage={
                                        productContentful?.promotionMessage
                                    }
                                />
                            </div>
                            <div className={s.compatibility}>
                                <CompatibilityBlock
                                    compatibility={
                                        productContentful?.compatibility
                                    }
                                />
                            </div>

                            <ProductPromo
                                message={
                                    productContentful?.promoMessage?.content
                                }
                            />
                        </div>
                        {!isAccessory && variantsSelector}

                        {dataThirdPartyPromoMessage && (
                            <PromoCampaignSection
                                dataThirdPartyPromoMessage={
                                    dataThirdPartyPromoMessage
                                }
                                setApplyPromoCampaign={setApplyPromoCampaign}
                            />
                        )}
                        {displayCopyBlock && (
                            <ProductCopyBlock
                                image={productContentful?.copyBlock?.image}
                                heading={productContentful?.copyBlock?.heading}
                                text={productContentful?.copyBlock?.text}
                                fontColorHex={
                                    productContentful?.copyBlock?.fontColorHex
                                }
                                cta={productContentful?.copyBlock?.cta}
                            />
                        )}

                        <ProductAwards awards={productContentful?.awards} />
                        {substituteItem &&
                            substituteItem !== 'NO REPLACEMENT' && (
                                <SubstituteItem
                                    substituteSKU={substituteItem}
                                />
                            )}
                    </div>
                    {!!productsFeaturedBundles?.length && (
                        <div
                            id="featured-bundles-wrapper-mobile"
                            className={s['featured-bundles-wrapper-mobile']}
                        >
                            {renderFeaturedBundles}
                        </div>
                    )}
                    <div
                        id="add-to-cart-placeholder-desktop"
                        className={s['add-to-cart-placeholder']}
                    />
                    {isTransactionalView('bundleAndSave', locale, customer) &&
                    transformBundleAndSaveInformations &&
                    (product?.price_range?.minimum_price.discount
                        ?.amount_off === 0 ||
                        product?.bundle_and_save_informations.some(
                            (b: any) => b.stackable
                        )) &&
                    handleWaitClearCache(BDS_WAIT_CLEAR_CACHE) &&
                    !isOutOfStock &&
                    transformBundleAndSaveInformations?.length &&
                    !isDisable ? (
                        <ProductsRelatedTiles
                            isDisable={isDisable}
                            setIsDisable={setIsDisable}
                            isChecked={isChecked}
                            setIsChecked={setIsChecked}
                            product={product}
                            bundle_and_save_informations={
                                transformBundleAndSaveInformations
                            }
                            setItemAdded={setItemAdded}
                            itemAdded={itemAdded}
                            setIndexItemSelecting={setIndexItemSelecting}
                            indexItemSelecting={indexItemSelecting}
                            relatedProductsData={relatedProductsData}
                            title={
                                productContentful?.bundleAndSaveTitle! ||
                                t('pdp||bundleandsave|Bundle And Save')
                            }
                            relatedSKUs={
                                bundle_and_save_skus?.length &&
                                typeof bundle_and_save_skus?.[0] === 'string'
                                    ? (bundle_and_save_skus as string[])
                                    : []
                            }
                            infoProduct
                        />
                    ) : null}
                    <ProductFeatures features={productContentful?.features} />
                    {pdp_cta.visible &&
                        isTransactionalView('stock', locale, customer) &&
                        document &&
                        createPortal(
                            <div
                                className={cn(
                                    s['add-to-cart-container'],
                                    s[trueStockStatus]
                                )}
                                id="addToCartParent"
                            >
                                {!isOutOfStock && (
                                    <div className={s['shipping-info']}>
                                        <KlarnaWrapper
                                            productPrice={productPrice}
                                        />
                                    </div>
                                )}
                                {renderAddToCartBtn}
                                {productContentful?.buildYourOwnLink && (
                                    <Link
                                        href={
                                            productContentful?.buildYourOwnLink
                                        }
                                    >
                                        <a>
                                            <Button
                                                className={cn(
                                                    'scuf-button-secondary',
                                                    s['build-your-own-btn']
                                                )}
                                            >
                                                <CustomizeIcon />
                                                {t('Build your own')}
                                            </Button>
                                        </a>
                                    </Link>
                                )}
                            </div>,
                            document.querySelector(
                                '#add-to-cart-placeholder-desktop'
                            )!
                        )}
                </div>
            </section>
        )
    }, [
        badge,
        variantsSelector,
        isAccessory,
        bundle_and_save_skus,
        bundle_products,
        cross_sell_skus,
        customer,
        description,
        indexItemSelecting,
        isChecked,
        isConfig,
        isDisable,
        isGiftCard,
        isOutOfStock,
        itemAdded,
        locale,
        name,
        not_sellable,
        options,
        pdp_cta.visible,
        product,
        productContentful?.awards,
        productContentful?.features,
        productContentful?.promoMessage?.content,
        productContentful?.compatibilityCheck,
        productPrice,
        productPriceRange,
        relatedProductsData,
        renderAddToCartBtn,
        selectedOptions,
        shippingInfo,
        show_variants,
        sku,
        transformBundleAndSaveInformations,
        variant,
        variantBadge,
        variantName,
        resetItemsCrossell,
        variantPriceRange,
        variants.visible,
        deliveryByDate,
        substituteItem,
        productContentful?.buildYourOwnLink,
        productContentful?.longDescription
    ])

    const availableCountries =
        productData.productDetail.items[0].available_countries
    const countriesLanguageAvailable = regionMapLanguages?.filter(
        (countries: any) =>
            availableCountries.includes(countries.region) ||
            edgekvRegionAvailables.includes(countries?.region?.toUpperCase())
    )
    const getCurrentRegion: any = () => {
        const region = regionMapLanguages.find(
            (country) =>
                country.redirectRegion === currentRegion?.toLocaleLowerCase() ||
                country.region === currentRegion?.toLocaleLowerCase()
        )
        return region?.region
    }

    const floatingATCHeader = useMemo(() => {
        const element =
            (pdp_cta.visible &&
                isTransactionalView('stock', locale, customer) &&
                global?.document &&
                createPortal(
                    <section
                        className={cn(
                            s['atc-header'],
                            endOfHeadInfoContainer
                                ? s['atc-header-show']
                                : s['atc-header-hide']
                        )}
                    >
                        <div
                            className={cn(
                                'scuf-container',
                                s['floating-ATC-container']
                            )}
                        >
                            <h1>{name}</h1>
                            {!isOutOfStock && (
                                <div className={s['atc-wrapper']}>
                                    {renderAddToCartBtn}
                                </div>
                            )}
                        </div>
                    </section>,
                    document.querySelector(`#${ATC_HEADER_ID}`)!
                )) ||
            null
        return element
    }, [
        pdp_cta.visible,
        locale,
        customer,
        name,
        renderAddToCartBtn,
        endOfHeadInfoContainer,
        isOutOfStock
    ])

    useEffect(() => {
        const tabID = router.asPath.split('#')[1]
        if (tabID) {
            document.getElementById('tabs-container')?.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest'
            })
        }
    }, [])

    return (
        <>
            <Head>
                {countriesLanguageAvailable?.map((country: any) => {
                    const edgeKVSku = findSKUFromEdgeKVMap(country.region)
                    let convertPath = `${path}`
                    if (
                        edgeKVSku &&
                        edgeKVSku?.sku.toLowerCase() !==
                            product?.sku.toLowerCase()
                    ) {
                        const splittedPath = convertPath.split('/')
                        splittedPath[splittedPath.length - 1] =
                            edgeKVSku?.sku_url
                        splittedPath[
                            splittedPath.length - 2
                        ] = edgeKVSku?.sku.toLowerCase()
                        convertPath = splittedPath.join('/')
                    }
                    return country?.redirectRegion ? (
                        <link
                            key={`${country?.redirectLanguage}-${country.region}`}
                            rel={rel}
                            hrefLang={`${country.redirectLanguage}-${country.region}`}
                            href={`${origin}${country?.redirectRegion}/${country?.redirectLanguage}${convertPath}`}
                        />
                    ) : (
                        country.languages.map(
                            (language: any, index: number) => {
                                const href = `${origin}${country.region}/${language}${convertPath}`
                                const hrefLang = `${language}-${country.region}`
                                return (
                                    <>
                                        {index === 0 &&
                                            country.defaultLanguage && (
                                                <link
                                                    key={`${country.defaultLanguage}-${country.region}`}
                                                    rel={rel}
                                                    hrefLang={`${country.defaultLanguage}`}
                                                    href={`${origin}${country.region}/${country.defaultLanguage}${convertPath}`}
                                                />
                                            )}
                                        <link
                                            key={hrefLang}
                                            rel={rel}
                                            hrefLang={hrefLang}
                                            href={href}
                                        />
                                    </>
                                )
                            }
                        )
                    )
                })}
                {!REGION_UNAVAILABLE_HREFLANG.includes(
                    currentRegion.toUpperCase()
                )
                    ? Object.values(LANGUAGE_CODES_MAP)
                          .filter(
                              (language) =>
                                  language !== currentLang && //
                                  !countriesLanguageAvailable.some(
                                      (region) =>
                                          region.redirectLanguage ===
                                              language &&
                                          region.redirectRegion ===
                                              currentRegion.toLocaleLowerCase()
                                  )
                          )
                          .map((lang) => (
                              // eslint-disable-next-line react/jsx-key
                              <link
                                  rel={rel}
                                  hrefLang={`${lang}-${getCurrentRegion()}`}
                                  href={`${origin}${currentRegion.toLocaleLowerCase()}/${lang}${path}`}
                              />
                          ))
                    : null}
                <link rel="preconnect" href="https://res.cloudinary.com" />
                <link rel="dns-prefetch" href="https://res.cloudinary.com" />
                {!!mediaGalleryEntries[0]?.url && (
                    <link
                        rel="preload"
                        href={mediaGalleryEntries[0].url}
                        as="image"
                    />
                )}
            </Head>
            <Script
                id="preload-first-image"
                // eslint-disable-next-line i18next/no-literal-string
                strategy="beforeInteractive"
                dangerouslySetInnerHTML={{
                    __html: `
                              const link = document.createElement('link');
                              link.rel = 'preload';
                              link.as = 'image';
                              link.href = '${mediaGalleryEntries[0]?.url}';
                              document.head.appendChild(link);
                            `
                }}
            />
            <Container className={s['pdp-dark-bg']} clean>
                <NotifyMeModal
                    productTitle={name}
                    sku={sku}
                    productImage={
                        convertImageFormat(
                            image?.url,
                            'c_pad,q_auto,h_1024,w_1024',
                            'webp'
                        ) || ''
                    }
                    productUrl={
                        typeof window !== 'undefined'
                            ? window.location.href
                            : ''
                    }
                    isModalOpen={isNotifyMeModalOpen}
                    setIsModalOpen={setIsNotifyMeModalOpen}
                />
                <UnsubscribeNotifyMeModal
                    sku={sku}
                    isModalOpen={isUnsubscribeMeModalOpen}
                    setIsModalOpen={setIsUnsubscribeMeModalOpen}
                />
                {dataThirdPartyPromoMessage && promo_campaigns?.length ? (
                    <PromoCampaignModal
                        dataThirdPartyPromoMessage={dataThirdPartyPromoMessage}
                    />
                ) : null}
                {floatingATCHeader}
                <div className={s['container-classes']} id="container-classes">
                    <SEO
                        product={yotpoJsonLd({ product, review })}
                        productContentful={
                            productContentful as IProductContentfulResponse<TypeComponentsTab>
                        }
                        socialDefaultContent={socialContent}
                    />
                    <div className={cn(s['pdp-container'], 'full-screen-mode')}>
                        <div
                            className={cn(
                                s['pdp-left-container'],
                                {
                                    [s[
                                        'accessory-pdp-left-container'
                                    ]]: isAccessory
                                },
                                {
                                    ['has-featured-bundles-section']:
                                        productsFeaturedBundles?.length
                                }
                            )}
                            id="pdp-left-container"
                        >
                            <div
                                className={cn(s['pdp-gallery-container'], {
                                    [s[
                                        'pdp-accessory-gallery-container'
                                    ]]: isAccessory
                                })}
                            >
                                <div
                                    className={cn(s['pdp-breadcrumbs'], {
                                        [s[
                                            'accessory-pdp-breadcrumbs'
                                        ]]: isAccessory
                                    })}
                                >
                                    {product?.categories?.length &&
                                    dataMegaMenu?.data ? (
                                        <Breadcrumbs
                                            product={{
                                                name: product?.name ?? '',
                                                sku: product?.sku ?? '',
                                                categories:
                                                    product?.categories ?? [],
                                                categoryHierarchies:
                                                    product?.category_hierarchies ??
                                                    []
                                            }}
                                            mainCategorySlug={category_slug}
                                            separator=" / "
                                            rootText={t('breadcrumbs|Home')}
                                        />
                                    ) : null}
                                </div>
                                {productContentful?.headline && (
                                    <h2
                                        className={cn(
                                            s['pdp-product-headline'],
                                            {
                                                ['hidden']: isAccessory
                                            }
                                        )}
                                    >
                                        {productContentful.headline}
                                    </h2>
                                )}
                                {/* Check if product is an accessory */}
                                <ProductGallery
                                    bundleProducts={bundle_products}
                                    galleryControlDimensions={{
                                        width: 22,
                                        height: 22
                                    }}
                                    layout="responsive"
                                    isAccessoryProduct={isAccessory}
                                    mediaGalleryEntries={mediaGalleryEntries.map(
                                        (asset: any) => {
                                            if (
                                                !asset?.url ||
                                                asset?.url === ''
                                            ) {
                                                return {
                                                    ...asset,
                                                    url: `${
                                                        typeof window !==
                                                        'undefined'
                                                            ? window.location
                                                                  .origin
                                                            : ''
                                                    }/images/default-product-image.png`
                                                }
                                            } else {
                                                // asset.url = convertImageFormat(
                                                //     asset.url,
                                                //     'c_pad,q_auto,h_1024,w_1024',
                                                //     'webp'
                                                // )
                                                return asset
                                            }
                                        }
                                    )}
                                    embedVideoUrl={
                                        productContentful?.embedVideo?.url
                                    }
                                    cloudinaryDimensionsModel={
                                        productContentful?.cloudinaryDimensionsModel
                                    }
                                    howToLink={productContentful?.howToLink}
                                    productTabsVisible={productTabs}
                                    config={{
                                        mobile: {
                                            gallerytype: isAccessory
                                                ? GalleryTypeEnum.THUMBNAIL
                                                : GalleryTypeEnum.SCROLL,
                                            zoom: false,
                                            loop: true,
                                            fullscreen: false,
                                            sliderProps: {
                                                main: {
                                                    navigation: false,
                                                    pagination: false,
                                                    allowTouchMove: true
                                                }
                                            }
                                        },
                                        desktop: {
                                            gallerytype: isAccessory
                                                ? GalleryTypeEnum.THUMBNAIL
                                                : GalleryTypeEnum.SCROLL,
                                            zoom: false,
                                            fullscreen: true,
                                            thumb: true,
                                            sliderProps: {
                                                main: {
                                                    navigation: false,
                                                    pagination: false,
                                                    maxSlides: 5
                                                }
                                            }
                                        }
                                    }}
                                />
                            </div>

                            {renderFeaturedBundles}
                        </div>
                        {productDetails}
                    </div>
                </div>
                {productContentful?.horizontalLightingGallery ? (
                    <HorizontalLightingGallery
                        content={productContentful.horizontalLightingGallery}
                    />
                ) : null}
                {productContentful?.verticalLightingGallery ? (
                    <VerticalLightingGallery
                        content={productContentful.verticalLightingGallery}
                    />
                ) : null}
                {productContentful?.imageTwoTileCallout ? (
                    <ImageTwoTile
                        content={productContentful.imageTwoTileCallout}
                    />
                ) : null}
                {productContentful?.twoTileCallout ? (
                    <TwotilewithCalloutMedia
                        content={productContentful.twoTileCallout}
                    />
                ) : null}
                {productContentful?.bannerIntereactiveCallouts ? (
                    <BannerIntereactiveCallout
                        content={productContentful.bannerIntereactiveCallouts}
                    />
                ) : null}
                {productContentful?.verticalGraphSingleBars ? (
                    <VerticalGraphSingleBars
                        content={productContentful.verticalGraphSingleBars}
                    />
                ) : null}
                {productContentful?.horizontalGraphSingleBars ? (
                    <HorizontalGraphSingleBars
                        content={productContentful.horizontalGraphSingleBars}
                    />
                ) : null}
                {productContentful?.verticalGraphDoubleBars ? (
                    <VerticalGraphDoubleBars
                        content={productContentful.verticalGraphDoubleBars}
                    />
                ) : null}
                {productContentful?.tabs?.length && (
                    <div
                        className={cn(
                            s['container-classes'],
                            s['tabs-container']
                        )}
                        id="tabs-container"
                    >
                        <Tabs
                            defaultTab={0}
                            tabs={filterEmptyTabs(productContentful.tabs, [
                                {
                                    contentType: 'blog-articles',
                                    filterKey: 'blogEntries'
                                }
                            ])}
                            linkedProducts={productContentful.linkedProducts}
                            packageContents={productContentful?.packageContents}
                            productData={product}
                            tabVariant={ETabVariant.DEFAULT_PDP_TAB}
                            className="pl-5"
                            atcVisible={pdp_cta.visible}
                            relatedProductsData={related_accessories_data}
                        />
                    </div>
                )}
                {productContentful?.contentModules && (
                    <ContentPage
                        pageContent={convertProductContentfulToContentPage(
                            productContentful!
                        )}
                    />
                )}
                <ProductContentCarousel
                    carouselContent={productContentful?.carouselContent}
                />

                {productContentful?.nonInteractiveProductBlock && (
                    <ProductBlocksWrapper
                        productBlocks={
                            productContentful.nonInteractiveProductBlock as IProductBlocks
                        }
                    />
                )}
                {productContentful?.items?.length && (
                    <div
                        className={cn(
                            s['container-classes'],
                            s['faq-container']
                        )}
                    >
                        <FAQModuleCollection items={productContentful.items} />
                    </div>
                )}
                {productContentful?.carouselHow?.length && (
                    <CarouselHow
                        carouselHow={productContentful?.carouselHow[0]}
                    />
                )}
                {productContentful?.productFamily?.productBlocks?.length && (
                    <ProductFamily content={productContentful?.productFamily} />
                )}

                {isTransactionalView('worksWellWith', locale, customer) &&
                    cross_sell_skus?.length > 0 &&
                    cross_sell_skus_data?.length > 0 && (
                        <CrosssellCarousel
                            products={{
                                productData: cross_sell_skus_data?.flatMap(
                                    (el) => el.productData
                                )
                            }}
                            title={productContentful?.crossellingTitle}
                        />
                    )}
            </Container>
        </>
    )
}

PDP.Layout = Layout
