import React, { Dispatch, SetStateAction } from 'react'
import chunk from 'lodash/chunk'
import cn from 'classnames'
import { ProductInterface } from '@pylot-data/fwrdschema'
import { RichContent } from '@corsairitshopify/pylot-rich-content'

import GridPage from './GridPage'
import EmptyPLP from './EmptyPLP'
import s from './Plpgrid.module.scss'

type MarketingTile = {
    columnSpan: {
        col: number
        span: number
    }
    content: string
    class: string
    position: number
}

type PlpGridPropTypes<P> = {
    products: P[]
    ProductTile: React.ComponentType<P>
    LoadingTile: React.ComponentType
    marketingTiles?: MarketingTile[]
    pageSize?: number
    isLoading?: boolean
    hideMarketingBlock?: boolean
    showMarketingBlockOnLessProducts?: boolean
    className?: string
    compareItems: ProductInterface[]
    setCompareItems: Dispatch<SetStateAction<any>>
    setCompareVisibility?: Dispatch<SetStateAction<any>>
    searchTerm?: string | string[] | undefined
    isFindMemoryByCompatibility?: boolean
}

const PLPGrid = ({
    products,
    ProductTile,
    LoadingTile,
    marketingTiles = [],
    pageSize,
    isLoading = false,
    hideMarketingBlock = false,
    showMarketingBlockOnLessProducts = false,
    className,
    compareItems,
    setCompareItems,
    setCompareVisibility,
    searchTerm,
    isFindMemoryByCompatibility
}: PlpGridPropTypes<any>): JSX.Element => {
    if (products.length === 0 && isLoading) {
        return (
            <EmptyPLP
                searchTerm={searchTerm}
                isMemoryFinder={isFindMemoryByCompatibility}
            />
        )
    }

    const processedMarketing = marketingTiles.map((item) => ({
        ...item,
        startIdx:
            showMarketingBlockOnLessProducts && products.length < item.position
                ? products.length - 1
                : item.position - 1
    }))

    const getMarketing = (idx: number) => {
        const filteredMarketing = processedMarketing.filter(
            (item) => item.startIdx === idx
        )
        if (!filteredMarketing.length) return null
        return (
            <div
                className={cn(filteredMarketing[0].class, 'marketing-tile')}
                style={{
                    gridColumn: `${filteredMarketing[0].columnSpan.col} / span ${filteredMarketing[0].columnSpan.span}`
                }}
            >
                <RichContent html={filteredMarketing[0].content} />
            </div>
        )
    }

    if (isLoading)
        return (
            <div className={cn(s['plp-grid'], className)}>
                <GridPage
                    startingIndex={0}
                    items={[{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}
                    ProductTile={LoadingTile}
                    getMarketing={getMarketing}
                />
            </div>
        )

    return (
        <div className={cn(s['plp-grid'], className)}>
            {chunk(products, pageSize ?? products.length).map((items, key) => (
                <GridPage
                    key={key}
                    startingIndex={pageSize ? key * pageSize : 0}
                    items={items}
                    ProductTile={ProductTile}
                    getMarketing={getMarketing}
                    hideMarketingBlock={hideMarketingBlock}
                    compareItems={compareItems}
                    setCompareItems={setCompareItems}
                    setCompareVisibility={setCompareVisibility}
                />
            ))}
        </div>
    )
}

export default PLPGrid
