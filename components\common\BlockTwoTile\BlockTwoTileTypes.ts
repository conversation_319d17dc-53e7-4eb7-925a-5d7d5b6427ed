import {
    CTAType,
    ImageType
} from '@pylot-data/hooks/contentful/use-content-json'
import {
    IHorizontalGraphSingleBarsType,
    IHTMLContentPage,
    IMeta,
    inlineStyling,
    IVerticalGraphDoubleBarsType,
    IVerticalGraphSingleBarsType
} from '../types'
import { ListsTableInterface } from '../ListsTable/ListsTable'
import { Content } from '../TableComparison/TableComparison.interfaces'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface Icons {
    title: string
    image: ImageType
    description: string
    identifier: string
    height?: number
    width?: number
    cloudinaryImage?: CloudinaryImage[]
    iconPosition?: boolean
}
// type ctaBorder = 'None' | 'Yellow' | 'Black'
// type ctaBackground = 'Yellow' | 'Black' | 'None'
type padding = 'Bottom' | 'Both' | 'None' | 'Top'
export type twoTileCta = {
    descriptionPosition?: string
    ctaRichText?: string
    cta?: CTAType
    title?: string
    image?: ImageType
    cloudinaryImage?: CloudinaryImage[]
    meta: IMeta<'componentCta'>
    displayText: string
    url: string
    openInANewTab?: boolean
    textColor?: string
    // ctaBorder?: ctaBorder
    // ctaBackground?: ctaBackground
    openInPopup?: boolean
    popupHtmlContent?: IHTMLContentPage
}

export interface BlockTwoTileResponse {
    subHeading: string
    subheadingColor?: string
    headingFontColor?: string
    heading?: string
    headingType: string
    layoutType: string
    sectionTitle?: string
    layoutPosition: string
    cloudinaryImageVideo: CloudinaryImage[]
    imageWidth?: string
    imageHeight?: string
    description?: string
    identifier: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryMobileBackgroundImage?: CloudinaryImage[]
    backgroundVideoBrightness?: number
    colorCodeGradient: string
    inlineStyling?: inlineStyling
    animation: boolean
    disclaimerBlock: string
    cta: twoTileCta[]
    fontColor: string
    pageBackgroundImage?: ImageType
    forceImageTakesFullHeight?: boolean
    padding: padding
    containerType: string
    variants?: string
}

export type BlockTwoTileCarouselProps = {
    content: {
        carouselItems: BlockTwoTileResponse[]
        backgroundColor?: string
        desktopBackgroundImage?: CloudinaryImage[]
        mobileBackgroundImage?: CloudinaryImage[]
        desktopPaddingTop?: string
        desktopPaddingBottom?: string
        mobilePaddingTop?: string
        mobilePaddingBottom?: string
        heading?: string
        headingType?: 'h1' | 'h2' | 'h3' | 'h4'
        headingColor?: string
    }
}
