import React from 'react'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'

import {
    FeatureProps,
    GroupColumn as GroupColumnProps,
    TechSpecsAndSupportProps
} from './types'
import s from './TechSpecsAndSupport.module.scss'
import Collapse from './Collapse'
import Group from './Group'
import GroupColumn from './GroupColumn'
import Feature from './Feature'

const Slider = dynamic(() => import('./Slider'), { ssr: false })

const PDPTechSpecsAndSupport: React.FC<TechSpecsAndSupportProps> = ({
    content
}) => {
    const { t } = useTranslation(['pdp'])

    const {
        techSpecsIdentifier,
        techSpecsGroupColumn1 = [],
        techSpecsGroupColumn2 = [],
        techSpecsGroupColumn3 = [],
        techSpecsImageSlider,
        supportIdentifier,
        supportGroupColumn = []
    } = content

    const renderGroupColumn = (groupColumn: GroupColumnProps[]) => {
        if (!groupColumn.length) return null

        return (
            <GroupColumn>
                {groupColumn.map((group, index) => (
                    <Group
                        key={`${group.heading} - ${index}`}
                        heading={group.heading}
                        gap={group.featuresGap}
                    >
                        {renderFeatures(group.features)}
                    </Group>
                ))}
            </GroupColumn>
        )
    }

    const renderFeatures = (features: FeatureProps[]) => {
        if (!features.length) return null

        return features.map((feature, index) => (
            <Feature key={`${feature.title} - ${index}`} {...feature} />
        ))
    }

    const hasTechSpecs =
        techSpecsGroupColumn1.length > 0 ||
        techSpecsGroupColumn2.length > 0 ||
        techSpecsGroupColumn3.length > 0 ||
        !!techSpecsImageSlider?.slides.length

    const hasSupport = supportGroupColumn.length > 0

    return (
        <section className={s['container']}>
            {hasTechSpecs && (
                <Collapse id={techSpecsIdentifier} heading={t('Tech Specs')}>
                    {renderGroupColumn(techSpecsGroupColumn1)}
                    {renderGroupColumn(techSpecsGroupColumn2)}
                    {renderGroupColumn(techSpecsGroupColumn3)}
                    {!!techSpecsImageSlider?.heading &&
                        !!techSpecsImageSlider?.slides.length && (
                            <Group heading={techSpecsImageSlider.heading}>
                                <Slider slides={techSpecsImageSlider.slides} />
                            </Group>
                        )}
                </Collapse>
            )}
            {hasSupport && (
                <>
                    <div className={s['divider']}>
                        <div className={s['divider--inner']} />
                    </div>
                    <Collapse id={supportIdentifier} heading={t('Support')}>
                        {renderGroupColumn(supportGroupColumn)}
                    </Collapse>
                </>
            )}
        </section>
    )
}

export default PDPTechSpecsAndSupport
