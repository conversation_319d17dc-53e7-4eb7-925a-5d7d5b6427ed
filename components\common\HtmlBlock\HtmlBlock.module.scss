/*
 * HTML Block
 * This is a legacy file, please do not delete at any cost
 * This file can be used as a reference for future bug fixes
 */
#HtmlBlock {
    :global {
        h1,
        .h1,
        h2,
        .h2,
        h3,
        .h3,
        h4,
        .h4,
        p,
        a,
        span,
        div {
            font-size: 12px;
        }
        section {
            background-color: #fff;
        }
        .pdpWrapper .wrapper {
            width: 90%;
            margin: 0 5%;
        }
        @media only screen and (min-width: 1320px) {
            .pdpWrapper .wrapper {
                width: 1200px;
                margin: 0 auto;
            }
        }
        .pdpWrapper #panel2 .content .wrapper {
            padding: 30px 0;
        }
        .pdpWrapper .chosen-container-single .chosen-single span {
            text-transform: unset !important; // Inherited styles from legacy site
        }
        .pdpWrapper .shop-box .shop-logo > img {
            max-height: 100px;
            max-width: 180px;
        }
        .pdpWrapper .panel-template a.yellow-cta:link,
        .pdpWrapper .panel-template a.yellow-cta:visited {
            @apply bg-yellow;
            color: #000;
            transition: all 0.3s ease;
        }
        .pdpWrapper .panel-template a.yellow-cta:hover,
        .pdpWrapper .panel-template a.yellow-cta:active {
            background-color: #f9f791;
            color: #000;
        }
        .pdpWrapper .pdpWrapper section.triangle-bkg {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/img/reusable/pdp-bkg-triangle.png')
                center no-repeat;
            background-size: cover;
        }
        .pdpWrapper .pdpWrapper section.triangle-left-bkg {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/img/reusable/pdp-bkg-left-triangle.png')
                center no-repeat;
            background-size: cover;
        }
        .pdpWrapper .pdpWrapper section.triangle-right-bkg {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/img/reusable/pdp-bkg-right-triangle.png')
                center no-repeat;
            background-size: cover;
        }
        .pdpWrapper .pdpWrapper section.triangle-left-000-bkg {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/img/reusable/pdp-blk-bkg-left-triangle.png')
                center no-repeat;
            background-size: cover;
        }
        .pdpWrapper .pdpWrapper section.triangle-right-000-bkg {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/img/reusable/pdp-blk-bkg-right-triangle.png')
                center no-repeat;
            background-size: cover;
        }
        .pdpWrapper .pdpWrapper section.pdp-bkg-fff {
            background-color: #fff;
        }
        .pdpWrapper .pdpWrapper section.pdp-bkg-eee {
            background-color: #eee;
        }
        .pdpWrapper .pdpWrapper section.pdp-bkg-000 {
            background-color: #000;
        }
        .pdpWrapper .pdpWrapper section.pdp-bkg-333 {
            background-color: #333;
        }
        .pdpWrapper .shop-box .shop-logo > img {
            max-height: 100px;
            max-width: 180px;
        }
        .pdpWrapper #specs-table {
            border-collapse: collapse;
            text-align: left;
            vertical-align: top;
        }
        .pdpWrapper #specs-table td,
        .pdpWrapper th {
            padding: 5px;
            border: 1px solid #ccc;
        }
        .pdpWrapper #specs-table .specs-table-title {
            background-color: #f4f4f4;
            color: #000;
            font-weight: bold;
            text-align: left;
        }
        .pdpWrapper #specs-table .specs-table-data {
            background-color: #fff;
            text-align: left;
        }
        .pdpWrapper #tab-awards > div > article > h4 {
            font-size: 12px !important; // Inherited styles from legacy site
        }
        .pdpWrapper #pdp {
            overflow: hidden;
        }
        .pdpWrapper .text-center {
            text-align: center;
        }
        .pdpWrapper .slick-slide {
            outline: none;
        }
        .pdpWrapper .yellow {
            @apply text-yellow;
        }
        .pdpWrapper .blue {
            color: #39b5fb;
        }
        .pdpWrapper section {
            position: relative;
        }
        .pdpWrapper .show-on-mobile {
            display: none;
        }
        .pdpWrapper section h1,
        .pdpWrapper section h2 {
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.6px;
        }
        .pdpWrapper section h1 {
            font-size: 1.8em;
            font-weight: 600;
            line-height: 1.2;
        }
        .pdpWrapper section h1.bebas,
        .pdpWrapper section h2.bebas {
            @apply font-tomorrow;
            font-size: 3.75rem;
            letter-spacing: 0.05rem;
            font-weight: 600;
            line-height: 3.75rem;
        }
        .pdpWrapper section h3 {
            font-weight: 400;
        }
        .pdpWrapper section p {
            @apply font-tomorrow font-semibold;
            font-size: 1rem;
            line-height: 24px;
            letter-spacing: 0.8px;
            font-weight: 400;

            @screen md {
                font-size: 20.8px;
                line-height: 36px;
            }

            @screen lg {
                font-size: 15.6px;
                letter-spacing: 0.78px;
                line-height: 28.08px;
            }
        }
        .pdpWrapper section .wrapper {
            position: relative;
        }
        .pdpWrapper section p.centered {
            font-size: 18px;
            line-height: 1.8;
            max-width: 1200px;
            margin: 18px auto 0;
            letter-spacing: 0.03em;
        }
        .pdpWrapper .item,
        .pdpWrapper .item a,
        .pdpWrapper .item a img {
            outline: none;
        }
        .pdpWrapper .cta,
        .pdpWrapper .mfp-wrap .cta {
            @apply font-tomorrow font-semibold;
            padding: 12px 24px;
            font-weight: 400;
            font-size: 12px;
            letter-spacing: 0.124px;
            display: inline-block;
            background: #000;
            color: #fff;
            margin: 12px 0;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper .cta.black {
            background: #000;
            color: #fff;
            font-weight: 100;
        }
        .pdpWrapper .cta.black:hover {
            background: #333;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper .cta:hover .mfp-wrap .cta:hover {
            background: #000;
            color: #fff;
            font-weight: 400;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper .content-left,
        .pdpWrapper .content-right {
            padding: 8vw 0 9vw;
            min-height: 15vw;
            width: 46.5%;
            display: inline-block;
            vertical-align: middle;
        }
        .pdpWrapper .content-right {
            padding-left: 1%;
            margin-left: 52%;
            text-align: right;
        }
        .pdpWrapper .content-right p {
            font-weight: 100;
        }
        .pdpWrapper .half {
            width: 47%;
            display: inline-block;
            vertical-align: top;
            position: relative;
        }
        .pdpWrapper .sticky_addtocart {
            position: fixed;
            bottom: -100px;
            right: 0;
            padding: 12px;
            background: #fff;
            z-index: 2;
            border: #eee 1px solid;
            opacity: 0;
            display: none;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .sticky_addtocart.active {
            opacity: 1;
            display: block;
            bottom: 0;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .sticky_addtocart .cta.small {
            padding: 12px 12px;
            font-size: 12px;
            margin: 0 0 0 12px;
        }
        .pdpWrapper #pdp-corsair .text-center {
            text-align: center;
        }
        .pdpWrapper #pdp-corsair .slick-slide {
            outline: none;
        }
        .pdpWrapper #pdp-corsair .yellow {
            @apply text-yellow;
        }
        .pdpWrapper #pdp-corsair section {
            position: relative;
        }
        .pdpWrapper #pdp-corsair .show-on-mobile {
            display: none;
        }
        .pdpWrapper #pdp-corsair section h1,
        .pdpWrapper #pdp-corsair section h2 {
            letter-spacing: 0.6px;
        }
        .pdpWrapper #pdp-corsair section h1 {
            font-weight: 400;
        }
        .pdpWrapper #pdp-corsair section h3 {
            font-weight: 600;
            color: #222;
            font-size: 13px;
            letter-spacing: 2.4px;
            font-weight: 500;
        }
        .pdpWrapper #pdp-corsair section p {
            font-size: 13.2px;
            line-height: 1.8;
            letter-spacing: 0.6px;
            font-weight: 400;
            position: relative;
            display: block;
        }
        .pdpWrapper #pdp-corsair section .wrapper {
            position: relative;
        }
        .pdpWrapper #pdp-corsair section p.centered {
            font-size: 18px;
            line-height: 1.8;
            max-width: 1200px;
            margin: 18px auto 0;
            letter-spacing: 0.03em;
        }
        .pdpWrapper .chair-addtocart-cta {
            margin: 12px 0;
        }
        .pdpWrapper #pdp-corsair .item,
        .pdpWrapper #pdp-corsair .item a,
        .pdpWrapper #pdp-corsair .item a img {
            outline: none;
        }
        .pdpWrapper #pdp-corsair .item a img {
            max-width: 100%;
        }
        .pdpWrapper .item a.learn-more-btn {
            display: inline-block;
            background-color: #ccc;
        }
        .pdpWrapper #pdp-corsair .cta,
        .pdpWrapper .mfp-wrap .cta {
            padding: 15.6px 24px;
            font-weight: 400;
            font-size: 12px;
            letter-spacing: 0.124px;
            display: inline-block;
            background: #000;
            color: #fff;
            margin: 12px 0;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #pdp-corsair .cta.black {
            background: #000;
            color: #fff;
            font-weight: 100;
        }
        .pdpWrapper #pdp-corsair .cta.black:hover {
            background: #333;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #pdp-corsair .cta:hover .mfp-wrap .cta:hover {
            background: #000;
            color: #fff;
            font-weight: 400;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #pdp-corsair .content-left,
        .pdpWrapper #pdp-corsair .content-right {
            padding: 8vw 0 9vw;
            min-height: 15vw;
            width: 46.5%;
            display: inline-block;
            vertical-align: middle;
        }
        .pdpWrapper #pdp-corsair .content-right {
            padding-left: 1%;
            margin-left: 52%;
            text-align: right;
        }
        .pdpWrapper #pdp-corsair .content-right p {
            font-weight: 100;
        }
        .pdpWrapper #pdp-corsair .half {
            width: 47%;
            display: inline-block;
            vertical-align: top;
            position: relative;
        }
        .pdpWrapper #pdp-corsair .sticky_addtocart {
            position: fixed;
            bottom: -100px;
            right: 0;
            padding: 12px;
            background: #fff;
            z-index: 2;
            border: #eee 1px solid;
            opacity: 0;
            display: none;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair .sticky_addtocart.active {
            opacity: 1;
            display: block;
            bottom: 0;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair .sticky_addtocart .cta.small {
            padding: 12px 12px;
            font-size: 12px;
            margin: 0 0 0 12px;
            text-transform: uppercase;
        }
        .pdpWrapper #panel1 {
            background: #f9f9f9;
            background: -moz-linear-gradient(
                top,
                #fafafa 0%,
                #fafafa 90%,
                #f3f3f3 100%
            );
            background: -webkit-linear-gradient(
                top,
                #fafafa 0%,
                #fafafa 90%,
                #f3f3f3 100%
            );
            background: linear-gradient(
                to bottom,
                #fafafa 0%,
                #fafafa 90%,
                #f3f3f3 100%
            );
            color: #666;
            padding: 10em 60px;
        }
        .pdpWrapper #panel1 hr {
            margin: 24px 0;
        }
        .pdpWrapper #panel1 h1 {
            font-weight: 500;
        }
        .pdpWrapper #panel1 h1 small {
            font-size: 40%;
            font-weight: 100;
            color: #999;
            line-height: 2.5;
        }
        .pdpWrapper #panel1 #price {
            margin-top: 5px;
            font-weight: 500;
        }
        .pdpWrapper #panel1 #product-details .original-price {
            color: black;
            display: inline-block;
        }
        .pdpWrapper #panel1 #product-details .original-price.struck {
            position: relative;
        }
        .pdpWrapper #panel1 #product-details .original-price.struck:after {
            border-bottom: 0.125em solid black;
            content: '';
            left: 0;
            margin-top: calc(0.125em / 2 * -1);
            position: absolute;
            right: 0;
            top: 50%;
        }
        .pdpWrapper #panel1 #product-details .sale-price {
            color: #4292ff;
            display: inline-block;
        }
        .pdpWrapper #panel1 #price small {
            font-size: 50%;
            font-weight: 100;
            vertical-align: super;
        }
        .pdpWrapper #panel1 .wrapper {
            display: flex;
            flex-direction: row;
            margin-top: 3em;
        }
        .pdpWrapper #panel1 #details {
            flex: 1;
            margin-left: 140px;
            position: relative;
        }
        .pdpWrapper #pdp-corsair #panel1 {
            background: #f9f9f9;
            background: -moz-linear-gradient(
                top,
                #fafafa 0%,
                #fafafa 90%,
                #f3f3f3 100%
            );
            background: -webkit-linear-gradient(
                top,
                #fafafa 0%,
                #fafafa 90%,
                #f3f3f3 100%
            );
            background: linear-gradient(
                to bottom,
                #fafafa 0%,
                #fafafa 90%,
                #f3f3f3 100%
            );
            color: #666;
            padding: 10em 60px;
        }
        .pdpWrapper #pdp-corsair #panel1 hr {
            margin: 24px 0;
        }
        .pdpWrapper #pdp-corsair #panel1 h1 {
            font-weight: 500;
        }
        .pdpWrapper #pdp-corsair #panel1 h1 small {
            font-size: 10px;
            font-weight: 400;
            color: #a1a1a1;
            line-height: 2.5;
            letter-spacing: 0.85px;
            width: 115px;
            height: 10px;
        }
        .pdpWrapper #pdp-corsair #panel1 #price {
            margin-top: 5px;
            font-weight: 500;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details .original-price {
            color: black;
            display: inline-table;
        }
        .pdpWrapper
            #pdp-corsair
            #panel1
            #product-details
            .original-price.struck {
            position: relative;
        }
        .pdpWrapper
            #pdp-corsair
            #panel1
            #product-details
            .original-price.struck:after {
            border-bottom: 0.125em solid black;
            content: '';
            left: 0;
            margin-top: calc(0.125em / 2 * -1);
            position: absolute;
            right: 0;
            top: 50%;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details #childProductList {
            list-style-type: disc;
            list-style-position: outside;
            margin-left: 17px;
            margin-top: 10px;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details .pdpBundleList {
            margin-bottom: 10px;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details .sale-price {
            color: #4292ff;
            display: inline-block;
        }
        .pdpWrapper #pdp-corsair #panel1 #price small {
            font-size: 50%;
            font-weight: 100;
            padding-left: 8px;
        }
        .pdpWrapper #pdp-corsair #panel1 .wrapper {
            display: flex;
            flex-direction: row;
            margin-top: 3em;
        }
        .pdpWrapper .text-out-of-stock {
            padding-bottom: 15px;
            color: #ec7205;
            display: none;
        }
        .pdpWrapper #pdp-corsair .pdp-corsair #panel1 .wrapper {
            display: unset;
        }
        .pdpWrapper #pdp-corsair #panel1 #details {
            flex: 1;
            margin-left: 140px;
            position: relative;
        }
        .pdpWrapper #panel1 #product_full {
            max-width: 100%;
            max-height: 600px;
        }
        .pdpWrapper #panel1 #product_excerpt {
            font-size: 9.6px;
            letter-spacing: 0.012px;
        }
        .pdpWrapper #panel1 #product-details {
            max-width: 300px;
        }
        .pdpWrapper #panel1 #product-details .deal {
            color: white;
            @apply font-tomorrow;
            font-weight: 600;
            font-size: 9.6px;
            letter-spacing: 12px;
            display: inline-block;
            padding: 10.8px 15.6px;
            margin-bottom: 18px;
        }
        .pdpWrapper #panel1 #product-details .deal.featured-deal {
            background: #ff4c4c;
        }
        .pdpWrapper #panel1 #product-details .discount small {
            color: black;
            font-size: 48%;
            font-weight: 600;
            letter-spacing: 0.6px;
        }
        .pdpWrapper #panel1 #quantity {
            background: white !important; // Inherited styles from legacy site
            padding: 12px;
            height: 1rem;
            border: 1px solid #eee;
            border-radius: 0;
            font-weight: 400;
            background-clip: none;
            box-shadow: none;
            color: #333;
            background-color: #f7f8f9;
            margin: 0 0 12px 12px;
            text-decoration: none;
            white-space: nowrap;
            line-height: normal;
            letter-spacing: 0.6px;
            width: 17%;
        }
        .pdpWrapper #panel1 #colors li {
            display: inline-block;
            border-radius: 50%;
            height: 15px;
            width: 15px;
            border: 2px solid #ccc;
            padding: 3px;
            transition: all 300ms ease;
            -webkit-transition: all 300ms ease;
            cursor: pointer;
        }
        .pdpWrapper #panel1 #colors .active,
        .pdpWrapper .no-touch #panel1 #colors li:hover {
            border: 2px solid #666;
            transition: all 300ms ease;
            -webkit-transition: all 300ms ease;
        }
        .pdpWrapper #panel1 #colors .inner {
            border-radius: 50%;
            width: 100%;
            height: 100%;
            background-color: #222;
        }
        .pdpWrapper #panel1 #colors ul {
            letter-spacing: 12px;
            margin: 0;
            padding: 0.7em 0 0.95em;
        }
        .pdpWrapper #panel1 #options #colors .color-name {
            color: #c9c9c9;
            text-transform: uppercase;
        }
        .pdpWrapper #panel1 .cta {
            display: block;
        }
        .pdpWrapper #panel1 .thumbs {
            width: 120px;
            text-align: center;
            padding: 1.12px;
            margin: 18px;
            display: none;
        }
        .pdpWrapper #panel1 .item .media-content {
            width: 100%;
            height: 540px;
            background-size: contain !important; // Inherited styles from legacy site
            position: relative;
        }
        .pdpWrapper #panel1 .videoWrapper {
            position: relative;
            padding-bottom: 56%;
            height: 0;
        }
        .pdpWrapper #panel1 .videoWrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .pdpWrapper #panel1 .full_pic {
            margin-top: 0.5em;
            margin-left: 160px;
            display: none;
        }
        .pdpWrapper #panel1 .full_pic iframe,
        .pdpWrapper #panel1 .full_pic img {
            width: 100%;
        }
        .pdpWrapper #panel1 h1,
        .pdpWrapper #panel1 h2 {
            color: #000;
        }
        .pdpWrapper #panel1 #wishlist {
            border-right: 1px solid #ccc;
            display: inline-block;
            font-weight: 500;
            padding: 18px 0;
            width: 49%;
        }
        .pdpWrapper #panel1 #wishlist a {
            color: #399bff;
        }
        .pdpWrapper #panel1 #ships-by {
            display: inline-block;
            padding: 18px 0;
            width: 49%;
        }
        .pdpWrapper #panel1 .additional-infos {
            border-top: 1px solid #ccc;
            border-bottom: 1px solid #ccc;
            bottom: 0;
            margin-top: 12px;
            max-width: 300px;
            position: absolute;
            width: 100%;
        }
        .pdpWrapper #panel1 .carousel-testimonial {
            display: none;
        }
        .pdpWrapper #panel1 .carousel-testimonial .author {
            vertical-align: top;
            display: inline-block;
            width: 24%;
        }
        .pdpWrapper #panel1 .carousel-testimonial .text {
            vertical-align: top;
            display: inline-block;
            width: 75%;
        }
        .pdpWrapper .thumbs .thumb_img {
            cursor: pointer;
        }
        .pdpWrapper .thumbs .slick-slide {
            margin: 0.3em 0;
            height: 58px;
            transition: opacity 300ms ease;
            -webkit-transition: opacity 300ms ease;
        }
        .pdpWrapper .thumbs .slick-slide:not(.slick-current) {
            opacity: 0.5;
            border: none;
        }
        .pdpWrapper .thumbs .slick-slide:hover {
            opacity: 1;
            transition: opacity 300ms ease;
            -webkit-transition: opacity 300ms ease;
        }
        .pdpWrapper .slick-slide .author img {
            margin: 0 auto;
            cursor: pointer;
        }
        .pdpWrapper .thumbs .slick-current {
            border: none;
        }
        .pdpWrapper #panel1 .full_pic .slick-dots {
            bottom: 0em;
            display: block;
            position: relative;
            padding: 12px 0;
            margin: 0;
        }
        .pdpWrapper
            #panel1
            .full_pic
            .slick-dots
            li.slick-active
            button:before {
            opacity: 0.75;
            color: #333;
        }
        .pdpWrapper #panel1 .full_pic .slick-dots li button:before {
            opacity: 1;
            color: #aaa;
        }
        .pdpWrapper #panel1 .full_pic .featured .feature_cta {
            cursor: pointer;
            position: absolute;
            width: 10%;
            max-width: 41px;
            opacity: 0.9;
            display: none;
            filter: invert(70%);
            -webkit-filter: invert(70%);
        }
        .pdpWrapper #panel1 .full_pic .featured .feature_cta_icon {
            transform: rotate(0deg);
            transition: 250ms transform;
        }
        .pdpWrapper
            .no-touch
            #panel1
            .full_pic
            .featured
            .feature_cta_icon:hover {
            transform: rotate(135deg);
        }
        .pdpWrapper #panel1 .full_pic .featured .feature_cta:before {
            display: block;
            position: absolute;
            top: 49%;
            left: 49%;
            content: '';
            width: 22px;
            height: 22px;
            margin: -11px auto auto -11px;
            -webkit-transform-origin: 50% 50%;
            transform-origin: 50% 50%;
            border-radius: 50%;
            border: 1px solid orange;
            background-color: rgba(0, 0, 0, 0);
            opacity: 1;
            z-index: -1;
            pointer-events: none;
            animation: pulsate 3s cubic-bezier(0.2, 1, 0.2, 1) infinite;
        }
        .pdpWrapper #panel1 .full_pic .featured .feature_content {
            position: absolute;
            opacity: 0;
            background: rgba(255, 255, 255, 0.9);
            text-align: left;
            max-width: 300px;
            padding: 0em;
            border-radius: 0px;
            border: solid 1px #eee;
            z-index: -1;
            box-shadow: 2px 2px 10px rgba(50, 50, 50, 0.1);
            transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
            -webkit-transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
            margin-top: -12px;
        }
        .pdpWrapper .full_pic .feature_desc {
            padding: 12px;
        }
        .pdpWrapper #panel1 .full_pic .featured .feature_content.active {
            margin-top: 0;
            opacity: 1;
            z-index: 999;
            transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
            -webkit-transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
        }
        .pdpWrapper .chosen-container {
            text-align: left;
        }
        .pdpWrapper .chosen-container-single .chosen-single {
            background: white !important; // Inherited styles from legacy site
            position: relative;
            display: block;
            overflow: hidden;
            outline: none;
            padding: 12px 3.5em 12px 12px;
            height: auto;
            border: 1px solid #eee;
            border-radius: 0;
            font-weight: 400;
            background-clip: none;
            box-shadow: none;
            color: #333;
            text-decoration: none;
            white-space: nowrap;
            line-height: normal;
            letter-spacing: 0.6px;
            margin: 0;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: #f7f8f9;
            background-position: calc(100% - 12px) 13px;
            background-repeat: no-repeat;
            outline: none;
        }
        .pdpWrapper #sortby_chosen .chosen-single {
            background-color: #fff;
        }
        .pdpWrapper .chosen-container-single .chosen-single div b {
            display: none;
        }
        .pdpWrapper .chosen-container .chosen-drop {
            border-left: 1px solid #eee;
            border-right: 1px solid #eee;
            border-bottom: 1px solid #eee;
            border-radius: 0;
            box-shadow: none;
            color: #333;
            text-decoration: none;
            white-space: nowrap;
            letter-spacing: 0.6px;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: #fff;
            margin: 0;
            outline: none;
        }
        .pdpWrapper .chosen-container .chosen-results li.active-result {
            padding: 10px 8px;
        }
        .pdpWrapper .chosen-container .chosen-results li.highlighted {
            background-color: #999 !important; // Inherited styles from legacy site
            background-image: none !important; // Inherited styles from legacy site
            box-shadow: none !important; // Inherited styles from legacy site
            -webkit-box-shadow: none !important; // Inherited styles from legacy site
        }
        .pdpWrapper .chosen-container-single .chosen-search input[type='text'] {
            padding: 12px !important; // Inherited styles from legacy site
            border: 1px solid #ddd !important; // Inherited styles from legacy site
            background-position: calc(100% - 12px) 13px !important; // Inherited styles from legacy site
            background-repeat: no-repeat !important; // Inherited styles from legacy site
        }
        .pdpWrapper #pdp-corsair #panel1 #product_full {
            max-width: 100%;
            max-height: 600px;
        }
        .pdpWrapper #pdp-corsair #panel1 #product_excerpt {
            font-size: 13.2px;
            letter-spacing: 0.012px;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details {
            max-width: 360px;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details .deal {
            color: white;
            @apply font-tomorrow font-semibold;
            font-weight: 600;
            font-size: 9.6px;
            letter-spacing: 12px;
            display: inline-block;
            padding: 0.9em 15.6px;
            margin-bottom: 18px;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details .deal.featured-deal {
            @apply bg-yellow;
        }
        .pdpWrapper #pdp-corsair #panel1 #product-details .discount small {
            color: black;
            font-size: 48%;
            font-weight: 600;
            letter-spacing: 0.6px;
        }
        .pdpWrapper #pdp-corsair #panel1 #quantity {
            background: white !important; // Inherited styles from legacy site
            padding: 12px;
            height: 1rem;
            border: 1px solid #eee;
            border-radius: 0;
            font-weight: 400;
            background-clip: none;
            box-shadow: none;
            color: #333;
            background-color: #f7f8f9;
            margin: 0 0 12px 12px;
            text-decoration: none;
            white-space: nowrap;
            line-height: normal;
            letter-spacing: 0.6px;
            width: 17%;
        }
        .pdpWrapper #pdp-corsair #panel1 #colors li {
            display: inline-block;
            border-radius: 50%;
            height: 15px;
            width: 15px;
            border: 2px solid #ccc;
            padding: 3px;
            transition: all 300ms ease;
            -webkit-transition: all 300ms ease;
            cursor: pointer;
        }
        .pdpWrapper #pdp-corsair #panel1 #colors .active,
        .pdpWrapper .no-touch #pdp-corsair #panel1 #colors li:hover {
            border: 2px solid #666;
            transition: all 300ms ease;
            -webkit-transition: all 300ms ease;
        }
        .pdpWrapper #pdp-corsair #panel1 #colors .inner {
            border-radius: 50%;
            width: 100%;
            height: 100%;
            background-color: #222;
        }
        .pdpWrapper #pdp-corsair #panel1 #colors ul {
            letter-spacing: 12px;
            margin: 0;
            padding: 0.7em 0 0.95em;
        }
        .pdpWrapper #pdp-corsair #panel1 #options #colors .color-name {
            color: #c9c9c9;
            text-transform: uppercase;
        }
        .pdpWrapper #pdp-corsair #panel1 .cta {
            display: block;
        }
        .pdpWrapper #pdp-corsair #panel1 .thumbs {
            width: 120px;
            text-align: center;
            padding: 1.12px;
            margin: 18px;
            display: none;
        }
        .pdpWrapper #pdp-corsair #panel1 .item .media-content {
            width: 100%;
            height: 540px;
            background-size: contain !important; // Inherited styles from legacy site
            position: relative;
        }
        .pdpWrapper #pdp-corsair #panel1 .videoWrapper {
            position: relative;
            padding-bottom: 56%;
            height: 0;
        }
        .pdpWrapper #pdp-corsair #panel1 .videoWrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .pdpWrapper #pdp-corsair #panel1 .full_pic {
            margin-top: 0.5em;
            margin-left: 160px;
            display: none;
        }
        .pdpWrapper #pdp-corsair #panel1 .full_pic iframe,
        .pdpWrapper #pdp-corsair #panel1 .full_pic img {
            width: 100%;
        }
        .pdpWrapper #pdp-corsair #panel1 h1,
        .pdpWrapper #pdp-corsair #panel1 h2 {
            color: #000;
        }
        .pdpWrapper #pdp-corsair #panel1 #wishlist {
            border-right: 1px solid #ccc;
            display: inline-block;
            font-weight: 500;
            padding: 18px 0;
            width: 49%;
        }
        .pdpWrapper #pdp-corsair #panel1 #wishlist a {
            color: #399bff;
        }
        .pdpWrapper #pdp-corsair #panel1 #ships-by {
            display: inline-block;
            padding: 18px 0;
            width: 49%;
        }
        .pdpWrapper #pdp-corsair #panel1 .additional-infos {
            border-top: 1px solid #ccc;
            border-bottom: 1px solid #ccc;
            bottom: 0;
            margin-top: 12px;
            max-width: 300px;
            position: absolute;
            width: 100%;
        }
        .pdpWrapper #pdp-corsair #panel1 .carousel-testimonial {
            display: none;
        }
        .pdpWrapper #pdp-corsair #panel1 .carousel-testimonial .author {
            vertical-align: top;
            display: inline-block;
            width: 24%;
        }
        .pdpWrapper #pdp-corsair #panel1 .carousel-testimonial .text {
            vertical-align: top;
            display: inline-block;
            width: 75%;
        }
        .pdpWrapper #pdp-corsair .thumbs .thumb_img {
            cursor: pointer;
        }
        .pdpWrapper #pdp-corsair .thumbs .slick-slide {
            margin: 0.3em 0;
            height: 58px;
            transition: opacity 300ms ease;
            -webkit-transition: opacity 300ms ease;
        }
        .pdpWrapper #pdp-corsair .thumbs .slick-slide:not(.slick-current) {
            opacity: 0.5;
            border: none;
        }
        .pdpWrapper #pdp-corsair .thumbs .slick-slide:hover {
            opacity: 1;
            transition: opacity 300ms ease;
            -webkit-transition: opacity 300ms ease;
        }
        .pdpWrapper #pdp-corsair .slick-slide .author img {
            margin: 0 auto;
            cursor: pointer;
        }
        .pdpWrapper #pdp-corsair .thumbs .slick-current {
            border: none;
        }
        .pdpWrapper #pdp-corsair #panel1 .full_pic .slick-dots {
            bottom: 0em;
            display: block;
            position: relative;
            padding: 12px 0;
            margin: 0;
        }
        .pdpWrapper
            #pdp-corsair
            #panel1
            .full_pic
            .slick-dots
            li.slick-active
            button:before {
            opacity: 0.75;
            color: #333;
        }
        .pdpWrapper
            #pdp-corsair
            #panel1
            .full_pic
            .slick-dots
            li
            button:before {
            opacity: 1;
            color: #aaa;
        }
        .pdpWrapper #pdp-corsair #panel1 .full_pic .featured .feature_cta {
            cursor: pointer;
            position: absolute;
            width: 10%;
            max-width: 41px;
            opacity: 0.9;
            display: none;
            filter: invert(70%);
            -webkit-filter: invert(70%);
        }
        .pdpWrapper #pdp-corsair #panel1 .full_pic .featured .feature_cta_icon {
            transform: rotate(0deg);
            transition: 250ms transform;
        }
        .pdpWrapper
            .no-touch
            #pdp-corsair
            #panel1
            .full_pic
            .featured
            .feature_cta_icon:hover {
            transform: rotate(135deg);
        }
        .pdpWrapper
            #pdp-corsair
            #panel1
            .full_pic
            .featured
            .feature_cta:before {
            display: block;
            position: absolute;
            top: 49%;
            left: 49%;
            content: '';
            width: 22px;
            height: 22px;
            margin: -11px auto auto -11px;
            -webkit-transform-origin: 50% 50%;
            transform-origin: 50% 50%;
            border-radius: 50%;
            border: 1px solid orange;
            background-color: rgba(0, 0, 0, 0);
            opacity: 1;
            z-index: -1;
            pointer-events: none;
            animation: pulsate 3s cubic-bezier(0.2, 1, 0.2, 1) infinite;
        }
        .pdpWrapper #pdp-corsair #panel1 .full_pic .featured .feature_content {
            position: absolute;
            opacity: 0;
            background: rgba(255, 255, 255, 0.9);
            text-align: left;
            max-width: 300px;
            padding: 0em;
            border-radius: 0px;
            border: solid 1px #eee;
            z-index: -1;
            box-shadow: 2px 2px 10px rgba(50, 50, 50, 0.1);
            transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
            -webkit-transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
            margin-top: -12px;
        }
        .pdpWrapper #pdp-corsair .full_pic .feature_desc {
            padding: 12px;
        }
        .pdpWrapper
            #pdp-corsair
            #panel1
            .full_pic
            .featured
            .feature_content.active {
            margin-top: 0;
            opacity: 1;
            z-index: 999;
            transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
            -webkit-transition: opacity 200ms ease, margin-top 100ms ease,
                z-index 200ms ease;
        }
        .pdpWrapper #pdp-corsair .chosen-container {
            text-align: left;
        }
        .pdpWrapper #pdp-corsair .chosen-container-single .chosen-single {
            background-color: white !important; // Inherited styles from legacy site
            position: relative;
            display: block;
            overflow: hidden;
            outline: none;
            padding: 12px 3.5em 12px 12px;
            height: auto;
            border: 1px solid #eee;
            border-radius: 0;
            font-weight: 400;
            background-clip: none;
            box-shadow: none;
            color: #333;
            text-decoration: none;
            white-space: nowrap;
            line-height: normal;
            letter-spacing: 0.6px;
            margin: 0;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: #f7f8f9;
            background-position: calc(100% - 12px) 13px;
            background-repeat: no-repeat;
            outline: none;
        }
        .pdpWrapper #pdp-corsair #sortby_chosen .chosen-single {
            background-color: #fff;
        }
        .pdpWrapper #pdp-corsair .chosen-container-single .chosen-single div b {
            display: none;
        }
        .pdpWrapper #pdp-corsair .chosen-container .chosen-drop {
            border-left: 1px solid #eee;
            border-right: 1px solid #eee;
            border-bottom: 1px solid #eee;
            border-radius: 0;
            box-shadow: none;
            color: #333;
            text-decoration: none;
            white-space: nowrap;
            letter-spacing: 0.6px;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-color: #fff;
            margin: 0;
            outline: none;
        }
        .pdpWrapper
            #pdp-corsair
            .chosen-container
            .chosen-results
            li.active-result {
            padding: 10px 8px;
        }
        .pdpWrapper
            #pdp-corsair
            .chosen-container
            .chosen-results
            li.highlighted {
            background-color: #999 !important; // Inherited styles from legacy site
            background-image: none !important; // Inherited styles from legacy site
            box-shadow: none !important; // Inherited styles from legacy site
            -webkit-box-shadow: none !important; // Inherited styles from legacy site
        }
        .pdpWrapper
            #pdp-corsair
            .chosen-container-single
            .chosen-search
            input[type='text'] {
            padding: 12px !important; // Inherited styles from legacy site
            border: 1px solid #ddd !important; // Inherited styles from legacy site
            background-image: url('https://assets.corsair.com/image/upload/f_auto,q_auto/akamai/responsive/img/downloads/icon-search.png') !important; // Inherited styles from legacy site
            background-position: calc(100% - 12px) 13px !important; // Inherited styles from legacy site
            background-repeat: no-repeat !important; // Inherited styles from legacy site
        }
        .pdpWrapper #panel2 .sub-nav {
            border: 1px solid #eee;
        }
        .pdpWrapper #panel2 .sub-nav ul {
            padding: 0;
            margin: 0;
        }
        .pdpWrapper #panel2 .sub-nav ul li {
            display: inline-block;
            padding: 24px 1%;
            width: 13.5%;
            text-align: center;
            border: 3px solid #fff;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #panel2 .sub-nav ul li.active {
            border-bottom: 3px solid #333;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #panel2 .sub-nav ul li a {
            font-weight: 500;
            letter-spacing: 0.08em;
            color: #222;
        }
        .pdpWrapper #panel2 .sub-nav ul li a:hover {
            color: #666;
        }

        .pdpWrapper .mainContainer #product-overview {
            -webkit-column-count: 2;
            -moz-column-count: 2;
            column-count: 2;
            -webkit-column-gap: 20px;
            -moz-column-gap: 20px;
            column-gap: 20px;
        }
        .pdpWrapper #panel2 .hiddenContainer {
            display: none;
        }
        .pdpWrapper #panel2 .mainContainer {
            padding: 0 12px 12px;
        }
        .pdpWrapper #panel2 .list-item {
            border-bottom: 1px solid #eee;
            padding: 18px 0;
            color: #666;
            max-width: 90%;
            letter-spacing: 0.08em;
        }
        .pdpWrapper #panel2 .list-item .fa {
            font-size: 18px;
            margin: 0 0.5em;
        }
        .pdpWrapper #panel2 .half #value {
            font-weight: 500;
        }
        .pdpWrapper #panel2 article {
            padding: 30px 0;
            display: none;
        }
        .pdpWrapper #panel2 article:last-child {
            border-bottom: none;
        }
        .pdpWrapper #panel2 article p {
            margin: 0 0 12px;
        }
        .pdpWrapper #panel2 article a {
            color: #333;
            font-size: 12px;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #panel2 article a:hover {
            color: #999;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #panel2 article .featured-image {
            display: inline-block;
            vertical-align: top;
            width: 20%;
            max-width: 300px;
        }
        .pdpWrapper #panel2 article .text {
            display: inline-block;
            vertical-align: top;
            margin-left: 12px;
            width: 75%;
        }
        .pdpWrapper #panel2 article .text h3 {
            margin-top: 2.4px;
            margin-bottom: 4.8px;
            color: #333;
            font-weight: 500;
            font-size: 13.2px;
        }
        .pdpWrapper #panel2 .ui-state-default,
        .pdpWrapper #panel2 .ui-widget-content .ui-state-default,
        .pdpWrapper #panel2 .ui-widget-header .ui-state-default {
            background: #fafafa;
            padding-top: 12px;
            padding-bottom: 12px;
            border: 1px solid #ddd;
            border-radius: 0;
            outline: none;
        }
        .pdpWrapper #panel2 .ui-widget-content {
            color: #333;
            padding-top: 12px;
            padding-bottom: 12px;
            border-bottom: none;
            border-top: none;
            border-radius: 0;
            border-left: none;
            border-right: none;
            background: none;
        }
        .pdpWrapper #panel2 .ui-accordion .ui-accordion-header {
            margin-top: 0.5em;
            color: #111;
            font-weight: 500;
            letter-spacing: 12px;
            background: none;
            padding-top: 14.4px;
            padding-bottom: 12px;
            border: none;
            border-radius: 0;
            border-top: 1px solid #ddd;
            outline: none;
            text-transform: uppercase;
        }
        .pdpWrapper #panel2 .ui-accordion .ui-accordion-header:first-child {
            border-top: none;
        }
        .pdpWrapper #panel2 .mainContainer h3 {
            font-size: 13px;
            letter-spacing: 2.4px;
            font-weight: 500;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav {
            border: 1px solid #eee;
            background-color: #fff;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav ul {
            padding: 0;
            margin: 0;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav ul li {
            display: inline-block;
            padding: 24px 1%;
            text-align: center;
            border: 3px solid #fff;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav ul li.active {
            border-bottom: 3px solid #333;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav ul li a {
            font-weight: 500;
            letter-spacing: 0.08em;
            color: #222;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav ul li a:hover {
            color: #666;
        }
        .pdpWrapper #pdp-corsair .mainContainer #product-overview {
            -webkit-column-count: 2;
            -moz-column-count: 2;
            column-count: 2;
            -webkit-column-gap: 20px;
            -moz-column-gap: 20px;
            column-gap: 20px;
        }
        .pdpWrapper #pdp-corsair #panel2 .hiddenContainer {
            display: none;
        }
        .pdpWrapper #pdp-corsair #panel2 .list-item {
            border-bottom: 0.8px solid #ccc;
            padding: 12px 0;
            color: #333;
            max-width: 90%;
            letter-spacing: 0.08em;
        }
        .pdpWrapper #pdp-corsair #panel2 .list-item .fa {
            font-size: 18px;
            margin: 0 0.5em;
        }
        .pdpWrapper #pdp-corsair #panel2 .half #value {
            font-weight: 500;
            color: #666;
        }
        .pdpWrapper #pdp-corsair #panel2 article {
            padding: 30px 0;
            display: none;
        }
        .pdpWrapper #pdp-corsair #panel2 article:last-child {
            border-bottom: none;
        }
        .pdpWrapper #pdp-corsair #panel2 article p {
            margin: 0 0 12px;
        }
        .pdpWrapper #pdp-corsair #panel2 article a {
            color: #333;
            font-size: 12px;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #pdp-corsair #panel2 article a:hover {
            color: #999;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper #pdp-corsair #panel2 article .featured-image {
            display: inline-block;
            vertical-align: top;
            width: 20%;
            max-width: 300px;
        }
        .pdpWrapper #pdp-corsair #panel2 article .text {
            display: inline-block;
            vertical-align: top;
            margin-left: 12px;
            width: 75%;
        }
        .pdpWrapper #pdp-corsair #panel2 article .text h3 {
            margin-top: 2.4px;
            margin-bottom: 4.8px;
            color: #333;
            font-weight: 500;
            font-size: 13.2px;
        }
        .pdpWrapper #pdp-corsair #panel2 .ui-state-default,
        .pdpWrapper #pdp-corsair #panel2 .ui-widget-content .ui-state-default,
        .pdpWrapper #pdp-corsair #panel2 .ui-widget-header .ui-state-default {
            background: #fafafa;
            padding-top: 12px;
            padding-bottom: 12px;
            border: 1px solid #ddd;
            border-radius: 0;
            outline: none;
        }
        .pdpWrapper #pdp-corsair #panel2 .ui-widget-content {
            color: #333;
            padding-top: 12px;
            padding-bottom: 12px;
            border-bottom: none;
            border-top: none;
            border-radius: 0;
            border-left: none;
            border-right: none;
            background: none;
        }
        .pdpWrapper #pdp-corsair #panel2 .ui-accordionccor-accordion-header {
            margin-top: 0.5em;
            color: #111;
            font-weight: bold;
            letter-spacing: 12px;
            padding-top: 14.4px;
            padding-bottom: 12px;
            border-radius: 0;
            border: 1px solid #ddd;
            outline: none;
            text-transform: uppercase;
            text-align: left;
        }
        .pdpWrapper
            #pdp-corsair
            #panel2
            .ui-accordion
            .ui-accordion-header:first-child {
            border-top: none;
            margin-top: 0px;
        }
        .pdpWrapper .panel5 {
            padding: 18px 0;
        }
        .pdpWrapper .panel5 h2 {
            font-weight: 400;
            letter-spacing: 12px;
        }
        .pdpWrapper .panel5 {
            padding: 20px;
        }
        .pdpWrapper .panel6 .item {
            position: relative;
            display: table;
            height: 25vw;
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/_ui/responsive/common/img/bg_related_products.png')
                no-repeat center / cover;
        }
        .pdpWrapper .panel6 .item:hover .learn-more {
            opacity: 1;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel6 .item:hover .product-name {
            color: #111;
            opacity: 1;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel6 .related-product {
            display: table-cell;
            vertical-align: middle;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .pdpWrapper .panel6 img {
            max-width: 15vw;
            max-height: 15vw;
            width: auto;
            margin: 0 auto;
        }
        .pdpWrapper .panel6 .learn-more {
            width: 100%;
            text-align: center;
            position: absolute;
            top: 45%;
            opacity: 0;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel6 .item .product-name {
            left: 0;
            right: 0;
            text-align: center;
            position: absolute;
            bottom: 3%;
            color: #333;
            letter-spacing: 0.6px;
            font-weight: 500;
            opacity: 0;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel6 .related-product .product-image {
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel6 .related-product:hover img {
            transform: scale(1.1);
            -webkit-transform: scale(1.1);
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel6 .item {
            position: relative;
            display: table;
            height: 25vw;
        }
        .pdpWrapper #pdp-corsair #panel6 .item:hover .learn-more {
            opacity: 1;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel6 .item:hover .product-name {
            color: #111;
            opacity: 1;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel6 .related-product {
            display: table-cell;
            vertical-align: middle;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .pdpWrapper #pdp-corsair #panel6 img {
            max-width: 20vw;
            max-height: 20vw;
            width: auto;
            margin: 0 auto;
        }
        .pdpWrapper #pdp-corsair #panel6 .learn-more {
            width: 100%;
            text-align: center;
            position: absolute;
            top: 45%;
            opacity: 0;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel6 .item .product-name {
            left: 0;
            right: 0;
            text-align: center;
            position: absolute;
            bottom: 8%;
            color: #333;
            letter-spacing: 12px;
            font-weight: 500;
            opacity: 0;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .mfp-wrap .content .half {
            display: inline-block;
            width: 47%;
            vertical-align: top;
        }
        .pdpWrapper #pdp-corsair #panel6 .related-product .product-image {
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper #pdp-corsair #panel6 .related-product:hover img {
            transform: scale(1.1);
            -webkit-transform: scale(1.1);
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .mfp-wrap .content .half img {
            max-width: 90%;
            margin: 12px auto;
        }
        .pdpWrapper .white-popup {
            position: relative;
            background: #fff;
            padding: 20px;
            width: auto;
            max-width: 800px;
            margin: 20px auto;
        }
        .pdpWrapper .mfp-fade.mfp-wrap .mfp-content {
            opacity: 0;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper .mfp-fade.mfp-wrap.mfp-ready .mfp-content {
            opacity: 1;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper .mfp-fade.mfp-wrap.mfp-removing .mfp-content {
            opacity: 0;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper .slick-prev,
        .pdpWrapper .slick-next {
            font-size: 0;
            line-height: 0;
            position: absolute;
            top: 50%;
            display: block;
            width: 20px;
            height: 20px;
            margin-top: -12px;
            padding: 0;
            cursor: pointer;
            color: transparent;
            border: none;
            outline: none;
            background: transparent;
        }
        .pdpWrapper .slick-prev:hover,
        .pdpWrapper .slick-prev:focus,
        .pdpWrapper .slick-next:hover,
        .pdpWrapper .slick-next:focus {
            color: transparent;
            outline: none;
            background: transparent;
        }
        .pdpWrapper .slick-prev:hover:before,
        .pdpWrapper .slick-prev:focus:before,
        .pdpWrapper .slick-next:hover:before,
        .pdpWrapper .slick-next:focus:before {
            opacity: 1;
        }
        .pdpWrapper .slick-prev.slick-disabled:before,
        .pdpWrapper .slick-next.slick-disabled:before {
            opacity: 0.25;
        }
        .pdpWrapper .slick-prev:before,
        .pdpWrapper .slick-next:before {
            font-family: FontAwesome;
            font-size: 24px;
            line-height: 1;
            opacity: 0.75;
            color: #000;
            -webkit-font-smoothing: subpixel-antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .pdpWrapper .thumbs .slick-prev {
            top: -2.5%;
            left: 0;
            right: 0;
            margin: 0 auto;
        }
        .pdpWrapper [dir='rtl'] .thumbs .slick-prev {
            right: -10px;
            left: auto;
        }
        .pdpWrapper .thumbs .slick-prev:before {
            content: '\f0d8';
        }
        .pdpWrapper [dir='rtl'] .thumbs .slick-prev:before {
            content: '\f0d8';
        }
        .pdpWrapper .thumbs .slick-next {
            top: 96%;
            left: 0;
            right: 0;
            margin: 0 auto;
        }
        .pdpWrapper [dir='rtl'] .thumbs .slick-next {
            right: auto;
            left: -2px;
        }
        .pdpWrapper .thumbs .slick-next:before {
            content: '\f0d7';
        }
        .pdpWrapper [dir='rtl'] .thumbs .slick-next:before {
            content: '\f0d7';
        }
        .pdpWrapper .slick-dots {
            position: absolute;
            bottom: -4em;
            display: block;
            width: 100%;
            padding: 0;
            list-style: none;
            text-align: center;
        }
        .pdpWrapper .slick-dots li {
            position: relative;
            display: inline-block;
            width: 20px;
            height: 20px;
            margin: 0px;
            padding: 0;
            cursor: pointer;
        }
        .pdpWrapper .slick-dots li button {
            font-size: 0;
            line-height: 0;
            display: block;
            width: 20px;
            height: 20px;
            padding: 5px;
            cursor: pointer;
            color: transparent;
            border: 0;
            outline: none;
            background: transparent;
        }
        .pdpWrapper .slick-dots li button:hover,
        .pdpWrapper .slick-dots li button:focus {
            outline: none;
        }
        .pdpWrapper .slick-dots li button:hover:before,
        .pdpWrapper .slick-dots li button:focus:before {
            opacity: 1;
        }
        .pdpWrapper .slick-dots li button:before {
            font-family: FontAwesome;
            font-size: 12px;
            line-height: 20px;
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 20px;
            content: '\f111';
            text-align: center;
            opacity: 0.25;
            color: #aaa;
            -webkit-font-smoothing: subpixel-antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .pdpWrapper .slick-dots li.slick-active button:before {
            opacity: 0.75;
            color: #aaa;
        }
        .pdpWrapper #videos {
            padding: 72px 0;
        }
        .pdpWrapper #videos ul {
            padding: 0;
        }
        .pdpWrapper #videos ul li {
            list-style-type: none;
            display: inline-block;
            width: 46%;
            padding: 2%;
        }
        .pdpWrapper #videos ul li a {
            color: #000;
        }
        .pdpWrapper #indabox {
            background: #222;
            padding: 72px 0;
            color: #fff;
        }
        .pdpWrapper #indabox h4,
        .pdpWrapper #indabox h1 {
            font-weight: 400;
            letter-spacing: 12px;
        }
        .pdpWrapper #indabox h4 {
            letter-spacing: 2.4px;
        }
        .pdpWrapper #indabox .subHeader {
            margin: 24px 0 -12px;
            font-weight: 400;
        }
        .pdpWrapper #indabox .half {
            display: inline-block;
            width: 46%;
            padding: 2%;
        }
        .pdpWrapper #indabox ul {
            padding: 12px 24px 3em;
        }
        .pdpWrapper #indabox ul li span {
            line-height: 2;
            font-weight: 100;
            letter-spacing: 0.6px;
            color: #fff;
        }
        .pdpWrapper #indabox ul li {
            color: #fecc2d;
        }
        .pdpWrapper #cart {
            position: fixed;
            top: 0;
            right: -370px;
            width: 370px;
            background: #fff;
            height: 100vh;
            z-index: 11;
            color: #333;
            padding: 3em 0 10px 0;
            -webkit-transition: all 0.3s ease-out;
            -moz-transition: all 0.3s ease-out;
            transition: all 0.35s ease-out;
        }
        .pdpWrapper #cart.active {
            right: 0;
            -webkit-transition: all 0.3s ease-out;
            -moz-transition: all 0.3s ease-out;
            transition: all 0.3s ease-out;
        }
        .pdpWrapper #cart .close {
            position: absolute;
            top: 12px;
            right: 12px;
            cursor: pointer;
            font-size: 24px;
            color: #333;
        }
        .pdpWrapper #cart-overlay {
            position: fixed;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            width: 100vw;
            height: 100vh;
            top: 0;
            left: 0;
            z-index: 10;
        }
        .pdpWrapper #cart .subtotal {
            padding: 12px 0;
            position: absolute;
            bottom: 7em;
            width: 100%;
            left: 0;
            right: 0;
        }
        .pdpWrapper #cart .cta {
            position: absolute;
            bottom: 46px;
            right: 0;
            left: 0;
            margin: 0;
            font-weight: 100;
            letter-spacing: 0.124px;
            text-align: center;
            cursor: pointer;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper #cart .cta:hover {
            background: #333;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper #cart-content li {
            display: inline-block;
            background: #fcfcfc;
            border: 1px solid #eee;
            padding: 10px;
            outline: none;
            margin-bottom: 12px;
        }
        .pdpWrapper #cart-content li h2 {
            font-weight: 500;
        }
        .pdpWrapper #cart-content li .product-thumb {
            display: inline-block;
            width: 100px;
            height: 100px;
            vertical-align: top;
        }
        .pdpWrapper #cart-content li .product-details h2 {
            margin: 5px 0;
        }
        .pdpWrapper #cart-content li .product-details p {
            margin: 5px 0 10px;
        }
        .pdpWrapper #cart-content li .product-details {
            display: inline-block;
            width: 200px;
            padding: 0 0 0 15px;
            vertical-align: top;
        }
        .pdpWrapper #cart-content li a {
            font-size: 9.6px;
            font-weight: 600;
        }
        .pdpWrapper #cart-content {
            padding: 0;
            margin: 30px 0 0 10px;
            overflow-y: scroll;
        }
        .pdpWrapper #pdp-corsair #videos {
            padding: 72px 0;
        }
        .pdpWrapper #pdp-corsair #videos ul {
            padding: 0;
        }
        .pdpWrapper #pdp-corsair #videos ul li {
            list-style-type: none;
            display: inline-block;
            padding: 2%;
        }
        .pdpWrapper #pdp-corsair #videos ul li a {
            color: #000;
        }
        .pdpWrapper #pdp-corsair #indabox {
            background: #222;
            padding: 72px 0;
            color: #fff;
        }
        .pdpWrapper #pdp-corsair #indabox h4,
        .pdpWrapper #pdp-corsair #indabox h1 {
            font-weight: 400;
            letter-spacing: 12px;
        }
        .pdpWrapper #pdp-corsair #indabox h4 {
            letter-spacing: 2.4px;
        }
        .pdpWrapper #pdp-corsair #indabox .subHeader {
            margin: 24px 0 -12px;
            font-weight: 400;
        }
        .pdpWrapper #pdp-corsair #indabox .half {
            display: inline-block;
            width: 46%;
            padding: 2%;
        }
        .pdpWrapper #pdp-corsair #indabox ul {
            padding: 12px 24px 3em;
        }
        .pdpWrapper #pdp-corsair #indabox ul li span {
            line-height: 2;
            font-weight: 100;
            letter-spacing: 0.6px;
            color: #fff;
        }
        .pdpWrapper #pdp-corsair #indabox ul li {
            color: #fecc2d;
        }
        .pdpWrapper #pdp-corsair #cart {
            position: fixed;
            top: 0;
            right: -450px;
            width: 450px;
            background: #fff;
            height: 100vh;
            z-index: 11;
            color: #333;
            padding: 3em 0 10px 0;
            -webkit-transition: all 0.3s ease-out;
            -moz-transition: all 0.3s ease-out;
            transition: all 0.35s ease-out;
        }
        .pdpWrapper #pdp-corsair #cart.active {
            right: 0;
            -webkit-transition: all 0.3s ease-out;
            -moz-transition: all 0.3s ease-out;
            transition: all 0.3s ease-out;
        }
        .pdpWrapper #pdp-corsair #cart .close {
            position: absolute;
            top: 12px;
            right: 12px;
            cursor: pointer;
            font-size: 24px;
            color: #333;
        }
        .pdpWrapper #pdp-corsair #cart-overlay {
            position: fixed;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            width: 100vw;
            height: 100vh;
            top: 0;
            left: 0;
            z-index: 10;
        }
        .pdpWrapper #pdp-corsair #cart .subtotal {
            padding: 12px 0;
            position: absolute;
            bottom: 3em;
            width: 100%;
            left: 0;
            right: 0;
        }
        .pdpWrapper #pdp-corsair #cart .cta {
            position: absolute;
            bottom: 0px;
            right: 0;
            left: 0;
            margin: 0;
            font-weight: 100;
            letter-spacing: 0.124px;
            text-align: center;
            cursor: pointer;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper #pdp-corsair #cart .cta:hover {
            background: #333;
            -webkit-transition: all 0.15s ease-out;
            -moz-transition: all 0.15s ease-out;
            transition: all 0.15s ease-out;
        }
        .pdpWrapper #pdp-corsair #cart-content li {
            display: inline-block;
            background: #fcfcfc;
            border: 1px solid #eee;
            padding: 10px;
            outline: none;
            margin-bottom: 12px;
        }
        .pdpWrapper #pdp-corsair #cart-content li h2 {
            font-weight: 500;
            word-break: break-word;
        }
        .pdpWrapper #pdp-corsair #cart-content li .product-thumb {
            display: inline-block;
            width: 100px;
            height: 100px;
            vertical-align: top;
        }
        .pdpWrapper #pdp-corsair #cart-content li .product-details h2 {
            margin: 5px 0;
        }
        .pdpWrapper #pdp-corsair #cart-content li .product-details p {
            margin: 5px 0 10px;
        }
        .pdpWrapper #pdp-corsair #cart-content li .product-details {
            display: inline-block;
            width: 200px;
            padding: 0 0 0 15px;
            vertical-align: top;
        }
        .pdpWrapper #pdp-corsair #cart-content li a {
            font-size: 9.6px;
            font-weight: 600;
        }
        .pdpWrapper #pdp-corsair #cart-content {
            padding: 0;
            margin: 30px 0 0 10px;
            overflow: auto;
            height: 100%;
            padding-bottom: 170px;
        }
        .pdpWrapper .subtotal.text-center {
            background-color: white;
            font-size: 14.4px;
        }
        .pdpWrapper #cart .cta {
            font-size: 14.4px;
            text-transform: uppercase;
        }
        .pdpWrapper
            #pdp-corsair
            #panel2
            .half
            .list-item
            div#value.package-child {
            padding-left: 20px;
        }
        .pdpWrapper #pdp-panel-hero {
            width: 100%;
            padding: 0;
        }
        .pdpWrapper #pdp-panel-hero .background {
            height: 100%;
            left: 0;
            position: absolute;
            text-align: center;
            top: 0;
            width: 100%;
        }
        .pdpWrapper #pdp-panel-hero.panel-template h1.bebas {
            @apply font-tomorrow;
            font-size: 60px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.75);

            span {
                font-size: inherit;
            }
        }
        .pdpWrapper #pdp-panel-hero video {
            z-index: 0;
            top: 50%;
            left: 50%;
            position: absolute;
            z-index: 0;
            width: 100vw;
            min-height: 105%;
            display: block;
            transform: translate(-50%, -50%);
        }
        .pdpWrapper #pdp-panel-hero .background img {
            height: 100%;
            position: relative;
            z-index: 1;
        }
        .pdpWrapper #pdp-panel-hero .hero-content-container {
            display: table;
            width: 100%;
        }
        .pdpWrapper #pdp-panel-hero.panel-template .wrapper {
            display: table-cell;
            min-height: 0;
            padding: 10vw 0;
            vertical-align: middle;
        }
        .pdpWrapper #pdp-panel-hero.panel-template p {
            @apply font-tomorrow font-semibold;
            font-weight: 400;
            letter-spacing: 0.5em;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.75);
        }
        .pdpWrapper #pdp-panel-hero .gradient-layer {
            left: 0;
            position: absolute;
            height: 100%;
            width: 100%;
            background: rgba(0, 0, 0, 0.25);
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }
        .pdpWrapper #pdp-panel-hero .cta:link,
        .pdpWrapper #pdp-panel-hero .cta:visited {
            @apply bg-yellow;
            color: #000;
            text-shadow: none;
        }
        .pdpWrapper #pdp-panel-hero .cta:hover,
        .pdpWrapper #pdp-panel-hero .cta:active {
            background-color: #e0dd00;
        }
        .pdpWrapper body.in {
            opacity: 1;
            overflow: auto;
        }
        .pdpWrapper section#panel-award {
            min-height: 600px;
        }
        .pdpWrapper #panel-award {
            background: #f9f9f9;
            background: -moz-linear-gradient(
                -45deg,
                rgba(249, 249, 249, 1) 40%,
                rgba(177, 177, 177, 1) 97%
            );
            background: -webkit-linear-gradient(
                -45deg,
                rgba(249, 249, 249, 1) 40%,
                rgba(177, 177, 177, 1) 97%
            );
            background: linear-gradient(
                135deg,
                rgba(249, 249, 249, 1) 40%,
                rgba(177, 177, 177, 1) 97%
            );
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f9f9f9', endColorstr='#b1b1b1',GradientType=1);
            height: 400px;
            min-height: 0;
            padding: 8em 0;
            position: relative;
        }
        .pdpWrapper #panel-award:before {
            background-size: contain;
            content: '';
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            width: 75%;
        }
        .pdpWrapper #panel-award h3 {
            font-size: 1.7em;
            line-height: 1.35em;
        }
        .pdpWrapper #panel-award .text-black {
            color: black;
            font-size: 1.7em;
            font-weight: 600;
            letter-spacing: 0;
            text-transform: none;
        }
        .pdpWrapper #panel-award .text-gray {
            color: #686868;
        }
        .pdpWrapper .container {
            margin-right: auto;
            margin-left: auto;
            padding-left: 10px;
            padding-right: 10px;

            @media (min-width: 640px) {
                width: 100%;
            }
            @media (min-width: 1024px) {
                width: 1024px;
            }
            @media (min-width: 1400px) {
                width: 1400px;
            }
        }
        .pdpWrapper #panel-award .container {
            height: 100%;
            margin: 0 auto;
            max-width: 1200px;
            position: relative;
            text-align: center;
            width: 100%;
        }
        .pdpWrapper #panel-award .half {
            display: inline-block;
            vertical-align: middle;
            width: 50%;
        }
        .pdpWrapper #panel-award .half.left {
            height: 100%;
        }
        .pdpWrapper #panel-award .half.right {
            position: relative;
        }
        .pdpWrapper #panel-award .half.right .background {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/_ui/responsive/common/images/panel-award-quotation.png')
                no-repeat center;
            background-size: contain;
            height: 100%;
            left: 60px;
            position: absolute;
            width: calc(100% - 120px);
        }
        .pdpWrapper #panel-award .half.right .spacer {
            position: relative;
            height: 400px;
        }
        .pdpWrapper #panel-award .half.right .quote {
            left: 50%;
            max-width: 420px;
            opacity: 0;
            pointer-events: none;
            position: absolute;
            top: 0;
            transform: translateX(-50%);
            transition: 1500ms opacity;
            width: 100%;
        }
        .pdpWrapper #panel-award .half.right .quote.selected {
            opacity: 1;
            pointer-events: unset;
        }
        .pdpWrapper #panel-award .half.right .quote .author {
            font-size: 1.25em;
            font-weight: 600;
            letter-spacing: 0.15em;
            padding-top: 25px;
        }
        .pdpWrapper #panel-award .half.right .icon {
            margin: 0 auto 25px;
            position: relative;
            height: 180px;
            width: 180px;
        }
        .pdpWrapper #panel-award .half.right .arrow {
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
            height: 35px;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 35px;
        }
        .pdpWrapper #panel-award .half.right .arrow .hover {
            background: #dcdcdc;
            border-radius: 50%;
            height: 100%;
            position: absolute;
            transform: scale(0);
            transition: 250ms transform;
            width: 100%;
        }
        .pdpWrapper .no-touch #panel-award .half.right .arrow:hover .hover {
            transform: scale(1.1);
        }
        .pdpWrapper #panel-award .arrow .container {
            transform: scale(1);
            transition: 500ms transform, 500ms opacity;
        }
        .pdpWrapper .no-touch #panel-award .arrow#next:hover .container {
            opacity: 1;
            transform: scale(1.5);
        }
        .pdpWrapper .no-touch #panel-award .arrow#prev:hover .container {
            opacity: 1;
            transform: scale(1.5) rotate(180deg);
        }
        .pdpWrapper #panel-award .arrow.prev {
            left: 0;
        }
        .pdpWrapper #panel-award .arrow.next {
            right: 0;
        }
        .pdpWrapper #panel-award .arrow .container {
            background: url('https://res.cloudinary.com/corsair-pwa/image/upload/akamai/_ui/responsive/common/images/panel-award-arrow.png')
                no-repeat center;
            background-size: 50%;
            height: 35px;
            opacity: 0.4;
            padding: 0;
            position: relative;
            width: 35px;
        }
        .pdpWrapper #panel-award .arrow.prev .container {
            transform: rotate(180deg);
        }
        .pdpWrapper #panel-award .arrow .container:before {
            transform: rotate(45deg);
            transform-origin: 100% 0%;
        }
        .pdpWrapper #panel-award .arrow .container:after {
            left: 33%;
            transform: rotate(-45deg);
        }
        .pdpWrapper #panel-award .half.right .dots-bar {
            display: inline-block;
            position: relative;
        }
        .pdpWrapper #panel-award .half.right .dots-bar .dot {
            background: #707070;
            border-radius: 50%;
            cursor: pointer;
            display: inline-block;
            height: 8px;
            margin-right: 9.6px;
            width: 8px;
        }
        .pdpWrapper #panel-award .half.right .dots-bar .dot.selected {
            background: #fdd000;
        }
        .pdpWrapper .testimonial {
            background-size: cover;
            min-height: 400px;
        }
        #pdp .pdpWrapper .panel-template.testimonial h1 {
            border-left: 3px solid #39b5fb;
            @apply font-tomorrow font-semibold;
            font-size: 1.8em;
            font-weight: 400;
            letter-spacing: 0.024px;
            line-height: 1.6;
            margin-left: 15px;
            padding: 15px 0 15px 25px;
        }
        .pdpWrapper .testimonial .endorser {
            font-size: 15.6px;
            font-weight: 100;
            letter-spacing: 12px;
            line-height: 1.6;
            position: relative;
            text-align: right;
        }
        .pdpWrapper .testimonial .endorser-img-mobile {
            display: none;
        }
        @media only screen and (max-width: 1330px) {
            .pdpWrapper #panel1 .full_pic .featured {
                height: 40vw;
            }
            .pdpWrapper #panel1 #details #options #quantity {
                width: 16%;
            }
            .pdpWrapper #pdp-corsair #panel1 .full_pic .featured {
                height: 40vw;
            }
            .pdpWrapper #pdp-corsair #panel1 #details #options #quantity {
                width: 16%;
            }
        }
        @media only screen and (max-width: 1200px) {
            .pdpWrapper #pdp {
                font-size: 11px;
            }
            .pdpWrapper #panel1 .wrapper {
                margin-top: 3em;
            }
            .pdpWrapper #panel1 #quantity {
                height: 21px;
            }
            .pdpWrapper #panel1 h1 small {
                font-size: 30%;
            }
            .pdpWrapper #panel1 #details {
                margin-left: 78px;
            }
            .pdpWrapper #panel1 #colors ul {
                padding: 0em 0 0.5em;
            }
            .pdpWrapper #panel1 #colors li {
                border: 1px solid #ccc;
                width: 10px;
                height: 10px;
            }
            .pdpWrapper #panel1 .cta-container {
                padding: 0 !important; // Inherited styles from legacy site
            }
            .pdpWrapper .no-touch #panel1 #colors li:hover,
            .pdpWrapper #panel1 #colors .active {
                border: 1px solid black !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 #details #options #quantity {
                font-size: 12px;
                height: 15px;
            }
            .pdpWrapper #panel1 #details #product-details .deal {
                margin-bottom: 0;
            }
            .pdpWrapper #panel1 #details #product-details .price {
                margin-bottom: 0;
            }
            .pdpWrapper #panel1 #details #product-details .discount {
                margin-top: -13px;
                font-size: 1.8em !important; // Inherited styles from legacy site
            }
            .pdpWrapper .chosen-container {
                font-size: 12px !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair {
                font-size: 11px;
            }
            .pdpWrapper #pdp-corsair h1 {
                font-size: 18px;
                margin: 0 auto;
            }
            .pdpWrapper #pdp-corsair #panel1 .wrapper {
                margin-top: 3em;
            }
            .pdpWrapper #pdp-corsair #panel1 #quantity {
                height: 21px;
            }
            .pdpWrapper #pdp-corsair #panel1 h1 small {
                font-size: 30%;
            }
            .pdpWrapper #pdp-corsair #panel1 #details {
                margin-left: 78px;
            }
            .pdpWrapper #pdp-corsair #panel1 #colors ul {
                padding: 0em 0 0.5em;
            }
            .pdpWrapper #pdp-corsair #panel1 #colors li {
                border: 1px solid #ccc;
                width: 10px;
                height: 10px;
            }
            .pdpWrapper #pdp-corsair #panel1 .cta-container {
                padding: 0 !important; // Inherited styles from legacy site
            }
            .pdpWrapper .no-touch #pdp-corsair #panel1 #colors li:hover,
            .pdpWrapper #pdp-corsair #panel1 #colors .active {
                border: 1px solid black !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 #details #options #quantity {
                font-size: 12px;
                height: 15px;
            }
            .pdpWrapper #pdp-corsair #panel1 #details #product-details .deal {
                margin-bottom: 0;
            }
            .pdpWrapper #pdp-corsair #panel1 #details #product-details .price {
                margin-bottom: 0;
            }
            .pdpWrapper
                #pdp-corsair
                #panel1
                #details
                #product-details
                .discount {
                margin-top: -13px;
                font-size: 1.8em !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair .chosen-container {
                font-size: 12px !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-panel-hero.panel-template .wrapper {
                padding-top: 5vw 0;
            }
            .pdpWrapper .panel-template .position-absolute.fromCenter {
                top: 50%;
            }
            .pdpWrapper #pdp-panel-hero .hero-content-container h1 {
                font-size: 60px !important; // Inherited styles from legacy site
            }
        }
        @media only screen and (max-width: 992px) {
            .pdpWrapper #pdp {
                font-size: 10px;
            }
            .pdpWrapper h1 {
                font-size: 3em;
            }
            .pdpWrapper #panel1 h1 {
                font-size: 3em !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 .wrapper {
                margin-top: 24px;
            }
            .pdpWrapper #panel1 #details {
                margin-left: 0;
            }
            .pdpWrapper #panel1 #details #product-details #product-name {
                font-size: 0.7em !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 #details #options #quantity {
                height: 13px;
            }
            .pdpWrapper #panel1 #product_excerpt {
                font-size: 0.9em;
                max-width: 460px;
            }
            .pdpWrapper #panel6 .item {
                height: 33vw;
            }
            .pdpWrapper p.centered {
                text-align: left;
            }
            .pdpWrapper #panel1 #wishlist {
                border-right: 0;
                padding-bottom: 12px;
            }
            .pdpWrapper #pdp-corsair {
                font-size: 10px;
            }
            .pdpWrapper #pdp-corsair #panel1 h1 {
                font-size: 3em !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 .wrapper {
                margin-top: 24px;
            }
            .pdpWrapper #pdp-corsair #panel1 #details {
                margin-left: 0;
            }
            .pdpWrapper
                #pdp-corsair
                #panel1
                #details
                #product-details
                #product-name {
                font-size: 0.7em !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 #details #options #quantity {
                height: 13px;
            }
            .pdpWrapper #pdp-corsair #panel1 #product_excerpt {
                font-size: 13.2px;
                max-width: 460px;
            }
            .pdpWrapper #pdp-corsair #panel6 .item {
                height: 33vw;
            }
            .pdpWrapper #pdp-corsair p.centered {
                text-align: left;
            }
            .pdpWrapper #pdp-corsair #panel1 #wishlist {
                border-right: 0;
                padding-bottom: 12px;
            }
            .pdpWrapper #panel2 .sub-nav .wrapper {
                margin: 0;
                width: 100%;
            }
            .pdpWrapper #panel1 .show-on-mobile {
                display: block;
            }
            .pdpWrapper #panel1 .hide-on-mobile {
                display: none;
            }
            .pdpWrapper #gallery-container {
                max-height: inherit !important; // Inherited styles from legacy site
            }
            .pdpWrapper .thumbs {
                display: none !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 .full_pic {
                margin-top: 5%;
                margin-left: 0px;
                margin-right: 7%;
            }
            .pdpWrapper #panel1 .item .media-content {
                height: 50vw;
            }
            .pdpWrapper #panel1 .full_pic .featured {
                height: 50vw;
            }
            .pdpWrapper #panel1 .half {
                width: 100%;
                height: auto !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 .thumbs {
                display: none;
            }
            .pdpWrapper #panel2 article .featured-image {
                width: 40%;
            }
            .pdpWrapper #panel2 article .text {
                width: 55%;
            }
            .pdpWrapper #pdp-corsair #panel1 .show-on-mobile {
                display: block;
            }
            .pdpWrapper #pdp-corsair #panel1 .hide-on-mobile {
                display: none;
            }
            .pdpWrapper #pdp-corsair #gallery-container {
                max-height: inherit !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair .thumbs {
                display: none !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 .full_pic {
                margin-top: 5%;
                margin-left: 0px;
                margin-right: 7%;
            }
            .pdpWrapper #pdp-corsair #panel1 .item .media-content {
                height: 50vw;
            }
            .pdpWrapper #pdp-corsair #panel1 .full_pic .featured {
                height: 50vw;
            }
            .pdpWrapper #pdp-corsair #panel1 .half {
                width: 100%;
                height: auto !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 .thumbs {
                display: none;
            }
            .pdpWrapper #pdp-corsair #panel2 article .featured-image {
                width: 40%;
            }
            .pdpWrapper #pdp-corsair #panel2 article .text {
                width: 55%;
            }
            .pdpWrapper #pdp-panel-hero h1 {
                font-size: 4em !important; // Inherited styles from legacy site
            }
            .pdpWrapper section#panel-award {
                padding: 0;
            }
            .pdpWrapper #panel-award:before {
                background-size: contain;
                width: 100%;
            }
            .pdpWrapper #panel-award .half {
                width: calc(50% - 10px);
            }
            .pdpWrapper #panel-award .half.left {
                width: calc(100% - 478px);
            }
            .pdpWrapper #panel-award .half.right {
                width: 478px;
            }
            .pdpWrapper #panel-award .half.right .quote {
                max-width: 400px;
            }
            .pdpWrapper .testimonial {
                min-height: 0;
            }
        }
        @media only screen and (max-width: 768px) {
        }
        @media only screen and (max-width: 650px) {
            .pdpWrapper #pdp-panel-hero.panel-template .wrapper {
                padding: 2vw 0;
            }
        }
        @media only screen and (max-width: 600px) {
            .pdpWrapper section#panel-award {
                min-height: 125vw;
            }
        }
        @media only screen and (max-width: 570px) {
            .pdpWrapper #pdp-panel-hero .hero-content-container {
                padding: 10vw 0;
            }
            .pdpWrapper #pdp-panel-hero h1 {
                font-size: 4.5em !important; // Inherited styles from legacy site
                margin: 0 auto;
            }
            .pdpWrapper #pdp-panel-hero p {
                font-size: 18px;
                margin: 24px auto;
                width: calc(100% - 20px);
            }
            .pdpWrapper #pdp-panel-hero video {
                width: 100vh;
            }
            .pdpWrapper #pdp-panel-hero.panel-template .wrapper {
                padding: 2vw 0;
            }
        }
        @media only screen and (max-width: 479px) {
            .pdpWrapper #panel1 #options .half {
                width: 100%;
            }
            .pdpWrapper #panel1 .full_pic .featured .feature_cta {
                display: none !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel6 .item {
                height: 100vw;
            }
            .pdpWrapper #panel6 img {
                max-width: 90vw;
                max-height: 90vw;
                width: auto;
            }
            .pdpWrapper #pdp-corsair #panel1 #options .half {
                width: 100%;
            }
            .pdpWrapper #pdp-corsair #panel1 .full_pic .featured .feature_cta {
                display: none !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel6 .item {
                height: 100vw;
            }
            .pdpWrapper #pdp-corsair #panel6 img {
                max-width: 90vw;
                max-height: 90vw;
                width: auto;
            }
            .pdpWrapper #pdp-panel-hero {
                min-height: 0;
            }
            .pdpWrapper #pdp-panel-hero.panel-template .wrapper {
                padding: 0;
                vertical-align: top;
            }
        }
        @media only screen and (max-width: 400px) {
            .pdpWrapper #panel-award .half.right {
                width: 330px;
            }
            .pdpWrapper #panel-award .half.right .quote {
                max-width: 260px;
            }
        }
        .pdpWrapper .panel-template {
            color: #555;
            z-index: 1;
            position: relative;
            padding: 10vw 0 10vw;
            overflow: hidden;
        }
        .pdpWrapper .panel-template.fivepadding {
            padding: 0;
        }
        .pdpWrapper .panel-template.fivepadding {
            padding: 5vw 0 5vw;
        }
        .pdpWrapper .panel-template .wrapper {
            position: relative;
        }
        .pdpWrapper .panel-template .col-half {
            width: 43.5%;
            display: inline-block;
            vertical-align: middle;
            padding: 3%;
        }
        .pdpWrapper .panel-template .col-third {
            width: 28.9%;
            display: inline-block;
            vertical-align: top;
            padding: 2%;
        }
        .pdpWrapper .panel-template .offset-half {
            margin-left: 49.9%;
        }
        .pdpWrapper .panel-template .offset-third {
            margin-left: 32.9%;
        }
        .pdpWrapper .panel-template .overlay {
            display: none;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
        }
        .pdpWrapper .panel-template .overlay.white {
            background: rgba(255, 255, 255, 0.6);
        }
        .pdpWrapper .panel-template .overlay.dark {
            background: rgba(0, 0, 0, 0.6);
        }
        .pdpWrapper .panel-template .img-swap {
            margin: 0 0 0 5%;
            padding: 0;
        }
        .pdpWrapper .panel-template .img-swap li {
            display: inline-block;
            list-style-type: none;
            width: 25%;
        }
        .pdpWrapper .panel-template .img-swap li a {
            opacity: 0.5;
            transition: 250ms opacity;
        }
        .pdpWrapper .panel-template .img-swap li a:hover,
        .pdpWrapper .panel-template .img-swap li a.selected {
            opacity: 1;
        }
        .pdpWrapper .panel-template .img-swap li img {
            width: 100%;
        }
        .pdpWrapper .panel-template .img-swap-container {
            position: relative;
        }
        .pdpWrapper .panel-template .img-swap-container img {
            margin: 4em auto;
            width: 100%;
        }
        .pdpWrapper .panel-template .img-swap-container .spacer {
            opacity: 0;
        }
        .pdpWrapper .panel-template .img-swap-container .option {
            opacity: 0;
            position: absolute;
            left: 0;
            top: 0;
            transition: 250ms opacity;
        }
        .pdpWrapper .panel-template .img-swap-container .option.show {
            opacity: 1;
        }
        .pdpWrapper .panel-template .product-features {
            position: relative;
            width: 100%;
            height: 500px;
        }
        .pdpWrapper .panel-template .product-features .product {
            max-width: 90%;
            margin: 0 auto;
            padding: 5%;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            opacity: 1;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .feature {
            position: absolute;
            opacity: 0;
            width: 300px;
            max-width: 100%;
            background: rgba(255, 255, 255, 1);
            list-style-type: none;
            border-radius: 0;
            border: 1px solid #ddd;
            line-height: 1.5;
            border-radius: 4px;
            margin-left: -12px;
            transition: opacity 500ms ease, margin-left 500ms ease;
            -webkit-transition: opacity 500ms ease, margin-left 500ms ease;
            line-height: 1.5;
            font-size: 0.9em;
            color: #666;
            padding: 18px 24px;
            text-align: left;
            font-weight: 400;
            letter-spacing: 0.08em;
            font-weight: 400;
        }
        .pdpWrapper .panel-template .product-features .feature.active {
            opacity: 1;
            margin-left: 0;
            transition: opacity 500ms ease, margin-left 500ms ease;
            -webkit-transition: opacity 500ms ease, margin-left 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .close {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 10px;
            cursor: pointer;
            display: none;
        }
        .pdpWrapper .panel-template .product-features .expand_feature {
            position: absolute;
            width: 3%;
            min-width: 35px;
            opacity: 0.25;
            cursor: pointer;
            z-index: 999;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .expand_feature:hover {
            opacity: 0.9;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .expand_feature.active {
            transform: rotate(
                45deg
            ) !important; // Inherited styles from legacy site
            -webkit-transform: rotate(
                45deg
            ) !important; // Inherited styles from legacy site
            opacity: 0.9;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        @media only screen and (max-width: 1200px) {
            .pdpWrapper .panel-template .product-features {
                height: 50vw;
                margin-top: 12px;
            }
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper .panel-template .product-features .product {
                max-width: 100%;
                padding: 5% 0;
            }
        }
        @media only screen and (max-width: 480px) {
            .pdpWrapper .panel-template .product-features .feature {
                top: 5% !important; // Inherited styles from legacy site
                width: 89%;
                padding: 10% 5%;
                background: rgba(255, 255, 255, 0.95);
                left: 0 !important; // Inherited styles from legacy site
            }
            .pdpWrapper .panel-template .product-features .feature.active {
                z-index: 999999;
            }
            .pdpWrapper .panel-template .product-features .close {
                display: block;
            }
        }
        .pdpWrapper .panel-template .white {
            color: #fff;
        }
        .pdpWrapper .panel-template h1 small {
            font-size: 0.72px;
            line-height: 1.2;
        }
        .pdpWrapper .panel-template h1,
        .pdpWrapper .panel-template h2 {
            @apply font-tomorrow;
            font-size: 3.75rem;
            font-weight: 600;
            letter-spacing: 0.05rem;
            line-height: 3.75rem;
            text-transform: uppercase !important; // Inherited styles from legacy site
        }
        .pdpWrapper .panel-template h3 {
            font-size: 13.2px;
            letter-spacing: 2.4px;
            font-weight: 500;
            margin: 24px 0;
        }
        .pdpWrapper .panel-template p {
            @apply font-tomorrow font-semibold;
            margin: 24px auto !important;
            letter-spacing: 0.6px;
        }
        .pdpWrapper .panel-template a {
            color: #000;
        }
        .pdpWrapper .panel-template a:hover {
            color: #000;
        }
        .pdpWrapper .panel-template .white p {
            font-weight: 100;
        }
        .pdpWrapper .panel-template .white a {
            color: #fff;
        }
        .pdpWrapper .panel-template .white a:hover {
            color: #ccc;
        }
        .pdpWrapper .panel-template .img-responsive {
            max-width: 100%;
        }
        .pdpWrapper .panel-template .img-absolute {
            position: absolute;
        }
        .pdpWrapper .panel-template img.half {
            width: 46%;
            max-width: 100%;
        }
        .pdpWrapper .panel-template .embed-video {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            max-width: 100%;
        }
        .pdpWrapper .panel-template .embed-video iframe,
        .pdpWrapper .panel-template .embed-video object,
        .pdpWrapper .panel-template .embed-video embed {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .pdpWrapper .variant-name {
            display: none;
            padding-top: 10px;
        }
        .pdpWrapper .chosen-container {
            padding-top: 10px;
        }
        .pdpWrapper .footer-container .chosen-container {
            padding-top: 0px;
        }
        @-webkit-keyframes pulsate {
            from {
                opacity: 0.75;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(3);
            }
        }
        .pdpWrapper #pdp-corsair {
            margin-top: -74px;
        }
        .pdpWrapper .glyphicon-eye-close:before {
            content: unset;
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper .panel-template .show-on-mobile {
                display: block !important; // Inherited styles from legacy site
            }
            .pdpWrapper .panel-template .hide-on-mobile {
                display: none;
            }
            .pdpWrapper .panel-template .col-third,
            .pdpWrapper .panel-template .col-half {
                width: 100%;
                margin: 12px 0;
                padding: 0;
            }
            .pdpWrapper .panel-template .wrapper {
                display: table;
            }
            .pdpWrapper .panel-template .col-half.bottom {
                display: table-footer-group;
            }
            .pdpWrapper .panel-template .col-half.top {
                display: table-header-group;
            }
            .pdpWrapper .panel-template .offset-half {
                margin-left: 0;
            }
            .pdpWrapper .panel-template .stack-on-mobile {
                position: relative;
                width: 100% !important; // Inherited styles from legacy site
            }
            .pdpWrapper .panel-template .stack-on-mobile.right {
                float: right;
            }
            .pdpWrapper #pdp-corsair #panel1 #gallery-container {
                margin: 0 -10% 5%;
                padding: 10% 10% 5%;
            }
        }
        .pdpWrapper #gallery-container {
            -webkit-box-sizing: unset;
            -moz-box-sizing: unset;
            box-sizing: unset;
        }
        .pdpWrapper h3,
        .pdpWrapper .h3 {
            font-size: unset;
            text-transform: uppercase;
        }
        .pdpWrapper .product-fixed-width-wrapper {
            max-width: 780px;
            margin: 0px auto;
        }
        .pdpWrapper .AddToCart-ShareOnSocialNetworkAction {
            display: none;
        }
        .pdpWrapper .bundle * {
            all: unset;
        }
        .pdpWrapper .bundle .promotion {
            color: black;
            @apply font-tomorrow font-semibold;
            font-weight: 600;
            font-size: 9.6px;
            text-transform: uppercase;
            letter-spacing: 12px;
        }
        .pdpWrapper .promotion {
            display: block !important; // Inherited styles from legacy site
        }
        .pdpWrapper .stock-wrapper {
            display: none;
        }
        .pdpWrapper .btn-icon:after {
            border-right: unset;
        }
        .pdpWrapper .glyphicon-shopping-cart:before,
        .pdpWrapper .glyphicon-envelope:before {
            content: unset;
        }
        .pdpWrapper .btn.btn-primary {
            @apply bg-yellow;
            font-weight: 400;
            font-size: 14px;
            letter-spacing: 1.35px;
            color: #000;
            margin-bottom: 1rem !important; // Inherited styles from legacy site
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
            -webkit-text-fill-color: black;
            white-space: normal !important; // Inherited styles from legacy site
            margin-top: 2rem;
            border: unset;
        }
        .pdpWrapper .btn.btn-primary:hover {
            background: #e1da00;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
            box-shadow: 0px 3px 8px 0px #000;
        }
        .pdpWrapper .btn.btn-primary.learn-more-btn:hover {
            background: #ccc;
        }
        .pdpWrapper div#label.half {
            opacity: 0.5;
            color: #666;
        }
        .pdpWrapper .text-center {
            text-align: center;
            img {
                margin: auto;
            }
        }
        .pdpWrapper .yellow {
            @apply text-yellow;
        }
        .pdpWrapper section {
            position: relative;
        }
        .pdpWrapper .show-on-mobile {
            display: none;
        }
        .pdpWrapper section h1,
        .pdpWrapper section h2 {
            text-transform: uppercase;
            letter-spacing: 0.6px;
        }
        .pdpWrapper section h1 {
            font-size: 1.8em;
            line-height: 1;
        }
        .pdpWrapper section h3 {
            font-weight: 400;
        }
        .pdpWrapper section .wrapper {
            position: relative;
        }
        .pdpWrapper .item,
        .pdpWrapper .item a,
        .pdpWrapper .item a img {
            outline: none;
        }
        .pdpWrapper .cta,
        .pdpWrapper .mfp-wrap .cta {
            padding: 12px 24px;
            font-weight: 400;
            font-size: 12px;
            letter-spacing: 0.124px;
            display: inline-block;
            background: #000;
            color: #fff;
            margin: 12px 0;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper .cta.black {
            background: #000;
            color: #fff;
            font-weight: 100;
        }
        .pdpWrapper .cta.black:hover {
            background: #333;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper .cta:hover .mfp-wrap .cta:hover {
            background: #000;
            color: #fff;
            font-weight: 400;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;
        }
        .pdpWrapper .content-left,
        .pdpWrapper .content-right {
            padding: 8vw 0 9vw;
            min-height: 15vw;
            width: 46.5%;
            display: inline-block;
            vertical-align: middle;
        }
        .pdpWrapper .content-right {
            padding-left: 1%;
            margin-left: 52%;
            text-align: right;
        }
        .pdpWrapper .content-right p {
            font-weight: 100;
        }
        .pdpWrapper .half {
            width: 47%;
            display: inline-block;
            vertical-align: top;
            position: relative;
        }
        .pdpWrapper #indabox {
            background: #222;
            padding: 72px 0;
            color: #fff;
        }
        .pdpWrapper #indabox h4,
        .pdpWrapper #indabox h1 {
            font-weight: 400;
            letter-spacing: 12px;
        }
        .pdpWrapper #indabox h4 {
            letter-spacing: 2.4px;
        }
        .pdpWrapper #indabox .subHeader {
            margin: 24px 0 -12px;
            font-weight: 400;
        }
        .pdpWrapper #indabox .half {
            display: inline-block;
            width: 46%;
            padding: 2%;
        }
        .pdpWrapper #indabox ul {
            padding: 12px 24px 3em;
        }
        .pdpWrapper #indabox ul li span {
            line-height: 2;
            font-weight: 100;
            letter-spacing: 0.6px;
            color: #fff;
        }
        .pdpWrapper #indabox ul li {
            color: #fecc2d;
        }
        @media only screen and (max-width: 1200px) {
            .pdpWrapper #pdp {
                font-size: 11px;
            }
        }
        @media only screen and (max-width: 992px) {
            .pdpWrapper #pdp {
                font-size: 10px;
            }
            .pdpWrapper p.centered {
                text-align: left;
            }
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper .show-on-mobile {
                display: block;
            }
            .pdpWrapper .hide-on-mobile {
                display: none;
            }
            .pdpWrapper .half {
                width: 100%;
                height: auto !important; // Inherited styles from legacy site
            }
            .pdpWrapper .content-left,
            .pdpWrapper .content-right {
                width: 100%;
            }
            .pdpWrapper #videos ul li,
            .pdpWrapper #indabox .half {
                width: 96%;
            }
            .pdpWrapper .content-right {
                margin-left: 0;
                text-align: left;
            }
        }
        .pdpWrapper .panel-template .product-features {
            position: relative;
            width: 100%;
            height: 500px;
        }
        .pdpWrapper .panel-template .product-features .product {
            max-width: 90%;
            margin: 0 auto;
            padding: 5%;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            opacity: 1;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .feature {
            position: absolute;
            opacity: 0;
            width: 300px;
            max-width: 100%;
            background: rgba(255, 255, 255, 1);
            list-style-type: none;
            border-radius: 0em;
            border: 1px solid #ddd;
            line-height: 1.5;
            border-radius: 4px;
            margin-left: -12px;
            transition: opacity 500ms ease, margin-left 500ms ease;
            -webkit-transition: opacity 500ms ease, margin-left 500ms ease;
            line-height: 1.5;
            font-size: 0.9em;
            color: #666;
            padding: 18px 24px;
            text-align: left;
            font-weight: 400;
            letter-spacing: 0.08em;
            font-weight: 400;
        }
        .pdpWrapper .panel-template .product-features .feature.active {
            opacity: 1;
            margin-left: 0;
            transition: opacity 500ms ease, margin-left 500ms ease;
            -webkit-transition: opacity 500ms ease, margin-left 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .close {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 10px;
            cursor: pointer;
            display: none;
        }
        .pdpWrapper .panel-template .product-features .expand_feature {
            position: absolute;
            width: 3%;
            min-width: 35px;
            opacity: 0.25;
            cursor: pointer;
            z-index: 999;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .expand_feature:hover {
            opacity: 0.9;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        .pdpWrapper .panel-template .product-features .expand_feature.active {
            transform: rotate(
                45deg
            ) !important; // Inherited styles from legacy site
            -webkit-transform: rotate(
                45deg
            ) !important; // Inherited styles from legacy site
            opacity: 0.9;
            transition: all 500ms ease;
            -webkit-transition: all 500ms ease;
        }
        @media only screen and (max-width: 1200px) {
            .pdpWrapper .panel-template .product-features {
                height: 50vw;
                margin-top: 12px;
            }
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper .panel-template .product-features .product {
                max-width: 100%;
                padding: 5% 0;
            }
        }
        @media only screen and (max-width: 480px) {
            .pdpWrapper .panel-template .product-features .feature {
                top: 5% !important; // Inherited styles from legacy site
                width: 89%;
                padding: 10% 5%;
                background: rgba(255, 255, 255, 0.95);
                left: 0 !important; // Inherited styles from legacy site
            }
            .pdpWrapper .panel-template .product-features .feature.active {
                z-index: 999999;
            }
            .pdpWrapper .panel-template .product-features .close {
                display: block;
            }
        }
        .pdpWrapper .panel-template .white {
            color: #fff;
        }
        .pdpWrapper .panel-template h1 small {
            font-size: 0.72px;
            line-height: 1.2;
        }
        .pdpWrapper .panel-template h3 {
            font-size: 13.2px;
            letter-spacing: 2.4px;
            font-weight: 500;
            margin: 24px 0;
        }
        .pdpWrapper .panel-template p {
            @apply font-tomorrow font-semibold;
            margin: 24px 0;
            letter-spacing: 0.6px;
        }
        .pdpWrapper .panel-template a {
            @apply font-tomorrow font-semibold;
            color: #000;
        }
        .pdpWrapper .panel-template a:hover {
            color: #000;
        }
        .pdpWrapper .panel-template .white a {
            color: #fff;
        }
        .pdpWrapper .panel-template .white a:hover {
            color: #ccc;
        }
        .pdpWrapper .panel-template .img-responsive {
            max-width: 100%;
        }
        .pdpWrapper .panel-template .img-absolute {
            position: absolute;
        }
        .pdpWrapper .panel-template img.half {
            width: 46%;
            max-width: 100%;
        }
        .pdpWrapper .panel-template .embed-video {
            position: relative;
            padding-bottom: 56.25%;
            height: 0;
            overflow: hidden;
            max-width: 100%;
        }
        .pdpWrapper .panel-template .embed-video iframe,
        .pdpWrapper .panel-template .embed-video object,
        .pdpWrapper .panel-template .embed-video embed {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper .mobilewhite {
                color: #fff;
            }
            .pdpWrapper .panel-template .show-on-mobile {
                display: block !important; // Inherited styles from legacy site
            }
            .pdpWrapper .panel-template .hide-on-mobile {
                display: none;
            }
            .pdpWrapper .panel-template .col-third,
            .pdpWrapper .panel-template .col-half {
                width: 100%;
                margin: 1em 0;
                padding: 0;
            }
            .pdpWrapper .panel-template .wrapper {
                display: table;
            }
            .pdpWrapper .panel-template .col-half.bottom {
                display: table-footer-group;
            }
            .pdpWrapper .panel-template .col-half.top {
                display: table-header-group;
            }
            .pdpWrapper .panel-template .offset-half {
                margin-left: 0;
            }
            .pdpWrapper .panel-template .stack-on-mobile {
                position: relative;
                width: 100% !important; // Inherited styles from legacy site
            }
            .pdpWrapper .panel-template .stack-on-mobile.right {
                float: right;
            }
        }
        .pdpWrapper #pdp-corsair ul > li > a:focus {
            text-decoration: none;
        }
        .pdpWrapper * {
            -webkit-box-sizing: border-box; // Inherited styles from legacy site
            -moz-box-sizing: border-box; // Inherited styles from legacy site
            box-sizing: border-box; // Inherited styles from legacy site
        }
        .pdpWrapper #pdp-corsair #panel1 {
            padding: 80px 60px;
        }
        .pdpWrapper .panel-template .img-responsive {
            max-width: 100%;
            display: inline;
        }
        .pdpWrapper #pdp-corsair #indabox .half {
            width: 49%;
            padding: 0 0 0 10%;
        }
        .pdpWrapper .panel-template .col-half {
            vertical-align: middle;
        }
        .pdpWrapper #pdp-corsair #indabox h4 {
            letter-spacing: 2.4px;
            padding-bottom: 12px;
            text-transform: uppercase;
            font-size: 14px;
        }
        .pdpWrapper #pdp-corsair #indabox ul {
            padding: 0em 24px;
        }
        .pdpWrapper #pdp-corsair #indabox .linepadd {
            padding: 0 0 0 8%;
        }
        .pdpWrapper .sticky_addtocart h2#price {
            margin-top: 0px;
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper #pdp-corsair #videos ul li,
            .pdpWrapper #pdp-corsair #indabox .half {
                width: 96%;
            }
            .pdpWrapper #pdp-corsair #panel1 #product-details {
                text-align: left;
            }
            .pdpWrapper #pdp-corsair .slick-track {
                max-height: none;
            }
            .pdpWrapper #pdp-corsair #panel1 h1 {
                font-size: 30px !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 #product-details {
                max-width: 480px;
            }
            .pdpWrapper #pdp-corsair .btn.btn-primary {
                font-size: 14.4px;
            }
            .pdpWrapper #pdp-corsair #panel1 {
                padding: 70px 60px !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 .wrapper {
                margin-top: 0;
            }
        }
        .pdpWrapper
            #pdp-corsair
            > #panel2
            > .content
            > .mainContainer
            > .wrapper {
            padding: 30px 0;
        }
        .pdpWrapper
            #pdp-corsair
            #panel2
            .mainContainer
            .product-classifications
            .wrapper {
            padding: 30px 0;
        }
        .pdpWrapper .panel6 .item .product-name {
            left: 5%;
            right: 5%;
            font-size: 14.4px;
            bottom: 5%;
        }
        .pdpWrapper .panel6 .slick-prev,
        .pdpWrapper .panel6 .slick-next {
            top: 45%;
            margin: 0 10px !important; // Inherited styles from legacy site
            -webkit-filter: invert(
                100%
            ) !important; // Inherited styles from legacy site
            filter: invert(
                100%
            ) !important; // Inherited styles from legacy site
            width: 20px !important; // Inherited styles from legacy site
            height: auto !important; // Inherited styles from legacy site
            z-index: 1;
        }
        .pdpWrapper .panel6 .slick-prev {
            left: 0;
            transform: rotate(180deg);
            -webkite-transform: rotate(180deg);
        }
        .pdpWrapper .panel6 .slick-next {
            right: 0;
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper .panel6 img {
                max-width: 43%;
                max-height: 40vw;
            }
            .pdpWrapper .panel6 .item {
                height: 50vw;
            }
        }
        .pdpWrapper .thumbs .slick-prev {
            top: -7.5%;
        }
        .pdpWrapper .pageType-ProductPage .feature_content .feature_desc h4 {
            font-size: 12px;
            font-weight: 600;
        }
        .pdpWrapper .pageType-ProductPage #panel1 #mobile-bar-container {
            margin: 1em auto 0;
            text-align: center;
            width: 90%;
        }
        .pdpWrapper .pageType-ProductPage #panel1 #mobile-bar-container .arrow {
            display: inline-block;
            font-size: 0;
            vertical-align: middle;
            width: 30px;
        }
        .pdpWrapper
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            .arrow.next {
            margin-left: 4%;
        }
        .pdpWrapper
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            .arrow.prev:after {
            content: '\f0d9';
            color: #000;
            display: block;
            font-family: FontAwesome;
            font-size: 35px;
            margin: 0 auto;
            position: relative;
            text-align: center;
            transition: 250ms color;
        }
        .pdpWrapper
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            .arrow.next:after {
            content: '\f0da';
            color: #000;
            display: block;
            font-family: FontAwesome;
            font-size: 35px;
            margin: 0 auto;
            position: relative;
            text-align: center;
            transition: 250ms color;
        }
        .pdpWrapper
            .no-touch
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            .arrow:hover:after {
            color: #777;
        }
        .pdpWrapper
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            #mobile-bar {
            display: inline-block;
            height: 4px;
            margin: 0 auto 0 4%;
            position: relative;
            vertical-align: middle;
            width: 60%;
        }
        .pdpWrapper
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            #mobile-bar
            .full {
            background: #d6d6d6;
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0);
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            width: 100%;
        }
        .pdpWrapper
            .pageType-ProductPage
            #panel1
            #mobile-bar-container
            #mobile-bar
            .current {
            background: gray;
            border-radius: 4px;
            border: 1px solid gray;
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            transition: 500ms width;
        }
        .pdpWrapper .bundleText {
            line-height: 1.8;
            letter-spacing: 0.6px;
        }
        .pdpWrapper .pdpBundleList {
            list-style: disc;
            list-style-position: inside;
            cursor: pointer;
            line-height: 1.8;
        }
        .pdpWrapper .productTabtitle {
            @apply font-tomorrow;
            border-bottom: solid;
            color: #666;
            line-height: 24px;
        }
        .pdpWrapper .tabDownloadTitle {
            margin: 2rem 0 0rem 0;
        }
        .pdpWrapper .panel6 .item .product-name {
            left: 0;
            right: 0;
            font-size: 14.4px;
            bottom: 0;
            padding: 5%;
            background-color: rgba(255, 255, 255, 0.5);
            width: 100%;
        }
        .pdpWrapper #pdp-corsair #cart .subtotal {
            bottom: 3.5em;
        }
        @media (min-width: 993px) {
            .pdpWrapper #pdp-corsair #panel1 .thumbs .thumb_img {
                background-position: 50% 50% !important; // Inherited styles from legacy site
                background-size: contain !important; // Inherited styles from legacy site
                background-repeat: no-repeat !important; // Inherited styles from legacy site
            }
            .pdpWrapper .thumbs .slick-next {
                top: 96.8% !important; // Inherited styles from legacy site
            }
        }
        .pdpWrapper #pdp-corsair {
            margin-top: unset;
        }
        .pdpWrapper #pdp-corsair #indabox .half,
        .pdpWrapper
            #pdp-corsair
            #indabox
            .half
            ul
            #pdp-corsair
            #indabox
            .half
            h4 {
            padding-left: 0;
            padding-right: 0;
        }
        .pdpWrapper #pdp-corsair #indabox .half,
        .pdpWrapper #pdp-corsair #indabox .linepadd {
            text-align: center;
        }
        .pdpWrapper #pdp-corsair #indabox .half ul,
        .pdpWrapper #pdp-corsair #indabox .half ul li {
            text-align: left;
        }
        .pdpWrapper #pdp-corsair #indabox ul {
            margin-left: calc(50% + 24px);
            padding: 0;
            transform: translateX(-50%);
        }
        .pdpWrapper #pdp-corsair #indabox ul.csheading {
            list-style-type: none;
            margin-bottom: 0;
            margin-left: calc(50% + 1em);
        }
        .pdpWrapper #pdp-corsair #indabox ul.csheading h4 {
            text-align: left;
            color: #fff;
        }
        .pdpWrapper #pdp-corsair #indabox .linepadd {
            margin-left: 0;
            margin-right: 0;
            padding-left: 0;
            padding-right: 0;
            width: 100%;
        }
        .pdpWrapper .pageType-ProductPage .main-content h1 {
            font-size: 1.9em;
            font-weight: 400;
            margin: 0;
            letter-spacing: 12px;
            text-transform: none !important; // Inherited styles from legacy site
        }
        .pdpWrapper .pageType-ProductPage .main-content h1,
        .pdpWrapper h2,
        .pdpWrapper h3,
        .pdpWrapper h4,
        .pdpWrapper h5,
        .pdpWrapper h6,
        .pdpWrapper .h1,
        .pdpWrapper .h2,
        .pdpWrapper .h3,
        .pdpWrapper .h4,
        .pdpWrapper .h5,
        .pdpWrapper .h6 {
            font-family: inherit;
            font-weight: 500;
            line-height: 1.3;
        }
        .pdpWrapper section ul {
            font-size: 13.2px;
            line-height: 1.8;
            font-weight: 400;
        }
        .pdpWrapper .reviews h4 {
            font-size: 12px;
            text-transform: uppercase;
            line-height: normal;
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper #pdp-corsair #indabox .half {
                width: 100%;
            }
            .pdpWrapper #pdp-corsair #indabox .half ul.csheading li h4 {
                text-align: left;
            }
            .pdpWrapper #pdp-corsair #indabox .linepadd h4 {
                width: auto;
                text-align: left;
                margin-left: calc(50% + 0.5em);
                transform: translateX(-50%);
            }
            .pdpWrapper #pdp-corsair #indabox .linepadd + .half ul {
                margin-bottom: 0;
            }
        }
        .pdpWrapper #pdp-corsair #cart.mobileminicart > a {
            position: static !important; // Inherited styles from legacy site
            bottom: auto !important; // Inherited styles from legacy site
            margin-bottom: 50px;
        }
        .pdpWrapper
            #pdp-corsair
            #cart.mobileminicart
            #minicart-checkout-button {
            position: static !important; // Inherited styles from legacy site
            bottom: auto !important; // Inherited styles from legacy site
        }
        .pdpWrapper #pdp-corsair #cart.mobileminicart .subtotal {
            position: static !important; // Inherited styles from legacy site
            bottom: auto !important; // Inherited styles from legacy site
        }
        .pdpWrapper #pdp-corsair #cart.mobileminicart #cart-content {
            padding-bottom: 20px;
        }
        .pdpWrapper #pdp-corsair #cart.mobileminicart #cart-content {
            height: auto;
        }
        .pdpWrapper #pdp-corsair #cart.mobileminicart {
            overflow-y: auto;
        }
        @media only screen and (max-width: 768px) {
            .pdpWrapper #pdp-corsair .show-on-mobile {
                width: calc(100vw - 30px);
            }
            .pdpWrapper #pdp-corsair .show-on-mobile #mobile-sub-nav {
                border: 1px solid #ccc;
                border-radius: 0;
                font-size: 13px;
                height: 42px;
                width: 100%;
            }
            .pdpWrapper #pdp-corsair #indabox ul.csheading {
                list-style-type: none;
                margin-bottom: 0;
                margin-left: calc(49% + 1em);
                width: 100%;
            }
            .pdpWrapper .pageType-ProductPage #indabox .wrapper h1 {
                text-align: left;
            }
            .pdpWrapper #pdp-corsair #indabox .linepadd h4 {
                width: 100%;
                text-align: left;
                margin-left: calc(49% + 0.5em);
                transform: translateX(-50%);
            }
            .pdpWrapper #pdp-corsair #indabox div ul {
                width: 100%;
                text-align: left;
            }
        }
        .pdpWrapper #pdp-corsair #cart-content li {
            display: table;
        }
        .pdpWrapper #pdp-corsair #cart-content li .product-thumb {
            display: table-cell;
            vertical-align: middle;
        }
        .pdpWrapper #pdp-corsair #panel2 .sub-nav ul li a {
            text-transform: uppercase;
        }
        .pdpWrapper button.btn.btn-primary.selected_true {
            background: #3e6eff;
            color: #000;
            border-radius: 15px !important; // Inherited styles from legacy site
            font-size: 0.8em !important; // Inherited styles from legacy site
            letter-spacing: 0.01px;
        }
        .pdpWrapper button.btn.btn-primary.selected_false {
            background: #3e6eff;
            color: #000;
            border-radius: 15px !important; // Inherited styles from legacy site
            border: 1px solid !important; // Inherited styles from legacy site
            color: #000 !important; // Inherited styles from legacy site
            -webkit-text-fill-color: unset !important; // Inherited styles from legacy site
            background: unset !important; // Inherited styles from legacy site
            font-size: 0.8em !important; // Inherited styles from legacy site
            letter-spacing: 0.01px;
        }
        .pdpWrapper #arrival-notification {
            height: 48px;
        }
        .pdpWrapper .top-product-desc h1,
        .pdpWrapper h2,
        .pdpWrapper h3 {
            line-height: unset;
            margin-top: unset;
            margin-bottom: unset;
        }
        .pdpWrapper
            .variant-selector
            .chosen-container
            .chosen-results
            li.active-result {
            display: list-item;
            cursor: pointer;
            text-transform: unset !important; // Inherited styles from legacy site
        }
        @media screen and (min-width: 825px) {
            .pdpWrapper #product-details #price {
                text-align: left !important; // Inherited styles from legacy site
            }
            .pdpWrapper #product-details #price div:nth-child(2) {
                width: 100%;
            }
            @-moz-document url-prefix() {
                .pdpWrapper #product-details #price .original-price {
                    width: max-content !important; // Inherited styles from legacy site
                }
                .pdpWrapper
                    #product-details
                    #price
                    .original-price
                    .product-price {
                    padding-left: 0 !important; // Inherited styles from legacy site
                }
                .pdpWrapper #product-price #price small {
                    padding-left: 0 !important; // Inherited styles from legacy site
                }
            }
        }
        .pdpWrapper .child-image-counter-label {
            @apply bg-yellow font-tomorrow;
            color: black;
            top: 5%;
            position: absolute;
            width: 115px;
            height: auto;
            text-align: center;
            font-size: small;
            font-weight: bold;
            padding: 7px;
        }
        .pdpWrapper .child-label-hidden {
            display: none;
        }
        .pdpWrapper .ship-promo {
            background-color: #f0f0f0;
            margin-bottom: 8px;
            display: flex;
            padding: 8px;
            align-items: center;
        }
        .pdpWrapper .ship-promo img {
            padding: 0 8px 0px 0px;
        }
        .pdpWrapper .coup-promo {
            background-color: #f7f7ba;
            display: flex;
            padding: 8px;
            align-items: center;
        }
        .pdpWrapper .coup-promo img {
            padding: 0px 8px 0px 0px;
        }
        .pdpWrapper .ship-promo .promotion,
        .pdpWrapper .coup-promo .promotion {
            color: #4d4d4d;
            font-weight: 400;
        }
        .pdpWrapper .coup-promo:not(:last-child) {
            margin-bottom: 8px;
        }
        .pdpWrapper .coup-promo:last-child {
            margin-bottom: 1rem;
        }
        .pdpWrapper .promo-message {
            margin-top: 12px;
        }
        .pdpWrapper .price-pdp .original-price:first-child {
            padding-top: 31px;
            height: 102px;
        }
        .pdpWrapper .panel6.btr-together-desktop.btr-together-desktop-hide {
            height: 0px;
            overflow: hidden;
        }
        @media only screen and (min-width: 993px) {
            .pdpWrapper .btr-together-desktop .item.slick-slide.slick-active {
                width: 25% !important; // Inherited styles from legacy site
            }
            .pdpWrapper .btr-together-desktop .slick-track {
                width: 100% !important; // Inherited styles from legacy site
            }
        }
        .pdpWrapper #pdp-corsair .sticky_addtocart.active h2#price {
            display: inline-block;
        }
        .pdpWrapper
            #pdp-corsair
            .sticky_addtocart.active
            h2#price
            .original-price:first-child {
            height: auto;
            padding-top: 0;
        }
        .pdpWrapper header h4 {
            color: black;
        }
        .pdpWrapper #tab-overview #custom-smalcode.black-bgr .active {
            color: #fff;
        }
        .pdpWrapper #tab-overview #custom-smalcode.white-bgr .active {
            color: #000;
        }
        .pdpWrapper #tab-overview #custom-smalcode .popup-youtube {
            color: white !important; // Inherited styles from legacy site
        }
        .pdpWrapper .crs-corsair-care h4.crs-corsair-care-heading {
            position: relative;
            overflow: hidden;
            font-size: 10px;
            margin-top: 24px;
            margin-bottom: 13px;
            text-align: center;
            color: #262626;
            line-height: 1.6;
            letter-spacing: 1.02px;
        }
        .pdpWrapper #pdp-corsair #panel1 h1.crs-corsair-care-heading {
            @apply font-tomorrow;
            font-size: 45px !important; // Inherited styles from legacy site
            font-weight: 400;
            letter-spacing: 1.02px;
            color: #000;
            margin-bottom: 21px;
        }
        .pdpWrapper .crs-corsair-care h4.crs-corsair-care-heading span {
            display: inline-block;
            vertical-align: baseline;
            zoom: 1;
            display: inline;
            vertical-align: auto;
            position: relative;
            padding: 0 4px;
            font-weight: 500;
        }
        .pdpWrapper .crs-corsair-care h4.crs-corsair-care-heading span::before,
        .pdpWrapper .crs-corsair-care h4.crs-corsair-care-heading span::after {
            content: '';
            display: block;
            width: 1000px;
            position: absolute;
            top: 48%;
            border-top: 1px solid #777;
        }
        .pdpWrapper .crs-corsair-care h4.crs-corsair-care-heading span::before {
            right: 100%;
        }
        .pdpWrapper .crs-corsair-care h4.crs-corsair-care-heading span::after {
            left: 100%;
        }
        .pdpWrapper .crs-warrenty-plans {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .pdpWrapper .crs-warrenty-plans span.crs-warrenty-description {
            width: auto;
            display: flex;
            flex-wrap: wrap;
        }
        .pdpWrapper .crs-warrenty-description p {
            font-weight: 400 !important; // Inherited styles from legacy site
            font-size: 11px;
            letter-spacing: 0.2px !important; // Inherited styles from legacy site
            line-height: 1.364 !important; // Inherited styles from legacy site
            color: #777;
        }
        .pdpWrapper .crs-warrenty-description p .crs-warrenty-learn-more {
            color: #2764a2;
            text-decoration: underline;
        }
        .pdpWrapper .crs-warrenty-description p:first-child {
            padding-bottom: 4px;
        }
        .pdpWrapper .crs-warrenty-atc-btn {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #262626;
            border: 1px solid #262626;
            background: transparent;
            cursor: pointer;
            padding: 5px 15px 5px 15px;
            font-weight: 500;
            font-size: 10px;
            line-height: 1.3;
            letter-spacing: 0.21px;
            min-width: 110px;
        }
        .pdpWrapper span.crs-warrenty-atc-btn-wrapper {
            margin-left: 5px;
        }
        .pdpWrapper .crs-corsair-care-old-price {
            text-decoration: line-through;
            margin-right: 4px;
        }
        .pdpWrapper .crs-warrenty-plans span:not(:first-child) {
            display: flex;
            justify-content: flex-end;
        }
        .pdpWrapper .crs-warrenty-atc-btn:hover {
            background-color: #262626;
            color: #fff;
        }
        .pdpWrapper .crs-warrenty-atc-btn:hover::after {
            content: 'ADD PLAN';
            position: absolute;
            background: #262626;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .pdpWrapper .crs-care-popup-main-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: auto;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 11000;
            min-height: 100vh;
        }
        .pdpWrapper
            .crs-care-popup-main-background.crs-corsair-care-popup-open {
            display: flex;
        }
        .pdpWrapper .crs-care-popup-main {
            width: calc(100% - 25px);
            max-width: 692px;
            box-shadow: 1px 1px 55px 12px rgba(0, 0, 0, 0.5);
            -webkit-box-shadow: 1px 1px 55px 12px rgba(0, 0, 0, 0.5);
            -moz-box-shadow: 1px 1px 55px 12px rgba(0, 0, 0, 0.5);
        }
        .pdpWrapper .crs-care-main-content {
            background-color: #fff;
        }
        .pdpWrapper .crs-care-content-header {
            height: 45px;
            display: flex;
            justify-content: flex-end;
            margin-right: 17px;
            align-items: center;
        }
        .pdpWrapper .crs-corsair-care-header-close-btn {
            font-weight: 600;
            color: #737373;
            font-size: 13px;
            cursor: pointer;
        }
        .pdpWrapper .crs-main-body-care {
            padding: 0px 60px 34px 60px;
        }
        .pdpWrapper .crs-corsair-care-title span {
            font-size: 17px;
            color: black;
            font-weight: 500;
        }
        .pdpWrapper .crs-corsair-care-price .crs-corsair-care-value {
            font-size: 21px;
            letter-spacing: 0.05rem;
            font-weight: 500;
            padding-right: 5px;
            color: black;
        }
        .pdpWrapper .crs-corsair-care-price .crs-corsair-care-format {
            color: black !important; // Inherited styles from legacy site
            font-size: 17px;
            letter-spacing: 0.05rem;
            font-weight: 400;
            color: #808080;
        }
        .pdpWrapper .crs-corsair-care-price .crs-corsair-care-format-old-price {
            color: black;
            font-size: 17px;
            letter-spacing: 0.05rem;
            font-weight: 400;
            color: #808080;
            text-decoration: line-through;
        }
        .pdpWrapper .crs-corsair-care-warrenty-period {
            color: black;
            font-weight: 500;
            letter-spacing: 0.71px;
            line-height: 1.333;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .pdpWrapper .crs-corsair-care-popup-description {
            font-size: 13px;
            letter-spacing: 0.25px;
            line-height: 1.308;
            font-weight: 400;
            color: #262626;
            margin-bottom: 2rem;
        }
        .pdpWrapper .crs-corsair-care-benefits {
            font-size: 13px;
            letter-spacing: 0.25px;
            line-height: 2.308;
            font-weight: 500;
            color: #262626;
            margin-bottom: 8px;
        }
        .pdpWrapper .crs-corsair-care-benefits-list {
            font-size: 13px;
            letter-spacing: 0.25px;
            line-height: 2.308;
            font-weight: 400;
            color: #262626;
            margin-bottom: 46px;
            padding-left: 25px;
        }
        .pdpWrapper .crs-corsair-care-benefits-list li {
            padding-left: 10px;
            line-height: 1.308;
            margin-bottom: 10px;
        }
        .pdpWrapper ul.crs-corsair-care-benefits-list {
            list-style-type: disc;
        }
        .pdpWrapper .crs-corsair-care-atc-btn-popup {
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 1.15px;
            line-height: 1.333;
            color: black;
            background: rgba(233, 230, 0, 1);
            padding: 1rem 0;
            width: 100%;
            margin-bottom: 45px;
        }
        .pdpWrapper .crs-corsair-care-atc-btn-popup {
            background: rgba(233, 230, 0, 0.3);
            color: rgba(0, 0, 0, 0.3);
            border: none;
        }
        .pdpWrapper .crs-corsair-care-atc-btn-popup.option-selected {
            background: rgba(233, 230, 0, 1);
            border: none;
            color: rgba(0, 0, 0, 1);
        }
        .pdpWrapper .crs-corsair-care-footer {
            display: flex;
            justify-content: space-between;
        }
        .pdpWrapper .crs-corsair-care-copyright {
            font-weight: 500;
            font-size: 11px;
            letter-spacing: 0.21px;
            line-height: 1.545;
            color: #262626;
        }
        .pdpWrapper .crs-corsair-care-FAQ a {
            font-weight: 400;
            font-size: 11px;
            letter-spacing: 0.25px;
            line-height: 1.818;
            color: #2764a2;
            text-decoration: underline;
        }
        .pdpWrapper .crs-corsair-care-popup-price-wrapper {
            display: flex;
            margin-bottom: 22px;
        }
        .pdpWrapper
            .crs-corsair-care-popup-price-wrapper
            .crs-corsair-care-popup-price {
            margin-right: 10px;
            padding: 11px 3px;
            font-size: 11px;
            letter-spacing: 0.3px;
            line-height: 1.273;
            color: #262626;
            font-weight: 500;
            border: 1px solid #262626;
            background-color: transparent;
            cursor: pointer;
            min-width: 173px;
        }
        .pdpWrapper
            .crs-corsair-care-popup-price-wrapper
            .crs-corsair-care-popup-price.selected {
            background-color: #262626;
            color: #fff;
        }
        .pdpWrapper .crs-warrenty-atc-btn.crs-corsair-care-atc-selected {
            background-color: #262626;
            color: #fff;
        }
        .pdpWrapper
            .crs-warrenty-atc-btn.crs-corsair-care-atc-selected:hover::after {
            content: 'REMOVE PLAN';
            position: absolute;
            background: #262626;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        @media only screen and (max-width: 600px) {
            .pdpWrapper .crs-care-popup-main {
                width: 100%;
                height: 100%;
            }
            .pdpWrapper .crs-care-main-content {
                height: 100%;
            }
            .pdpWrapper .crs-main-body-care {
                padding: 0px 20px 34px 20px;
                height: calc(100vh - 44px);
                overflow: auto;
            }
            .pdpWrapper
                .crs-corsair-care-popup-price-wrapper
                .crs-corsair-care-popup-price {
                width: 50%;
                min-width: 1px;
                margin: 0;
            }
            .pdpWrapper
                .crs-corsair-care-popup-price-wrapper
                .crs-corsair-care-popup-price:first-child {
                margin-right: 10px;
            }
            .pdpWrapper .crs-corsair-care-footer {
                align-items: center;
                flex-direction: column;
            }
            .pdpWrapper .crs-corsair-care-copyright {
                margin-bottom: 12px;
            }
            .pdpWrapper .crs-corsair-care-popup-description {
                margin-bottom: 15px;
            }
        }
        @media only screen and (max-width: 1024px) {
            .pdpWrapper .crs-warrenty-atc-btn:hover {
                background-color: transparent;
                color: #262626;
            }
            .pdpWrapper .crs-warrenty-atc-btn:hover::after {
                content: '';
                position: absolute;
                background: transparent;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .pdpWrapper .crs-warrenty-atc-btn.crs-corsair-care-atc-selected {
                background-color: #262626;
                color: #fff;
            }
            .pdpWrapper
                .crs-warrenty-atc-btn.crs-corsair-care-atc-selected:hover::after {
                content: '';
                position: absolute;
                background: transparent;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        @media only screen and (min-device-width: 375px) and (max-device-width: 812px) and (orientation: landscape) {
            .pdpWrapper .crs-care-popup-main {
                width: 100%;
                height: 100%;
            }
            .pdpWrapper .crs-main-body-care {
                height: calc(100vh - 44px);
                overflow: auto;
            }
        }
        .pdpWrapper .crs-warrenty-description .content {
            display: contents;
        }
        .pdpWrapper
            .FindRetailerNonCommerce-PickUpInStoreAction
            .modal
            .modal-header {
            height: auto;
            display: block;
        }
        #pdp #ml .blades-container img.blades {
            display: inline;
            width: 92%;
        }

        @media only screen and (min-width: 1320px) {
            .wrapper {
                margin: 0 auto;
                width: 1200px;
            }
        }

        #pdp section .h1,
        #pdp section h1 {
            @apply font-tomorrow;
            font-size: 51.2px;
            font-weight: 600;
            letter-spacing: 1.6px;
            line-height: 51.2px;

            @screen md {
                font-size: 56px;
                line-height: 56px;
            }

            @screen lg {
                font-size: 60px;
                line-height: 60px;
            }

            & > span {
                font-size: inherit;
            }
        }

        #pdp section.testimonial .col-half h1 {
            font-size: 21.6px;
        }

        #pdp-panel-hero video {
            z-index: 0;
            top: 50%;
            left: 50%;
            position: absolute;
            z-index: 0;
            width: 100vw;
            min-height: 105%;
            display: block;
            transform: translate(-50%, -50%);
        }

        section p {
            font-size: 15.6px;
        }

        #pdp-panel-hero.panel-template p {
            font-weight: 400;
            letter-spacing: 8px;
            text-shadow: 0 2px 10px rgb(0 0 0 / 75%);
        }

        #pdp-panel-hero.panel-template .wrapper {
            display: table-cell;
            min-height: 0;
            padding: 10vw 0;
            vertical-align: middle;
        }

        #pdp-panel-hero .hero-content-container {
            display: table;
            min-height: 600px;
            width: 100%;
        }

        .panel-template.product-hero {
            padding: 0;
        }

        #pdp .cta,
        .mfp-wrap .cta {
            @apply font-tomorrow font-semibold;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 12px;
            letter-spacing: 3.2px;
            display: inline-block;
            margin: 12px 0;
            transition: all 200ms ease;
            -webkit-transition: all 200ms ease;

            @screen md {
                font-size: 1rem;
            }

            @screen lg {
                font-size: 10.8px;
            }
        }

        #pdp #versatile {
            .header {
                margin-bottom: 8rem;
            }
            .versatile-sub-nav .sub-nav-connector {
                width: 33%;
            }
        }
        // ~~~~~~~~~~~~~~~
        // ~~~~~~~~~~~~~~~
        // ~MOBILE STYLES~
        // ~~~~~~~~~~~~~~~
        // ~~~~~~~~~~~~~~~
        @media only screen and (max-width: 768px) {
            .pdpWrapper .show-on-mobile {
                display: block;
            }
            .pdpWrapper .hide-on-mobile {
                display: none;
            }
            .pdpWrapper #panel1 .item .media-content {
                width: 100%;
                height: 90vw;
            }
            .pdpWrapper .half {
                width: 100%;
                height: auto !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 .wrapper {
                display: inline-block;
            }
            .pdpWrapper #panel1 .item .media-content {
                width: 100%;
                height: 80vw;
            }
            .pdpWrapper #panel1 #product-details {
                text-align: center;
                margin: 0 auto;
            }
            .pdpWrapper #panel1 .full_pic {
                margin: 0 5%;
            }
            .pdpWrapper #panel1 .half {
                width: 100% !important; // Inherited styles from legacy site
            }
            .pdpWrapper #panel1 {
                padding-top: 60px;
                padding-bottom: 12px;
            }
            .pdpWrapper #panel1 .thumbs {
                position: relative;
            }
            .pdpWrapper #panel1 .additional-infos {
                position: relative;
            }
            .pdpWrapper #panel1 .additional-infos .half {
                width: 47%;
            }
            .pdpWrapper #panel1 #gallery-container {
                background: #eee;
                margin: 0 -10% 5%;
                padding: 10% 10% 5%;
            }
            .pdpWrapper #panel1 #options .half {
                width: 47%;
            }
            .pdpWrapper .content-left,
            .pdpWrapper .content-right {
                width: 100%;
            }
            .pdpWrapper .mainContainer #product-overview {
                -webkit-column-count: 1;
                -moz-column-count: 1;
                column-count: 1;
            }
            .pdpWrapper #panel2 .chosen-container-single {
                margin: 0;
            }
            .pdpWrapper #panel2 .mainContainer {
                padding: 0;
            }
            .pdpWrapper #panel2 .half {
                padding-bottom: 24px;
            }
            .pdpWrapper #panel2 .list-item .half {
                padding-bottom: 0;
            }
            .pdpWrapper #panel2 article .featured-image,
            .pdpWrapper #panel2 article .text {
                max-width: 600px;
                margin: 0 auto;
                width: 100%;
            }
            .pdpWrapper #panel2 article .featured-image {
                padding-bottom: 24px;
            }
            .pdpWrapper #panel3 {
                background-size: 1600px;
            }
            .pdpWrapper #panel4 {
                background-size: 1600px;
            }
            .pdpWrapper #videos ul li,
            .pdpWrapper #indabox .half {
                width: 96%;
            }
            .pdpWrapper #panel6 .item {
                height: 50vw;
            }
            .pdpWrapper .content-right {
                margin-left: 0;
                text-align: left;
            }
            .pdpWrapper #panel6 img {
                max-width: 40vw;
                max-height: 40vw;
                width: auto;
            }
            .pdpWrapper #pdp-corsair .show-on-mobile {
                display: block;
            }
            .pdpWrapper #pdp-corsair .hide-on-mobile {
                display: none;
            }
            .pdpWrapper #pdp-corsair #panel1 .item .media-content {
                width: 100%;
                height: 90vw;
            }
            .pdpWrapper #pdp-corsair .half {
                width: 100%;
                height: auto !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 .wrapper {
                display: inline-block;
            }
            .pdpWrapper #pdp-corsair #panel1 .item .media-content {
                width: 100%;
                height: 80vw;
            }
            .pdpWrapper #pdp-corsair #panel1 #product-details {
                text-align: center;
                margin: 0 auto;
            }
            .pdpWrapper #pdp-corsair #panel1 .full_pic {
                margin: 0 5%;
            }
            .pdpWrapper #pdp-corsair #panel1 .half {
                width: 100% !important; // Inherited styles from legacy site
            }
            .pdpWrapper #pdp-corsair #panel1 {
                padding-top: 60px;
                padding-bottom: 12px;
            }
            .pdpWrapper #pdp-corsair #panel1 .thumbs {
                position: relative;
            }
            .pdpWrapper #pdp-corsair #panel1 .additional-infos {
                position: relative;
            }
            .pdpWrapper #pdp-corsair #panel1 .additional-infos .half {
                width: 47%;
            }
            .pdpWrapper #pdp-corsair #panel1 #gallery-container {
                background: #eee;
                margin: 0 -10% 5%;
                padding: 10% 10% 5%;
            }
            .pdpWrapper #pdp-corsair #panel1 #options .half {
                width: 47%;
            }
            .pdpWrapper #pdp-corsair .content-left,
            .pdpWrapper #pdp-corsair .content-right {
                width: 100%;
            }
            .pdpWrapper #pdp-corsair .mainContainer #product-overview {
                -webkit-column-count: 1;
                -moz-column-count: 1;
                column-count: 1;
            }
            .pdpWrapper #pdp-corsair #panel2 .chosen-container-single {
                margin: 0;
            }
            .pdpWrapper #pdp-corsair #panel2 .mainContainer {
                padding: 0;
            }
            .pdpWrapper #pdp-corsair #panel2 .half {
                padding-bottom: 24px;
            }
            .pdpWrapper #pdp-corsair #panel2 .list-item .half {
                padding-bottom: 0;
            }
            .pdpWrapper #pdp-corsair #panel2 article .featured-image,
            .pdpWrapper #pdp-corsair #panel2 article .text {
                max-width: 600px;
                margin: 0 auto;
                width: 100%;
            }
            .pdpWrapper #pdp-corsair #panel2 article .featured-image {
                padding-bottom: 24px;
            }
            .pdpWrapper #pdp-corsair #videos ul li,
            .pdpWrapper #pdp-corsair #indabox .half {
                width: 96%;
            }
            .pdpWrapper #pdp-corsair #panel6 .item {
                height: 50vw;
            }
            .pdpWrapper #pdp-corsair .content-right {
                margin-left: 0;
                text-align: left;
            }
            .pdpWrapper #pdp-corsair #panel6 img {
                max-width: 40vw;
                max-height: 40vw;
                width: auto;
            }
            .pdpWrapper section h1.bebas {
                font-size: 30px;
            }
            .pdpWrapper #pdp-panel-hero.panel-template .wrapper {
                padding: 5vw 0;
                width: 100%;
            }
            .pdpWrapper #pdp-panel-hero h1 {
                font-size: 60px !important; // Inherited styles from legacy site
                width: calc(100% - 20px);
            }
            .pdpWrapper #pdp-panel-hero p {
                font-size: 18px;
            }
            .pdpWrapper #pdp-panel-hero .background img {
                height: auto;
                width: 100%;
            }
            .pdpWrapper section#panel-award {
                min-height: 113vw;
                padding: 0;
            }
            .pdpWrapper #panel-award .half.left {
                width: 1px;
            }
            .pdpWrapper #panel-award .half.right {
                margin-bottom: 4em;
                vertical-align: bottom;
                width: 478px;
            }
            .pdpWrapper .testimonial {
                background-position: bottom right;
                min-height: 0;
            }
            .pdpWrapper .testimonial .endorser-img-mobile {
                background: #111;
                border: 3px solid #222;
                border-radius: 100px;
                display: inline-block;
                height: 130px;
                margin-left: 15px;
                margin-top: 25px;
                overflow: hidden;
                max-width: 50%;
                vertical-align: middle;
                width: 130px;
            }
            .pdpWrapper .testimonial .endorser-img-mobile img {
                margin-top: 5px;
                margin-left: 10px;
            }
            .pdpWrapper .testimonial .endorser em {
                display: inline-block;
                transform: translateY(25%);
            }

            #pdp #pdp-panel-hero video {
                display: none;
            }
            #pdp #pdp-panel-hero {
                padding: 0;
            }

            #pdp #pdp-panel-hero.panel-template p {
                font-size: 18px;
                margin: 24px auto !important;
                text-shadow: 0 2px 10px rgb(0 0 0 / 75%);
                letter-spacing: 0.5em;
                width: calc(100% - 20px);
            }

            #pdp #pdp-panel-hero .h1,
            #pdp #pdp-panel-hero h1 {
                @apply font-tomorrow font-semibold;
                font-size: 50px !important; // Inherited styles from legacy site
                font-weight: 600;
            }

            #pdp #pdp-panel-hero.panel-template p {
                @apply font-tomorrow font-semibold;
                font-size: 18px;
                margin: 24px auto;
                width: calc(100% - 20px);
            }

            #pdp section .h1.bebas,
            #pdp section h1.bebas {
                font-size: 25px !important; // Inherited styles from legacy site
                font-weight: 600;
            }

            #pdp .panel-template p,
            #pdp .content_wrapper p {
                @apply font-tomorrow font-semibold;
                font-size: 14px;
                font-weight: 400;
            }
        }

        #pdp .interest-points .content_wrapper .full h2.header {
            @apply font-tomorrow;
            color: #222;
            font-size: 48px;
            font-weight: 800;
            line-height: 60px;
            margin: 48px auto;
            max-width: 100%;
        }

        @media (min-width: 1024px) {
            #smalcode.wingsuit-root .lg\:smal-w-2\/4 {
                width: 48%;
            }
        }
        #custom-smalcode {
            .style-h1 {
                @apply font-tomorrow;
                line-height: 138px;
                letter-spacing: -3px;
                text-transform: uppercase;
                font-weight: bold;

                @media only screen and (max-width: 1199px) {
                    font-size: 118px;
                    line-height: 118px;
                    letter-spacing: -2px;
                }

                @media only screen and (max-width: 1023px) {
                    font-size: 42px;
                    line-height: 42px;
                    letter-spacing: -1px;
                }
            }
            .style-h2 {
                @apply font-tomorrow;
                font-weight: bold;
                font-size: 95px;
                letter-spacing: 4px;
                line-height: 95px;
                text-transform: uppercase;

                @media only screen and (max-width: 1199px) {
                    font-size: 80px;
                    letter-spacing: 0.05rem;
                    line-height: 85px;
                }

                @media only screen and (max-width: 1023px) {
                    font-size: 48px;
                    line-height: 50px;
                    letter-spacing: 0.05rem;
                }

                &.headline {
                    margin-bottom: 25px;
                    margin-top: 10px;
                }
            }
            .style-h3 {
                @apply font-verveineRegular;
                font-size: 60px;
                letter-spacing: 12px;
                line-height: 60px;
                text-transform: uppercase;

                @media only screen and (max-width: 1023px) {
                    font-size: 40px;
                    line-height: 48px;
                }
            }
        }
        #custom-smalcode {
            /* ==========================================================================
             development helper
             ========================================================================== */
        }
        #custom-smalcode .rel {
            position: relative;
            height: 100%;
            width: 100%;
        }
        #custom-smalcode .abs-center-wrap {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }
        #custom-smalcode .maxWidth {
            max-width: 1240px;
            margin-left: auto;
            margin-right: auto;
        }
        @media only screen and (min-width: 1440px) {
            #custom-smalcode .maxWidth {
                max-width: 80vw;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .desktop {
                display: none;
            }
        }
        @media only screen and (min-width: 768px) {
            #custom-smalcode .mobile {
                display: none;
            }
        }
        #custom-smalcode .ratio-wrap {
            padding-bottom: 56.25%;
            height: 0;
            position: relative;
        }
        #custom-smalcode .abs-wrap {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }
        #custom-smalcode .clearfix:after {
            clear: both;
            display: block;
            content: '';
        }
        #custom-smalcode html.overflow-hidden {
            height: 100%;
            overflow: hidden;
        }
        #custom-smalcode html.overflow-hidden body {
            height: 100%;
            overflow: hidden;
        }
        #custom-smalcode .headline {
            margin-bottom: 25px;
            margin-top: 10px;
        }
        #custom-smalcode .orange-header {
            color: #fecb00;
        }
        #custom-smalcode .pos-helper {
            position: absolute;
            left: 0;
            width: 1px;
            height: 1px;
        }
        #custom-smalcode .pos-helper.pos-helper-1 {
            top: 50vh;
            top: calc(var(--vh) * 50);
        }
        #custom-smalcode .pos-helper.pos-helper-2 {
            top: 100vh;
            top: calc(var(--vh) * 100);
        }
        #custom-smalcode .pos-helper.pos-helper-3 {
            top: 150vh;
            top: calc(var(--vh) * 150);
        }
        #custom-smalcode .pos-helper.pos-helper-4 {
            top: 200vh;
            top: calc(var(--vh) * 200);
        }
        #custom-smalcode .pos-helper.pos-helper-5 {
            top: 250vh;
            top: calc(var(--vh) * 250);
        }
        #custom-smalcode .pos-helper.pos-helper-6 {
            top: 300vh;
            top: calc(var(--vh) * 300);
        }
        #custom-smalcode .row {
            display: -webkit-box;
            display: flex;
        }
        #custom-smalcode .row.align-row-top {
            -webkit-box-align: start;
            align-items: flex-start;
        }
        #custom-smalcode .row.align-row-end {
            -webkit-box-align: end;
            align-items: flex-end;
        }
        #custom-smalcode .row.align-row-center {
            -webkit-box-align: center;
            align-items: center;
        }
        #custom-smalcode .row.align-row-stretch {
            -webkit-box-align: stretch;
            align-items: stretch;
        }
        #custom-smalcode .row.no-flex {
            display: block;
        }
        #custom-smalcode .row.left-col-full [class^='col-']:first-of-type {
            padding-left: 0;
            padding-right: 0;
        }
        #custom-smalcode .row.right-col-full [class^='col-']:last-of-type {
            padding-left: 0;
            padding-right: 0;
        }
        #custom-smalcode [class^='col-'] {
            padding-right: 8px;
            padding-left: 8px;
        }
        #custom-smalcode .col-1 {
            width: 8.3333333333%;
        }
        #custom-smalcode .col-2 {
            width: 16.6666666667%;
        }
        #custom-smalcode .col-3 {
            width: 25%;
        }
        #custom-smalcode .col-4 {
            width: 33.3333333333%;
        }
        #custom-smalcode .col-5 {
            width: 41.6666666667%;
        }
        #custom-smalcode .col-6 {
            width: 50%;
        }
        #custom-smalcode .col-7 {
            width: 58.3333333333%;
        }
        #custom-smalcode .col-8 {
            width: 66.6666666667%;
        }
        #custom-smalcode .col-9 {
            width: 75%;
        }
        #custom-smalcode .col-10 {
            width: 83.3333333333%;
        }
        #custom-smalcode .col-11 {
            width: 91.6666666667%;
        }
        #custom-smalcode .col-12 {
            width: 100%;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .row [class^='col-'] {
                padding-left: 32px;
                padding-right: 32px;
            }
            #custom-smalcode .row.tablet-break {
                display: block;
            }
            #custom-smalcode .row.tablet-break [class^='col-'].desktop {
                display: none;
            }
            #custom-smalcode .row.tablet-break .col-1 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-2 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-3 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-4 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-5 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-6 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-7 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-8 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-9 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-10 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-11 {
                width: 100%;
            }
            #custom-smalcode .row.tablet-break .col-12 {
                width: 100%;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .row,
            #custom-smalcode .row.tablet-break {
                display: block;
            }
            #custom-smalcode .row [class^='col-'],
            #custom-smalcode .row.tablet-break [class^='col-'] {
                padding-left: 32px;
                padding-right: 32px;
                width: auto;
            }
            #custom-smalcode .row.left-col-full [class^='col-']:first-of-type,
            #custom-smalcode
                .row.tablet-break.left-col-full
                [class^='col-']:first-of-type {
                padding-left: 0;
            }
            #custom-smalcode .row.right-col-full [class^='col-']:last-of-type,
            #custom-smalcode
                .row.tablet-break.right-col-full
                [class^='col-']:last-of-type {
                padding-right: 0;
            }
            #custom-smalcode .col-1 {
                width: 100%;
            }
            #custom-smalcode .col-2 {
                width: 100%;
            }
            #custom-smalcode .col-3 {
                width: 100%;
            }
            #custom-smalcode .col-4 {
                width: 100%;
            }
            #custom-smalcode .col-5 {
                width: 100%;
            }
            #custom-smalcode .col-6 {
                width: 100%;
            }
            #custom-smalcode .col-7 {
                width: 100%;
            }
            #custom-smalcode .col-8 {
                width: 100%;
            }
            #custom-smalcode .col-9 {
                width: 100%;
            }
            #custom-smalcode .col-10 {
                width: 100%;
            }
            #custom-smalcode .col-11 {
                width: 100%;
            }
            #custom-smalcode .col-12 {
                width: 100%;
            }
        }
        #custom-smalcode #grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2334;
            display: none;
        }
        #custom-smalcode #grid.active {
            display: block;
        }
        #custom-smalcode #grid div {
            height: 100%;
        }
        #custom-smalcode #grid .col-1 {
            position: relative;
        }
        #custom-smalcode #grid .col-1:after,
        #custom-smalcode #grid .col-1:before {
            width: 8px;
            background-color: red;
            opacity: 0.3;
            height: 100%;
            content: '';
            position: absolute;
            top: 0;
        }
        #custom-smalcode #grid .col-1:after {
            right: 0;
        }
        #custom-smalcode #grid .col-1:before {
            left: 0;
        }
        #custom-smalcode #grid .col-1:first-of-type:before {
            width: 100vw;
            left: calc(-100vw + 8px);
        }
        #custom-smalcode #grid .col-1:last-of-type:after {
            left: calc(100% - 8px);
            width: 100vw;
        }
        #custom-smalcode h1,
        #custom-smalcode h2,
        #custom-smalcode h3,
        #custom-smalcode h4,
        #custom-smalcode h5,
        #custom-smalcode h6,
        #custom-smalcode p {
            margin: 0;
        }
        #custom-smalcode .style-h1 {
            font-size: 138px;
            line-height: 138px;
            letter-spacing: -3px;
            text-transform: uppercase;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
        }
        @media only screen and (max-height: 899px) {
            #custom-smalcode .style-h1 {
                font-size: 118px;
                line-height: 118px;
                letter-spacing: -2px;
            }
        }
        @media only screen and (max-width: 1199px) {
            #custom-smalcode .style-h1 {
                font-size: 118px;
                line-height: 118px;
                letter-spacing: -2px;
            }
        }
        @media only screen and (max-height: 699px) {
            #custom-smalcode .style-h1 {
                font-size: 42px;
                line-height: 42px;
                letter-spacing: -1px;
            }
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h1 {
                font-size: 42px;
                line-height: 42px;
                letter-spacing: -1px;
            }
        }
        #custom-smalcode .style-h1-1 {
            font-size: 100px;
            line-height: 100px;
            letter-spacing: 5px;
            text-transform: uppercase;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h1-1 {
                font-size: 48px;
                line-height: 48px;
                letter-spacing: 0.05rem;
            }
        }
        #custom-smalcode .style-h2 {
            font-size: 95px;
            letter-spacing: 4px;
            line-height: 95px;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
            text-transform: uppercase;
        }
        @media only screen and (max-width: 1199px) {
            #custom-smalcode .style-h2 {
                font-size: 80px;
                letter-spacing: 0.05rem;
                line-height: 85px;
            }
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h2 {
                font-size: 48px;
                line-height: 50px;
                letter-spacing: 0.05rem;
            }
        }
        #custom-smalcode .style-h3 {
            font-size: 60px;
            line-height: 60px;
            font-weight: 400;
            text-transform: uppercase;
            font-family: 'Verveine';
            font-weight: normal;
        }
        @media only screen and (max-height: 699px) {
            #custom-smalcode .style-h3 {
                font-size: 40px;
                line-height: 48px;
            }
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h3 {
                font-size: 40px;
                line-height: 48px;
            }
        }
        #custom-smalcode .style-h4 {
            font-size: 14px;
            letter-spacing: 3px;
            text-transform: uppercase;
            font-family: Tomorrow, sans-serif;
            letter-spacing: inherit;
            font-weight: 500;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h4 {
                font-size: 12px;
                letter-spacing: 0.05rem;
            }
        }
        #custom-smalcode .style-h5 {
            font-size: 18px;
            letter-spacing: 0.05rem;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h5 {
                font-size: 13px;
                letter-spacing: 0px;
            }
        }
        #custom-smalcode .style-h6 {
            font-size: 22px;
            line-height: 28px;
            font-family: Tomorrow, aktiv-grotesk, sans-serif;
            letter-spacing: inherit;
            font-weight: bold;
            text-transform: uppercase;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .style-h6 {
                font-size: 16px;
                line-height: 22px;
            }
        }
        #custom-smalcode .body-p1,
        #custom-smalcode .body-p1 p {
            font-size: 22px;
            line-height: 36px;
            font-family: Tomorrow, aktiv-grotesk, sans-serif;
            letter-spacing: inherit;
            font-weight: normal;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .body-p1,
            #custom-smalcode .body-p1 p {
                font-size: 16px;
                line-height: 26px;
            }
        }
        #custom-smalcode .body-p2,
        #custom-smalcode .body-p2 p {
            font-size: 18px;
            line-height: 28px;
            font-family: Tomorrow, aktiv-grotesk, sans-serif;
            letter-spacing: inherit;
            font-weight: normal;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .body-p2,
            #custom-smalcode .body-p2 p {
                font-size: 14px;
                line-height: 22px;
            }
        }
        #custom-smalcode .element-label-yellow {
            background-color: #fecb00;
            text-transform: uppercase;
            font-size: 18px;
            letter-spacing: 0.05rem;
            font-weight: bold;
            padding: 16px;
            display: inline-block;
        }
        #custom-smalcode .element-usp-wrap .flex-wrap {
            display: -webkit-box;
            display: flex;
            -webkit-box-align: start;
            align-items: flex-start;
            margin-top: 20px;
        }
        #custom-smalcode .element-usp-wrap .element-usp-item {
            text-align: center;
            font-size: 18px;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
            letter-spacing: 0.05rem;
            line-height: 20px;
            text-transform: uppercase;
            padding-left: 8px;
            padding-right: 8px;
            white-space: nowrap;
            color: #808080;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .element-usp-wrap .element-usp-item {
                font-size: 14px;
                line-height: 16px;
                letter-spacing: 0.05rem;
            }
        }
        #custom-smalcode .element-usp-wrap .element-usp-item img {
            width: 80px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 15px;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .element-usp-wrap .element-usp-item img {
                width: 60px;
                margin-bottom: 10px;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .element-usp-wrap .flex-wrap {
                margin-top: 10px;
                flex-wrap: wrap;
            }
        }
        #custom-smalcode .element-usp-wrap:not(.no-fade) .element-usp-item,
        #custom-smalcode .element-usp-wrap:not(.no-fade) .usp-header {
            opacity: 0;
            -webkit-transform: translate(0, 50px);
            transform: translate(0, 50px);
            will-change: transform, opacity;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode
            .element-usp-wrap:not(.no-fade)
            .element-usp-item.active,
        #custom-smalcode .element-usp-wrap:not(.no-fade) .usp-header.active {
            opacity: 1;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode .element-image-mask {
            position: relative;
            overflow: hidden;
        }
        #custom-smalcode .element-image-mask .mask {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 5;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
            will-change: transform;
            -webkit-transition: 0.8s ease-out;
            transition: 0.8s ease-out;
        }
        #custom-smalcode .element-image-mask .mask.active {
            -webkit-transform: translate(100%, 0);
            transform: translate(100%, 0);
        }
        #custom-smalcode .text-fade-in-1 {
            opacity: 0;
            -webkit-transform: translate(0, 100px);
            transform: translate(0, 100px);
            will-change: transform, opacity;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode .text-fade-in-1.active {
            opacity: 1;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode .element-feature .inner-text-wrap {
            padding-left: 30px;
            padding-right: 30px;
            margin-top: 25px;
        }
        #custom-smalcode .element-feature .feature-headline {
            margin-top: 5px;
            margin-bottom: 25px;
            text-transform: uppercase;
        }
        #custom-smalcode .element-feature .feature-subheadline {
            color: #808080;
        }
        #custom-smalcode .element-feature .bg-image {
            height: 488px;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            position: relative;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .element-feature .feature-headline {
                margin-top: 2px;
                margin-bottom: 10px;
            }
            #custom-smalcode .element-feature:not(.element-feature-4-elements) {
                display: -webkit-box;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
            }
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .bg-image,
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .inner-text-wrap {
                width: 50%;
            }
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .bg-image {
                height: 0;
                padding-bottom: 61%;
            }
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .inner-text-wrap {
                margin-top: 0;
                padding-left: 25px;
                padding-right: 15px;
            }
            #custom-smalcode
                .element-feature.element-feature-4-elements
                .inner-text-wrap {
                padding-left: 20px;
                padding-right: 20px;
            }
            #custom-smalcode
                .element-feature.element-feature-4-elements
                .bg-image {
                height: 0;
                padding-bottom: 120%;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .element-feature.element-feature-4-elements,
            #custom-smalcode .element-feature:not(.element-feature-4-elements) {
                display: -webkit-box;
                display: flex;
                -webkit-box-align: center;
                align-items: center;
            }
            #custom-smalcode
                .element-feature.element-feature-4-elements
                .bg-image,
            #custom-smalcode
                .element-feature.element-feature-4-elements
                .inner-text-wrap,
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .bg-image,
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .inner-text-wrap {
                width: 50%;
            }
            #custom-smalcode
                .element-feature.element-feature-4-elements
                .bg-image,
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .bg-image {
                height: 0;
                padding-bottom: 61%;
            }
            #custom-smalcode
                .element-feature.element-feature-4-elements
                .inner-text-wrap,
            #custom-smalcode
                .element-feature:not(.element-feature-4-elements)
                .inner-text-wrap {
                margin-top: 0;
                padding-left: 25px;
                padding-right: 15px;
            }
        }
        #custom-smalcode .element-feature {
            opacity: 0;
            -webkit-transform: translate(0, 100px);
            transform: translate(0, 100px);
            will-change: transform, opacity;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode .element-feature.active {
            opacity: 1;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode .element-icue-software .icue-header {
            margin-bottom: 40px;
        }
        #custom-smalcode .element-icue-software .icue-icon {
            width: 142px;
            margin-right: 30px;
        }
        #custom-smalcode .element-icue-software .flex-wrap {
            display: -webkit-box;
            display: flex;
            -webkit-box-align: center;
            align-items: center;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .element-icue-software .icue-header {
                margin-bottom: 15px;
            }
            #custom-smalcode .element-icue-software .icue-icon {
                width: 70px;
                margin-right: 30px;
            }
        }
        #custom-smalcode .element-icue-software {
            opacity: 0;
            -webkit-transform: translate(0, 50px);
            transform: translate(0, 50px);
            will-change: transform, opacity;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode .element-icue-software.active {
            opacity: 1;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode .cta-learn-more {
            display: -webkit-box;
            display: flex;
            -webkit-box-align: center;
            align-items: center;
        }
        #custom-smalcode .cta-learn-more img {
            width: 10px;
            margin-left: 10px;
        }
        #custom-smalcode .cta-white {
            background-color: white;
            padding-left: 16px;
            padding-right: 16px;
            padding-top: 18px;
            padding-bottom: 18px;
            display: inline-block;
            cursor: pointer;
        }
        #custom-smalcode .element-zoom-image {
            position: relative;
            margin-left: 40px;
            margin-right: 40px;
            overflow: hidden;
        }
        #custom-smalcode .element-zoom-image .cta-white {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%) translate(0, -0.5px);
            transform: translate(-50%, -50%) translate(0, -0.5px);
        }
        #custom-smalcode .element-zoom-image video {
            width: 100%;
            height: auto;
            display: block;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .element-zoom-image {
                margin-left: 0;
                margin-right: 0;
            }
        }
        #custom-smalcode .element-zoom-image {
            opacity: 0;
            will-change: opacity;
            -webkit-transition: 0.8s;
            transition: 0.8s;
        }
        #custom-smalcode .element-zoom-image.active {
            opacity: 1;
        }
        #custom-smalcode .element-zoom-image .zoom-ele {
            -webkit-transform: scale(1.4);
            transform: scale(1.4);
            will-change: transform;
            -webkit-transition: 0.8s;
            transition: 0.8s;
        }
        #custom-smalcode .element-zoom-image .zoom-ele.active {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        #custom-smalcode .element-product-teaser {
            padding-top: 40px;
            padding-bottom: 30px;
            height: 100%;
        }
        #custom-smalcode .element-product-teaser .image-swap-element {
            padding-left: 50px;
            padding-right: 50px;
        }
        #custom-smalcode .element-product-teaser .image-swap-element img {
            display: none;
        }
        #custom-smalcode
            .element-product-teaser.black
            .image-swap-element
            [data-id='black'],
        #custom-smalcode
            .element-product-teaser.white
            .image-swap-element
            [data-id='white'],
        #custom-smalcode
            .element-product-teaser.red
            .image-swap-element
            [data-id='red'] {
            display: block;
        }
        #custom-smalcode .element-product-teaser .color-picker-wrap {
            display: -webkit-inline-box;
            display: inline-flex;
            margin-bottom: 40px;
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker {
            width: 24px;
            height: 24px;
            position: relative;
            margin-right: 25px;
            cursor: pointer;
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker:last-of-type {
            margin-right: 0;
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker
            .color-bullet {
            width: 16px;
            height: 16px;
            position: absolute;
            border-radius: 100%;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker
            .active-indicator {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            border: 2px solid white;
            border-radius: 100%;
            opacity: 0;
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
            will-change: opacity;
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker.black
            .color-bullet {
            background-color: black;
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker.white
            .color-bullet {
            background-color: white;
        }
        #custom-smalcode
            .element-product-teaser
            .color-picker-wrap
            .color-picker.red
            .color-bullet {
            background-color: red;
        }
        #custom-smalcode
            .element-product-teaser.black
            .color-picker-wrap
            .color-picker.black
            .active-indicator,
        #custom-smalcode
            .element-product-teaser.white
            .color-picker-wrap
            .color-picker.white
            .active-indicator,
        #custom-smalcode
            .element-product-teaser.red
            .color-picker-wrap
            .color-picker.red
            .active-indicator {
            opacity: 1;
        }
        #custom-smalcode .element-product-teaser .cta-transparent {
            display: none;
        }
        #custom-smalcode .element-product-teaser.black .cta-transparent.black,
        #custom-smalcode .element-product-teaser.white .cta-transparent.white,
        #custom-smalcode .element-product-teaser.red .cta-transparent.red {
            display: inline-block;
        }
        #custom-smalcode .element-product-teaser .inner-title {
            font-size: 38px;
            letter-spacing: -0.8px;
            text-transform: uppercase;
            margin-bottom: 25px;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .element-product-teaser {
                padding-top: 25px;
                padding-bottom: 25px;
            }
            #custom-smalcode .element-product-teaser .image-swap-element {
                padding-left: 0;
                padding-right: 0;
            }
            #custom-smalcode .element-product-teaser .color-picker-wrap {
                margin-bottom: 25px;
            }
            #custom-smalcode
                .element-product-teaser
                .color-picker-wrap
                .color-picker {
                margin-right: 15px;
            }
            #custom-smalcode .element-product-teaser .inner-title {
                margin-bottom: 15px;
                font-size: 28px;
                letter-spacing: -0.4px;
            }
        }
        #custom-smalcode .element-product-teaser {
            opacity: 0;
            -webkit-transform: translate(0, 100px);
            transform: translate(0, 100px);
            will-change: transform, opacity;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode .element-product-teaser.active {
            opacity: 1;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode .cta-transparent {
            font-size: 14px;
            font-weight: 400;
            text-transform: uppercase;
            border: 1px solid white;
            display: inline-block;
            padding-left: 40px;
            padding-right: 40px;
            padding-top: 15px;
            padding-bottom: 15px;
            font-family: Tomorrow, aktiv-grotesk, sans-serif;
            letter-spacing: inherit;
            font-weight: 500;
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
        }
        #custom-smalcode .cta-transparent:hover {
            background-color: black;
            border: 1px solid black;
        }
        #custom-smalcode .element-pax-move {
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
            will-change: transform;
        }
        #custom-smalcode .element-pax-move.active {
            -webkit-transform: translate(0, -30vh);
            transform: translate(0, -30vh);
            -webkit-transform: translate(0, calc(var(--vh) * -30));
            transform: translate(0, calc(var(--vh) * -30));
        }
        #custom-smalcode .element-video-popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #252525;
            z-index: 110;
            pointer-events: none;
            opacity: 0;
            -webkit-transition: 0.3s ease-out;
            transition: 0.3s ease-out;
            will-change: opacity;
        }
        #custom-smalcode .element-video-popup.active {
            opacity: 1;
            pointer-events: all;
        }
        #custom-smalcode .element-video-popup .center-item {
            width: 100%;
        }
        #custom-smalcode .element-video-popup .inner-popup {
            display: -webkit-box;
            display: flex;
            -webkit-box-align: center;
            align-items: center;
            height: 100%;
            width: 70%;
            margin-left: auto;
            margin-right: auto;
        }
        #custom-smalcode .element-video-popup .close-wrap {
            margin-left: auto;
            margin-right: auto;
            width: 22px;
            cursor: pointer;
            margin-bottom: 30px;
        }
        #custom-smalcode .element-video-popup video,
        #custom-smalcode .element-video-popup iframe {
            width: 100%;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        #custom-smalcode .element-video-popup iframe {
            height: 100%;
        }
        #custom-smalcode .element-video-popup .ratio-wrap {
            padding-bottom: 56.25%;
            height: 0;
            position: relative;
        }
        #custom-smalcode .element-video-popup .abs-wrap {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .element-video-popup .inner-popup {
                width: 90%;
            }
        }
        #custom-smalcode .panel-hero-pdp {
            background-color: #f9f9f9;
            text-align: center;
            height: 100vh;
            height: calc(var(--vh) * 100);
            position: relative;
            overflow: hidden;
        }
        #custom-smalcode .panel-hero-pdp .headline-wrap {
            display: -webkit-inline-box;
            display: inline-flex;
            -webkit-box-align: center;
            align-items: center;
            margin-bottom: 25px;
        }
        #custom-smalcode .panel-hero-pdp .headline-wrap .element-label-yellow {
            margin-left: 20px;
        }
        #custom-smalcode .panel-hero-pdp .hero-device {
            position: absolute;
            height: 65vh;
            height: calc(var(--vh) * 65);
            width: auto;
            bottom: 0;
            left: 50%;
            z-index: 5;
        }
        #custom-smalcode .panel-hero-pdp .title-wrap {
            position: absolute;
            left: 0;
            width: 100%;
            top: 13vh;
            top: calc(var(--vh) * 13);
            z-index: 10;
        }
        @media only screen and (max-height: 899px) {
            #custom-smalcode .panel-hero-pdp .hero-device {
                height: 60vh;
                height: calc(var(--vh) * 60);
            }
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-hero-pdp {
                height: 85vh;
                height: calc(var(--vh) * 85);
            }
            #custom-smalcode .panel-hero-pdp .hero-device {
                height: 50vh;
                height: calc(var(--vh) * 50);
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-hero-pdp .headline-wrap {
                margin-bottom: 10px;
            }
        }
        #custom-smalcode .panel-hero-pdp .hero-device {
            opacity: 0;
            -webkit-transform: scale(1.3) translate(-50%, 0);
            transform: scale(1.3) translate(-50%, 0);
            will-change: transform, opacity;
            -webkit-transition: 0.8s ease-out;
            transition: 0.8s ease-out;
        }
        #custom-smalcode .panel-hero-pdp .hero-device.active {
            opacity: 1;
            -webkit-transform: scale(1) translate(-50%, 0);
            transform: scale(1) translate(-50%, 0);
        }
        #custom-smalcode .panel-explosion-view {
            position: relative;
            padding-bottom: 33vw;
            padding-top: 120px;
        }
        #custom-smalcode .panel-explosion-view .bg-layer {
            position: relative;
            z-index: 3;
            width: 70%;
            right: 2%;
            bottom: 0;
            position: absolute;
            z-index: 5;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-explosion-view .bg-layer {
                position: relative;
                width: 100%;
                right: 0;
                margin-top: 20px;
            }
        }
        #custom-smalcode .panel-explosion-view .text-layer {
            position: relative;
            z-index: 10;
        }
        #custom-smalcode .panel-explosion-view .element-usp-wrap {
            margin-top: 60px;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-explosion-view {
                padding-top: 50px;
                padding-bottom: 50px;
            }
            #custom-smalcode .panel-explosion-view .text-layer {
                position: relative;
                top: auto;
            }
            #custom-smalcode .panel-explosion-view .element-usp-wrap {
                margin-top: 40px;
            }
        }
        #custom-smalcode .panel-comfort {
            padding-top: 120px;
            padding-bottom: 120px;
            background-color: #f9f9f9;
            position: relative;
        }
        #custom-smalcode .panel-comfort .outer-usp-wrap {
            margin-top: 60px;
            margin-bottom: 25px;
            display: -webkit-box;
            display: flex;
            -webkit-box-pack: end;
            justify-content: flex-end;
        }
        #custom-smalcode .panel-comfort .relative-wrap {
            position: relative;
            padding-bottom: 20vw;
        }
        #custom-smalcode .panel-comfort .element-image-mask .mask {
            background-color: #f9f9f9;
        }
        #custom-smalcode .panel-comfort .image-mask-1 {
            margin-top: 60px;
        }
        #custom-smalcode .panel-comfort .image-swap {
            position: absolute;
            bottom: 0;
            right: 15%;
            width: 58%;
            z-index: 5;
        }
        #custom-smalcode .panel-comfort .image-swap .device-left {
            position: absolute;
            bottom: 21%;
            left: 13%;
            width: 28.1%;
        }
        #custom-smalcode .panel-comfort .image-swap .device-right {
            position: absolute;
            bottom: 9.5%;
            right: 11.5%;
            width: 44%;
        }
        #custom-smalcode .panel-comfort .image-swap .device-right.wireless {
            bottom: 13.5%;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-comfort {
                padding-top: 50px;
                padding-bottom: 40px;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-comfort {
                text-align: right;
                overflow: hidden;
                /*.element-image-mask{
                width: 60%;
                display: inline-block;
              }*/
            }
            #custom-smalcode .panel-comfort .relative-wrap {
                padding-bottom: 30px;
            }
            #custom-smalcode .panel-comfort .image-mask-1 {
                margin-top: 0;
                margin-bottom: 0;
                width: 50%;
            }
            #custom-smalcode .panel-comfort .image-mask-2 {
                width: 80%;
                display: inline-block;
            }
            #custom-smalcode .panel-comfort .outer-usp-wrap {
                margin-top: 40px;
                margin-bottom: 30px;
            }
            #custom-smalcode .panel-comfort .image-swap {
                right: -5%;
                left: auto;
                width: 75%;
                bottom: -5%;
            }
        }
        #custom-smalcode .panel-comfort .image-swap .device-left {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-transform-origin: center center;
            transform-origin: center center;
            will-change: transform;
        }
        #custom-smalcode .panel-comfort .image-swap .device-left.active {
            -webkit-transform: rotate(20deg) translate(-18%, 11%);
            transform: rotate(20deg) translate(-18%, 11%);
        }
        #custom-smalcode .panel-comfort .image-swap .device-right {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            -webkit-transform-origin: center center;
            transform-origin: center center;
            will-change: transform;
        }
        #custom-smalcode .panel-comfort .image-swap .device-right.active {
            -webkit-transform: rotate(-20deg) translate(12%, 13%);
            transform: rotate(-20deg) translate(12%, 13%);
        }
        #custom-smalcode .panel-connectivity {
            padding-top: 120px;
            padding-bottom: 120px;
            position: relative;
        }
        #custom-smalcode .panel-connectivity .element-usp-wrap-2 {
            margin-top: 0;
            margin-left: 60px;
        }
        #custom-smalcode .panel-connectivity .row-image-usp {
            margin-top: 50px;
        }
        #custom-smalcode .panel-connectivity .usp-outer-wrap {
            display: -webkit-box;
            display: flex;
            margin-bottom: 40px;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-connectivity {
                padding-top: 40px;
                padding-bottom: 0;
            }
            #custom-smalcode .panel-connectivity .row-image-usp {
                margin-top: 30px;
            }
            #custom-smalcode .panel-connectivity .element-usp-wrap-2 {
                margin-top: 30px;
                margin-bottom: 25px;
                margin-left: 0;
            }
            #custom-smalcode .panel-connectivity .col-image {
                padding-left: 0;
                padding-right: 0;
            }
            #custom-smalcode .panel-connectivity .usp-outer-wrap {
                display: block;
            }
        }
        #custom-smalcode .panel-features {
            padding-top: 110px;
            padding-bottom: 50px;
            position: relative;
            background-color: #f9f9f9;
            text-align: center;
        }
        #custom-smalcode .panel-features .row-features {
            margin-top: 40px;
        }
        #custom-smalcode .panel-features .element-feature {
            text-align: left;
        }
        #custom-smalcode .panel-features .headline {
            margin-bottom: 0;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-features {
                padding-bottom: 35px;
                padding-top: 65px;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-'] {
                padding-left: 0;
                padding-right: 0;
                margin-bottom: 25px;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-']:last-of-type {
                margin-bottom: 0;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-']:nth-child(odd)
                .element-feature {
                -webkit-box-orient: horizontal;
                -webkit-box-direction: reverse;
                flex-direction: row-reverse;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-']:nth-child(odd)
                .element-feature
                .inner-text-wrap {
                padding-right: 25px;
                padding-left: 15px;
            }
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features {
                display: -webkit-box;
                display: flex;
                -webkit-box-align: start;
                align-items: flex-start;
                flex-flow: wrap;
            }
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-'] {
                width: 42%;
                margin-top: 30px;
            }
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-']:nth-child(1),
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-']:nth-child(2) {
                margin-top: 0;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features,
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features {
                display: block;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-'],
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-'] {
                padding-left: 0;
                padding-right: 0;
                margin-bottom: 25px;
                width: 100%;
                margin-top: 0;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-']:last-of-type,
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-']:last-of-type {
                margin-bottom: 0;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-']:nth-child(odd)
                .element-feature,
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-']:nth-child(odd)
                .element-feature {
                -webkit-box-orient: horizontal;
                -webkit-box-direction: reverse;
                flex-direction: row-reverse;
            }
            #custom-smalcode
                .panel-features:not(.panel-features-4-elements)
                .row-features
                [class^='col-']:nth-child(odd)
                .element-feature
                .inner-text-wrap,
            #custom-smalcode
                .panel-features.panel-features-4-elements
                .row-features
                [class^='col-']:nth-child(odd)
                .element-feature
                .inner-text-wrap {
                padding-right: 25px;
                padding-left: 15px;
            }
        }
        #custom-smalcode .panel-programmable {
            padding-top: 120px;
            padding-bottom: 40px;
            position: relative;
        }
        #custom-smalcode .panel-programmable .row-icue-usps {
            margin-top: 40px;
        }
        #custom-smalcode .panel-programmable .usp-outer-wrap {
            display: -webkit-box;
            display: flex;
            -webkit-box-pack: end;
            justify-content: flex-end;
        }
        #custom-smalcode .panel-programmable .element-usp-wrap-1 {
            margin-right: 50px;
        }
        #custom-smalcode .panel-programmable .element-zoom-image {
            margin-top: 40px;
        }
        #custom-smalcode .panel-programmable .element-usp-wrap {
            margin-top: 50px;
        }
        @media only screen and (min-width: 1024px) {
            #custom-smalcode .panel-programmable .col-text {
                padding-right: 50px;
            }
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-programmable {
                padding-top: 65px;
                padding-bottom: 0;
            }
            #custom-smalcode .panel-programmable .element-icue-software {
                margin-top: 50px;
            }
            #custom-smalcode .panel-programmable .usp-outer-wrap {
                display: block;
            }
            #custom-smalcode .panel-programmable .element-usp-wrap-1 {
                margin-right: 0;
            }
            #custom-smalcode .panel-programmable .element-usp-wrap-1,
            #custom-smalcode .panel-programmable .element-usp-wrap-2 {
                margin-top: 40px;
            }
        }
        #custom-smalcode .panel-hero-category {
            position: relative;
            height: 100vh;
            height: calc(var(--vh) * 100);
            text-align: center;
            background-color: black;
        }
        #custom-smalcode .panel-hero-category .bg-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }
        #custom-smalcode .panel-hero-category .bg-layer .bg-image {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }
        #custom-smalcode .panel-hero-category .headline-layer {
            position: absolute;
            top: 45%;
            left: 0;
            width: 100%;
        }
        #custom-smalcode .panel-hero-category .headline {
            margin-top: 25px;
            margin-bottom: 35px;
        }
        #custom-smalcode .panel-hero-category .link-list {
            display: -webkit-inline-box;
            display: inline-flex;
            text-transform: uppercase;
            font-size: 30px;
            line-height: 30px;
            text-transform: uppercase;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-hero-category .link-list {
                font-size: 16px;
            }
        }
        #custom-smalcode .panel-hero-category .link-list a {
            display: block;
            margin-right: 60px;
        }
        #custom-smalcode .panel-hero-category .link-list a:last-of-type {
            margin-right: 0;
        }
        #custom-smalcode .panel-hero-category .read-more-layer {
            position: absolute;
            bottom: 0;
            left: 50%;
            -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
            white-space: nowrap;
        }
        #custom-smalcode .panel-hero-category .read-more-layer .text {
            text-transform: uppercase;
            font-size: 14px;
            font-family: Tomorrow, aktiv-grotesk, sans-serif;
            letter-spacing: inherit;
            font-weight: 500;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-hero-category .read-more-layer .text {
                font-size: 12px;
                letter-spacing: 0.05rem;
            }
        }
        #custom-smalcode .panel-hero-category .read-more-layer .animation-wrap {
            height: 40px;
            overflow: hidden;
            position: relative;
        }
        #custom-smalcode
            .panel-hero-category
            .read-more-layer
            .animation-wrap
            .line {
            width: 2px;
            height: 34px;
            position: absolute;
            bottom: 0;
            left: 50%;
            -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
            background-color: white;
            -webkit-animation: heroLineMove 2s infinite;
            animation: heroLineMove 2s infinite;
            will-change: transform;
        }
        #custom-smalcode .panel-hero-category .video-layer {
            position: absolute;
            right: 20px;
            bottom: 20px;
            width: 185px;
            cursor: pointer;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-hero-category .style-h1 {
                font-size: 82px;
                line-height: 92px;
                letter-spacing: -1px;
            }
        }
        #custom-smalcode .panel-hero-category .video-layer .play-icon,
        #custom-smalcode
            .panel-hero-category-video-layer-mobile
            .video-layer
            .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            width: 16px;
        }
        #custom-smalcode .panel-hero-category-video-layer-mobile {
            text-align: center;
            padding-top: 75px;
            padding-bottom: 50px;
            padding-left: 32px;
            padding-right: 32px;
            background-color: #363636;
        }
        #custom-smalcode .panel-hero-category-video-layer-mobile .video-layer {
            position: relative;
            margin-top: 30px;
        }

        @-webkit-keyframes heroLineMove {
            from {
                -webkit-transform: translate(-50%, -45px);
                transform: translate(-50%, -45px);
            }
            to {
                -webkit-transform: translate(-50%, 45px);
                transform: translate(-50%, 45px);
            }
        }

        @keyframes heroLineMove {
            from {
                -webkit-transform: translate(-50%, -45px);
                transform: translate(-50%, -45px);
            }
            to {
                -webkit-transform: translate(-50%, 45px);
                transform: translate(-50%, 45px);
            }
        }
        #custom-smalcode .panel-hero-category .bg-layer {
            opacity: 1;
            will-change: opacity;
        }
        #custom-smalcode .panel-hero-category .bg-layer.active {
            opacity: 0;
        }
        #custom-smalcode .panel-hero-category {
            overflow: hidden;
        }
        #custom-smalcode .panel-hero-category .bg-layer .inner-fade-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            will-change: opacity;
            opacity: 1;
        }
        #custom-smalcode
            .panel-hero-category
            .bg-layer
            .inner-fade-layer.active {
            opacity: 0;
        }
        #custom-smalcode .panel-hero-category .bg-layer .inner-move-up-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }
        #custom-smalcode .panel-hero-category .bg-layer img {
            height: 82%;
            width: auto;
            position: absolute;
            top: 9.5%;
            left: 50%;
            -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
        }
        #custom-smalcode .panel-hero-category .bg-layer.width-full img {
            width: 36%;
            height: auto;
            top: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
        }
        #custom-smalcode .panel-hero-category .element-image-mask .mask {
            background-color: black;
            -webkit-transition: 1s ease-out;
            transition: 1s ease-out;
        }
        #custom-smalcode .panel-hero-category .headline-layer .style-h6,
        #custom-smalcode .panel-hero-category .headline-layer .headline,
        #custom-smalcode .panel-hero-category .headline-layer a {
            will-change: opacity;
            opacity: 0;
        }
        #custom-smalcode .panel-hero-category .headline-layer .style-h6.fade-in,
        #custom-smalcode .panel-hero-category .headline-layer .headline.fade-in,
        #custom-smalcode .panel-hero-category .headline-layer a.fade-in {
            opacity: 1;
        }
        #custom-smalcode .panel-hero-category .headline-layer .style-h6,
        #custom-smalcode .panel-hero-category .headline-layer .headline {
            -webkit-transition: 1s ease-out;
            transition: 1s ease-out;
        }
        #custom-smalcode .panel-hero-category .headline-layer a {
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode .panel-hero-category .bg-layer {
            -webkit-transform: scale(1.4);
            transform: scale(1.4);
            will-change: opacity, transform;
            -webkit-transition: 2s ease-out;
            transition: 2s ease-out;
        }
        #custom-smalcode .panel-hero-category .bg-layer.zoom-in {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        #custom-smalcode .panel-hero-category .bg-layer .bg-image,
        #custom-smalcode .panel-hero-category .bg-layer img {
            opacity: 0;
            will-change: opacity;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
        }
        #custom-smalcode .panel-hero-category .bg-layer .bg-image.fade-in,
        #custom-smalcode .panel-hero-category .bg-layer img.fade-in {
            opacity: 1;
        }
        #custom-smalcode .panel-hero-category .bg-layer .bg-image {
            -webkit-transition: 1s ease-out;
            transition: 1s ease-out;
        }
        #custom-smalcode .panel-hero-category .bg-layer .inner-move-up-layer {
            -webkit-transform: translate(0, 50px);
            transform: translate(0, 50px);
            will-change: transform;
            -webkit-transition: 0.5s ease-out;
            transition: 0.5s ease-out;
        }
        #custom-smalcode
            .panel-hero-category
            .bg-layer
            .inner-move-up-layer.active {
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode .panel-hero-category .read-more-layer {
            -webkit-transform: translate(-50%, 100%);
            transform: translate(-50%, 100%);
            will-change: transform;
            -webkit-transition: 0.3s ease-out;
            transition: 0.3s ease-out;
        }
        #custom-smalcode .panel-hero-category .read-more-layer.fade-in {
            -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-hero-category .headline {
                margin-top: 0;
                margin-bottom: 0;
            }
            #custom-smalcode .panel-hero-category .link-list a {
                margin-right: 40px;
            }
            #custom-smalcode .panel-hero-category .bg-layer img {
                height: 55%;
                top: 23.5%;
            }
            #custom-smalcode .panel-hero-category .bg-layer .bg-image {
                top: auto;
                height: 65%;
                bottom: 30vw;
            }
        }
        #custom-smalcode .panel-meet-the-family {
            padding-top: 120px;
            padding-bottom: 60px;
            text-align: center;
            position: relative;
            background-image: -webkit-gradient(
                linear,
                left top,
                left bottom,
                from(#363636),
                color-stop(52%, #4b4b4b),
                color-stop(68%, #414141)
            );
            background-image: -webkit-linear-gradient(
                top,
                #363636 0%,
                #4b4b4b 52%,
                #414141 68%
            );
            background-image: linear-gradient(
                180deg,
                #363636 0%,
                #4b4b4b 52%,
                #414141 68%
            );
        }
        #custom-smalcode .panel-meet-the-family > .maxWidth {
            position: relative;
            z-index: 99;
        }
        #custom-smalcode .panel-meet-the-family .headline-wrap {
            margin-bottom: 25px;
        }
        #custom-smalcode .panel-meet-the-family .row-teasers {
            margin-top: 70px;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-meet-the-family {
                padding-top: 70px;
                padding-bottom: 40px;
            }
            #custom-smalcode .panel-meet-the-family .headline-wrap {
                margin-bottom: 15px;
            }
            #custom-smalcode .panel-meet-the-family .row-teasers {
                margin-top: 30px;
            }
            #custom-smalcode
                .panel-meet-the-family
                .row-teasers
                [class^='col-'] {
                padding-left: 4px;
                padding-right: 4px;
            }
        }
        #custom-smalcode .panel-audio-quality-2 {
            position: relative;
            padding-top: 110px;
            padding-bottom: 23vw;
        }
        #custom-smalcode .panel-audio-quality-2 .element-usp-wrap {
            margin-top: 60px;
        }
        #custom-smalcode .panel-audio-quality-2 .element-image-mask .mask {
            background-color: #414141;
        }
        #custom-smalcode .panel-audio-quality-2 .text-layer {
            position: relative;
            z-index: 10;
        }
        #custom-smalcode .panel-audio-quality-2 .bg-layer {
            width: 70%;
            right: 2%;
            bottom: 0;
            position: absolute;
            z-index: 5;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-audio-quality-2 .bg-layer {
                position: relative;
                width: 100%;
                right: 0;
                margin-top: 20px;
            }
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-audio-quality-2 {
                padding-top: 50px;
                padding-bottom: 30px;
            }
            #custom-smalcode .panel-audio-quality-2 .element-usp-wrap {
                margin-top: 40px;
            }
            #custom-smalcode .panel-audio-quality-2 .element-image-mask {
                margin-top: 15px;
            }
        }
        #custom-smalcode .product-sequence {
            position: relative;
        }
        #custom-smalcode .product-sequence img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
        }
        #custom-smalcode .product-sequence img.active {
            opacity: 1;
        }
        #custom-smalcode .product-sequence img:first-of-type {
            position: relative;
        }
        #custom-smalcode .panel-comfort-2 {
            padding-top: 110px;
            position: relative;
            background: -webkit-gradient(
                linear,
                left top,
                left bottom,
                from(#414141),
                color-stop(68%, #4a4a4c)
            );
            background: -webkit-linear-gradient(top, #414141 0%, #4a4a4c 68%);
            background: linear-gradient(180deg, #414141 0%, #4a4a4c 68%);
        }
        #custom-smalcode .panel-comfort-2 .bg-layer {
            position: absolute;
            top: 50%;
            left: 0;
            width: 70%;
            -webkit-transform: translate(0, -50%);
            transform: translate(0, -50%);
        }
        #custom-smalcode .panel-comfort-2 .text-layer {
            position: relative;
        }
        #custom-smalcode .panel-comfort-2 .outer-usp-wrap {
            margin-top: 60px;
            margin-bottom: 20px;
            display: -webkit-box;
            display: flex;
            -webkit-box-pack: end;
            justify-content: flex-end;
        }
        #custom-smalcode .panel-comfort-2 video {
            width: 100%;
            height: auto;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-comfort-2 {
                padding-top: 50px;
                padding-bottom: 50px;
                text-align: right;
            }
            #custom-smalcode .panel-comfort-2 .row-image {
                margin-top: -50px;
            }
            #custom-smalcode .panel-comfort-2 .row-image video {
                width: 120px;
                height: auto;
                display: inline-block;
            }
        }
        #custom-smalcode .panel-category-sticky,
        #custom-smalcode .panel-category-sticky-mobile {
            background-color: #4a4a4c;
        }
        #custom-smalcode .panel-category-sticky .pos-helper-1,
        #custom-smalcode .panel-category-sticky-mobile .pos-helper-1 {
            top: 60vh;
            top: calc(var(--vh) * 60);
        }
        #custom-smalcode .panel-category-sticky .pos-helper-2,
        #custom-smalcode .panel-category-sticky-mobile .pos-helper-2 {
            top: 120vh;
            top: calc(var(--vh) * 120);
        }
        #custom-smalcode .panel-category-sticky .seperator,
        #custom-smalcode .panel-category-sticky-mobile .seperator {
            margin-top: 25px;
            margin-bottom: 15px;
            width: 34px;
            height: 2px;
            background-color: #fecb00;
        }
        #custom-smalcode .panel-category-sticky .sticky-layer,
        #custom-smalcode .panel-category-sticky-mobile .sticky-layer {
            height: 100vh;
            height: calc(var(--vh) * 100);
            width: 100%;
            overflow: hidden;
        }
        #custom-smalcode .panel-category-sticky .image-wrap,
        #custom-smalcode .panel-category-sticky-mobile .image-wrap {
            position: absolute;
            left: 0;
            width: 100%;
            top: 50%;
            -webkit-transform: translate(0, -50%);
            transform: translate(0, -50%);
        }
        #custom-smalcode .panel-category-sticky .image-wrap .images-outer-wrap,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .images-outer-wrap {
            position: relative;
            -webkit-transform: scale(1.5);
            transform: scale(1.5);
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .images-outer-wrap.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .images-outer-wrap.active {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        #custom-smalcode .panel-category-sticky .image-wrap .images,
        #custom-smalcode .panel-category-sticky-mobile .image-wrap .images {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
        }
        #custom-smalcode .panel-category-sticky .image-wrap .images img,
        #custom-smalcode .panel-category-sticky-mobile .image-wrap .images img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
        }
        #custom-smalcode .panel-category-sticky .image-wrap .images img.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .images
            img.active {
            opacity: 1;
        }
        #custom-smalcode .panel-category-sticky .image-wrap .pfb-1,
        #custom-smalcode .panel-category-sticky-mobile .image-wrap .pfb-1 {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            opacity: 0;
        }
        #custom-smalcode .panel-category-sticky .image-wrap .pfb-1.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1.active {
            opacity: 1;
        }
        #custom-smalcode .panel-category-sticky .image-wrap .pfb-1 .indicator,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator {
            width: 26px;
            height: 26px;
            position: absolute;
            cursor: pointer;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator
            .inner-bullet,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator
            .inner-bullet {
            width: 12px;
            height: 12px;
            background-color: white;
            position: absolute;
            border-radius: 100%;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
            will-change: transform;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator
            .inner-circle,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator
            .inner-circle {
            width: 26px;
            height: 26px;
            border-radius: 100%;
            border: 1px solid #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
            will-change: width, height, opacity;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator:not(.active)
            .inner-circle,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator:not(.active)
            .inner-circle {
            -webkit-animation: indicatorLoop 2s infinite;
            animation: indicatorLoop 2s infinite;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator.active
            .inner-bullet,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator.active
            .inner-bullet {
            background-color: #fecb00;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator.active
            .inner-circle,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator.active
            .inner-circle {
            border: 1px solid #fecb00;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator.indicator-1,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator.indicator-1 {
            top: 47%;
            left: 69%;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator.indicator-2,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator.indicator-2 {
            top: 77%;
            left: 66%;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator.indicator-3,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator.indicator-3 {
            top: 75%;
            left: 6%;
        }
        #custom-smalcode
            .panel-category-sticky
            .image-wrap
            .pfb-1
            .indicator.indicator-4,
        #custom-smalcode
            .panel-category-sticky-mobile
            .image-wrap
            .pfb-1
            .indicator.indicator-4 {
            top: 62%;
            left: 57%;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode
                .panel-category-sticky
                .image-wrap
                .pfb-1
                .indicator.indicator-4,
            #custom-smalcode
                .panel-category-sticky-mobile
                .image-wrap
                .pfb-1
                .indicator.indicator-4 {
                top: 59%;
                left: 55%;
            }
        }
        #custom-smalcode .panel-category-sticky .inner-panel-wrap-connectivity,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity {
            position: absolute;
            left: 0;
            width: 100%;
            top: 0;
            -webkit-transform: translate(0, 90vh);
            transform: translate(0, 90vh);
            -webkit-transform: translate(0, calc(var(--vh) * 90));
            transform: translate(0, calc(var(--vh) * 90));
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity.active {
            -webkit-transform: translate(0, 50vh) translate(0, -50%);
            transform: translate(0, 50vh) translate(0, -50%);
            -webkit-transform: translate(0, calc(var(--vh) * 50))
                translate(0, -50%);
            transform: translate(0, calc(var(--vh) * 50)) translate(0, -50%);
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity.fadeAway,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity.fadeAway {
            -webkit-transform: translate(0, 0vh) translate(0, -100%);
            transform: translate(0, 0vh) translate(0, -100%);
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .element-usp-wrap,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .element-usp-wrap {
            margin-top: 60px;
            margin-bottom: 25px;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .element-usp-wrap
            .element-usp-item,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .element-usp-wrap
            .element-usp-item {
            opacity: 0.3;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .element-usp-wrap
            .element-usp-item.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .element-usp-wrap
            .element-usp-item.active {
            opacity: 1;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .info-elements-wrap,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-elements-wrap {
            position: relative;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .info-element,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element {
            opacity: 0;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .info-element.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element.active {
            opacity: 1;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-wrap-connectivity
            .info-element
            .info-headline,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element
            .info-headline {
            margin-bottom: 25px;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-title,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-title {
            position: absolute;
            left: 0;
            width: 100%;
            top: 0;
            -webkit-transform: translate(0, 100vh);
            transform: translate(0, 100vh);
            -webkit-transform: translate(0, calc(var(--vh) * 100));
            transform: translate(0, calc(var(--vh) * 100));
            pointer-events: none;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-title.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-title.active {
            -webkit-transform: translate(0, 10vh);
            transform: translate(0, 10vh);
            -webkit-transform: translate(0, calc(var(--vh) * 10));
            transform: translate(0, calc(var(--vh) * 10));
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            -webkit-transform: translate(0, 100vh);
            transform: translate(0, 100vh);
            -webkit-transform: translate(0, calc(var(--vh) * 100));
            transform: translate(0, calc(var(--vh) * 100));
            pointer-events: none;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides.active {
            -webkit-transform: translate(0, 10vh) translate(0, 25px);
            transform: translate(0, 10vh) translate(0, 25px);
            -webkit-transform: translate(0, calc(var(--vh) * 10))
                translate(0, 25px);
            transform: translate(0, calc(var(--vh) * 10)) translate(0, 25px);
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .images-slide-wrap,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .images-slide-wrap {
            position: relative;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask.active {
            opacity: 1;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask
            .mask,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask
            .mask {
            background-color: #4a4a4c;
            -webkit-transition: none;
            transition: none;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask
            .mask.transition-allowed,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask
            .mask.transition-allowed {
            -webkit-transition: 0.5s ease-out;
            transition: 0.5s ease-out;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask:before,
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask:after,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask:before,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask:after {
            content: '';
            position: absolute;
            left: 0;
            width: 100%;
            height: 1px;
            background: black;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask:before,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask:before {
            top: 0;
        }
        #custom-smalcode
            .panel-category-sticky
            .prepare-for-battle-image-slides
            .element-image-mask:after,
        #custom-smalcode
            .panel-category-sticky-mobile
            .prepare-for-battle-image-slides
            .element-image-mask:after {
            bottom: 0;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            -webkit-transform: translate(0, 150vh);
            transform: translate(0, 150vh);
            -webkit-transform: translate(0, calc(var(--vh) * 150));
            transform: translate(0, calc(var(--vh) * 150));
            pointer-events: none;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos.active {
            -webkit-transform: translate(0, 58vh);
            transform: translate(0, 58vh);
            -webkit-transform: translate(0, calc(var(--vh) * 62));
            transform: translate(0, calc(var(--vh) * 62));
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap {
            position: relative;
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info {
            padding-left: 25px;
            opacity: 0;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            -webkit-transition: 0.4s ease-out;
            transition: 0.4s ease-out;
            will-change: opacity, transform;
            -webkit-transform: translate(-30px, 0);
            transform: translate(-30px, 0);
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info.active,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info.active {
            opacity: 1;
            -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info.fade-out,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info.fade-out {
            opacity: 0;
            -webkit-transform: translate(30px, 0);
            transform: translate(30px, 0);
        }
        #custom-smalcode
            .panel-category-sticky
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info
            .pfb-info-headline,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle-infos
            .pfb-infos-wrap
            .pfb-info
            .pfb-info-headline {
            margin-top: 5px;
        }
        #custom-smalcode .panel-category-sticky-mobile .seperator {
            margin-top: 12px;
            margin-bottom: 8px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity {
            position: relative;
            -webkit-transform: none;
            transform: none;
            padding-top: 40px;
            padding-bottom: 30px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-elements-wrap {
            max-width: 500px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .element-usp-wrap {
            margin-top: 30px;
            margin-bottom: 40px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .element-usp-item {
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element {
            position: relative;
            opacity: 1;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element
            .info-headline {
            margin-bottom: 12px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element
            .bg-layer {
            padding-top: 30px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-wrap-connectivity
            .info-element
            .text-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 50%;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle {
            padding-top: 60px;
            padding-bottom: 40px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .row-headline {
            text-align: center;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .image-wrap {
            position: relative;
            top: 0;
            -webkit-transform: none;
            transform: none;
            width: 80%;
            margin-left: auto;
            margin-right: auto;
            margin-top: 40px;
            margin-bottom: 30px;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .image-wrap
            .pfb-1 {
            opacity: 1;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode
                .panel-category-sticky-mobile
                .inner-panel-prepare-for-battle
                .image-wrap {
                width: 100%;
            }
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .prepare-for-battle-slides {
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .prepare-for-battle-slides
            .images-slide-wrap
            .element-image-slide
            .inner-slide {
            display: -webkit-box;
            display: flex;
            -webkit-box-align: start;
            align-items: flex-start;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .prepare-for-battle-slides
            .images-slide-wrap
            .element-image-slide
            .inner-slide
            img,
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .prepare-for-battle-slides
            .images-slide-wrap
            .element-image-slide
            .inner-slide
            .pfb-info {
            width: 50%;
        }
        #custom-smalcode
            .panel-category-sticky-mobile
            .inner-panel-prepare-for-battle
            .prepare-for-battle-slides
            .images-slide-wrap
            .element-image-slide
            .inner-slide
            .pfb-info {
            padding-left: 20px;
        }

        @-webkit-keyframes indicatorLoop {
            0% {
                width: 26px;
                height: 26px;
                opacity: 1;
            }
            100% {
                width: 60px;
                height: 60px;
                opacity: 0;
            }
        }

        @keyframes indicatorLoop {
            0% {
                width: 26px;
                height: 26px;
                opacity: 1;
            }
            100% {
                width: 60px;
                height: 60px;
                opacity: 0;
            }
        }
        #custom-smalcode .panel-programmable-black {
            position: relative;
            padding-bottom: 50px;
            padding-top: 5vh;
            background-color: #4a4a4c;
        }
        #custom-smalcode .panel-programmable-black .row-with-space {
            margin-bottom: 50px;
        }
        #custom-smalcode .panel-programmable-black .row-usps {
            margin-top: 65px;
            margin-bottom: 65px;
        }
        #custom-smalcode .panel-programmable-black .cta-white {
            color: black;
        }
        #custom-smalcode .panel-programmable-black .legal-text {
            margin-top: 60px;
            font-size: 14px;
            line-height: 22px;
            font-family: Tomorrow, aktiv-grotesk, sans-serif;
            letter-spacing: inherit;
            opacity: 0.7;
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-programmable-black .legal-text {
                margin-top: 20px;
                margin-bottom: 50px;
            }
        }
        #custom-smalcode .panel-programmable-black .element-icue-software {
            margin-bottom: 50px;
        }
        @media only screen and (min-width: 1024px) {
            #custom-smalcode .panel-programmable-black .col-text {
                padding-right: 50px;
            }
        }
        @media only screen and (max-width: 1023px) {
            #custom-smalcode .panel-programmable-black {
                padding-top: 60px;
                padding-bottom: 30px;
            }
            #custom-smalcode .panel-programmable-black .element-icue-software {
                margin-top: 25px;
            }
            #custom-smalcode
                .panel-programmable-black
                .element-icue-software
                .icue-header {
                margin-bottom: 20px;
            }
            #custom-smalcode .panel-programmable-black .row-usps {
                margin-top: 40px;
                margin-bottom: 30px;
            }
            #custom-smalcode .panel-programmable-black .usp-outer-wrap {
                display: block;
            }
            #custom-smalcode .panel-programmable-black .element-usp-wrap-1 {
                margin-right: 0;
            }
            #custom-smalcode .panel-programmable-black .element-usp-wrap-1,
            #custom-smalcode .panel-programmable-black .element-usp-wrap-2 {
                margin-top: 40px;
            }
        }
        #custom-smalcode .panel-discover-more {
            position: relative;
            height: 100vh;
            height: calc(var(--vh) * 100);
            overflow: hidden;
            background-color: #4a4a4c;
        }
        #custom-smalcode .panel-discover-more .bg-layer {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            -webkit-transform: scale(1.5);
            transform: scale(1.5);
            opacity: 0;
        }
        #custom-smalcode .panel-discover-more .bg-layer.active {
            -webkit-transform: scale(1);
            transform: scale(1);
            opacity: 1;
        }
        #custom-smalcode .panel-discover-more .img-wrap {
            position: absolute;
            height: 100%;
            width: 100%;
            left: 0;
            bottom: 0;
        }
        #custom-smalcode .panel-discover-more .img-wrap img {
            height: 90%;
            width: auto;
            position: absolute;
            bottom: 0;
            right: 10vw;
            opacity: 0;
            -webkit-transition: 0.3s ease-out;
            transition: 0.3s ease-out;
            will-change: opacity;
        }
        #custom-smalcode .panel-discover-more .img-wrap img.active {
            opacity: 1;
        }
        #custom-smalcode .panel-discover-more .text-layer {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            -webkit-transform: translate(0, -50%);
            transform: translate(0, -50%);
            text-align: center;
        }
        #custom-smalcode .panel-discover-more .text-layer .discover-headline {
            text-transform: uppercase;
            font-family: 'Verveine';
            font-weight: normal;
            font-size: 60px;
            line-height: 60px;
            margin-top: 40px;
            margin-bottom: 70px;
        }
        #custom-smalcode .panel-discover-more .text-layer .menu-items-wrap {
            font-size: 40px;
            text-transform: uppercase;
            font-family: Tomorrow, sans-serif;
            font-weight: bold;
        }
        #custom-smalcode .panel-discover-more .text-layer .menu-items-wrap a {
            margin-right: 50px;
            opacity: 0.5;
            -webkit-transition: 0.2s ease-out;
            transition: 0.2s ease-out;
            will-change: opacity;
        }
        #custom-smalcode
            .panel-discover-more
            .text-layer
            .menu-items-wrap
            a.active {
            opacity: 1;
        }
        #custom-smalcode
            .panel-discover-more
            .text-layer
            .menu-items-wrap
            a:last-of-type {
            margin-right: 0;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode .panel-discover-more {
                height: 70vh;
                height: calc(var(--vh) * 70);
            }
            #custom-smalcode .panel-discover-more .img-wrap img {
                width: 110%;
                right: 55%;
                -webkit-transform: translate(50%, 0);
                transform: translate(50%, 0);
                height: auto;
            }
            #custom-smalcode .panel-discover-more .text-layer {
                top: 0;
                -webkit-transform: none;
                transform: none;
            }
            #custom-smalcode .panel-discover-more .text-layer [class^='col-'] {
                padding-left: 0;
                padding-right: 0;
            }
            #custom-smalcode
                .panel-discover-more
                .text-layer
                .discover-headline {
                padding-left: 15px;
                padding-right: 15px;
                margin-top: 30px;
                margin-bottom: 130px;
            }
            #custom-smalcode
                .panel-discover-more
                .text-layer
                .discover-headline {
                font-size: 40px;
                line-height: 48px;
            }
            #custom-smalcode .panel-discover-more .text-layer .menu-items-wrap {
                font-size: 30px;
            }
            #custom-smalcode
                .panel-discover-more
                .text-layer
                .menu-items-wrap
                a {
                margin-right: 0;
                display: block;
                width: 100%;
            }
            #custom-smalcode
                .panel-discover-more
                .text-layer
                .menu-items-wrap
                a:last-of-type {
                margin-right: 0;
            }
            #custom-smalcode
                .panel-discover-more
                .text-layer
                .menu-items-wrap
                a:not(.active) {
                pointer-events: none;
            }
        }

        /* ==========================================================================
             general
             ========================================================================== */
        body {
            margin: 0;
            color: #000;
        }

        #custom-smalcode {
            --vh: 1vh;
            background-color: #fff;
            position: relative;
            z-index: 1;
        }
        #custom-smalcode img {
            display: block;
            width: 100%;
            height: auto;
        }
        #custom-smalcode a {
            color: inherit;
            text-decoration: none;
        }
        #custom-smalcode ul,
        #custom-smalcode li {
            margin: 0;
            padding: 0;
            list-style-type: none;
        }
        #custom-smalcode * {
            -webkit-font-smoothing: subpixel-antialiased;
            -moz-osx-font-smoothing: grayscale;
            box-sizing: border-box;
        }
        #custom-smalcode .slick-slide {
            outline: 0;
        }
        #custom-smalcode.device-is-touch .touch-hide {
            display: none;
        }
        #custom-smalcode.device-is-touch .mobile.touch-show {
            display: block;
        }
        #custom-smalcode:not(.device-is-touch) .touch-show {
            display: none;
        }
        @media only screen and (max-width: 767px) {
            #custom-smalcode.device-is-touch .touch-hide,
            #custom-smalcode:not(.device-is-touch) .touch-hide {
                display: none;
            }
            #custom-smalcode.device-is-touch .mobile.touch-show,
            #custom-smalcode:not(.device-is-touch) .mobile.touch-show {
                display: block;
            }
        }

        #pdp-corsair .virtuoso-overview,
        .main-content .virtuoso-overview {
            text-align: center;
        }

        // #pdp-corsair .virtuoso-overview .features-overview {
        //     background-color: #f9f8f8;

        //     &section {
        //         background-color: #fff;
        //     }
        // }

        #pdp-corsair .virtuoso-overview .feature-pre,
        .main-content .virtuoso-overview .feature-pre {
            position: relative;
        }

        @media (min-width: 1348px) {
            #pdp-corsair .virtuoso-overview .feature-abs,
            .main-content .virtuoso-overview .feature-abs {
                position: relative;
                height: 822px;
                margin: 3rem auto 0;
            }
            #pdp-corsair .virtuoso-overview .feature-abs img,
            .main-content .virtuoso-overview .feature-abs img {
                position: absolute;
                width: 39.69%;
                height: 65.95%;
                top: 9%;
                left: 31.72%;
            }
        }

        #pdp-corsair .virtuoso-overview .feature-list,
        .main-content .virtuoso-overview .feature-list {
            text-align: left;
            list-style-type: none;
        }

        @media (min-width: 1348px) {
            #pdp-corsair .virtuoso-overview .feature-list li,
            .main-content .virtuoso-overview .feature-list li {
                position: absolute;
                width: 20em;
            }
            #pdp-corsair .virtuoso-overview .feature-list .has-callout:after,
            #pdp-corsair .virtuoso-overview .feature-list .has-callout:before,
            .main-content .virtuoso-overview .feature-list .has-callout:after,
            .main-content .virtuoso-overview .feature-list .has-callout:before {
                content: '';
                position: absolute;
                opacity: 0;
                -webkit-transition: opacity 2s;
                transition: opacity 2s;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list
                .has-callout.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list
                .has-callout.is-active:after {
                border: 1px solid #fecb00;
                opacity: 1;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list
                .has-callout.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list
                .has-callout.is-active:before {
                width: 5px;
                height: 5px;
                border-radius: 5px;
                background: #fecb00;
                margin-top: -2px;
                margin-left: -3px;
                opacity: 1;
            }
            #pdp-corsair .virtuoso-overview .feature-list_durable,
            .main-content .virtuoso-overview .feature-list_durable {
                top: 0;
                right: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_durable.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list_durable.is-active:after {
                top: 0.4rem;
                right: 105%;
                width: 45%;
                height: 230%;
                border-width: 1px 0 0 1px !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_durable.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list_durable.is-active:before {
                top: 230%;
                right: 150%;
                -webkit-transform: translateY(0.4rem);
                transform: translateY(0.4rem);
                margin-right: -3px;
            }
            #pdp-corsair .virtuoso-overview .feature-list_comfort,
            .main-content .virtuoso-overview .feature-list_comfort {
                top: 37.5%;
                left: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_comfort.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list_comfort.is-active:after {
                top: 0.4rem;
                left: 50%;
                width: 135%;
                height: 55%;
                border-width: 1px 1px 0 0 !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_comfort.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list_comfort.is-active:before {
                top: 55%;
                left: 185%;
                -webkit-transform: translateY(0.4rem);
                transform: translateY(0.4rem);
            }
            #pdp-corsair .virtuoso-overview .feature-list_sound,
            .main-content .virtuoso-overview .feature-list_sound {
                top: 37.5%;
                right: 0;
            }
            #pdp-corsair .virtuoso-overview .feature-list_sound.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list_sound.is-active:after {
                top: 0.4rem;
                right: 105%;
                width: 70%;
                height: 90%;
                border-width: 1px 0 0 1px !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_sound.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list_sound.is-active:before {
                top: 90%;
                right: 175%;
                -webkit-transform: translateY(0.4rem);
                transform: translateY(0.4rem);
                margin-right: -3px;
            }
            #pdp-corsair .virtuoso-overview .feature-list_customize,
            .main-content .virtuoso-overview .feature-list_customize {
                top: 75%;
                left: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_customize.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list_customize.is-active:after {
                top: -70%;
                left: 50%;
                width: 95%;
                height: 75%;
                border-width: 0 1px 1px 0 !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_customize.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list_customize.is-active:before {
                top: -70%;
                left: 145%;
            }
            #pdp-corsair .virtuoso-overview .feature-list_microphone,
            .main-content .virtuoso-overview .feature-list_microphone {
                top: 75%;
                right: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_microphone.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list_microphone.is-active:after {
                top: 0.4rem;
                right: 105%;
                width: 15%;
                height: 30%;
                border-width: 1px 0 0 0 !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_microphone.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list_microphone.is-active:before {
                top: 0.4rem;
                right: 120%;
                margin-right: -3px;
            }
            #pdp-corsair .virtuoso-overview .feature-list_storage,
            .main-content .virtuoso-overview .feature-list_storage {
                top: 75%;
                right: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_storage.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list_storage.is-active:after {
                top: 0.4rem;
                right: 105%;
                width: 15%;
                height: 30%;
                border-width: 1px 0 0 0 !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list_storage.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list_storage.is-active:before {
                top: 0.4rem;
                right: 120%;
                margin-right: -3px;
            }
            #pdp-corsair .virtuoso-overview,
            .main-content .virtuoso-overview {
                padding-bottom: 5rem;

                .feature-list--se {
                    margin-bottom: 0;
                }
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se
                .has-callout.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list--se
                .has-callout.is-active:after {
                border: 1px solid #3771d5;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se
                .has-callout.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list--se
                .has-callout.is-active:before {
                background: #3771d5;
            }
            #pdp-corsair .virtuoso-overview .feature-list--se_construction,
            .main-content .virtuoso-overview .feature-list--se_construction {
                top: 20%;
                left: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_construction.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list--se_construction.is-active:after {
                top: 1rem;
                left: 95%;
                width: 70%;
                height: 170%;
                border-width: 1px 1px 0 0 !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_construction.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list--se_construction.is-active:before {
                top: 170%;
                left: 165%;
                margin-right: -3px;
                -webkit-transform: translateY(1rem);
                transform: translateY(1rem);
            }
            #pdp-corsair .virtuoso-overview .feature-list--se_lighting,
            .main-content .virtuoso-overview .feature-list--se_lighting {
                top: 55%;
                left: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_lighting.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list--se_lighting.is-active:after {
                top: 1rem;
                left: 95%;
                width: 100%;
                height: 50%;
                border-width: 1px 1px 0 0 !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_lighting.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list--se_lighting.is-active:before {
                top: 50%;
                left: 195%;
                margin-right: -3px;
                -webkit-transform: translateY(1rem);
                transform: translateY(1rem);
            }
            #pdp-corsair .virtuoso-overview .feature-list--se_pouch,
            .main-content .virtuoso-overview .feature-list--se_pouch {
                top: 20%;
                right: 0;
            }
            #pdp-corsair .virtuoso-overview .feature-list--se_microphone,
            .main-content .virtuoso-overview .feature-list--se_microphone {
                top: 50%;
                right: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_microphone.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list--se_microphone.is-active:after {
                top: 1rem;
                right: 105%;
                width: 10%;
                height: 130%;
                border-width: 1px 0 0 1px !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_microphone.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list--se_microphone.is-active:before {
                top: 130%;
                right: 115%;
                margin-right: -3px;
                -webkit-transform: translateY(1rem);
                transform: translateY(1rem);
            }

            #pdp-corsair .virtuoso-overview .feature-list--se_storage,
            .main-content .virtuoso-overview .feature-list--se_storage {
                top: 77%;
                right: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_storage.is-active:after,
            .main-content
                .virtuoso-overview
                .feature-list--se_storage.is-active:after {
                top: 1rem;
                right: 105%;
                width: 10%;
                height: 130%;
                border-width: 1px 0 0 1px !important;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--se_storage.is-active:before,
            .main-content
                .virtuoso-overview
                .feature-list--se_storage.is-active:before {
                top: 130%;
                right: 115%;
                margin-right: -3px;
                -webkit-transform: translateY(1rem);
                transform: translateY(1rem);
            }
        }

        #pdp-corsair .virtuoso-overview .feature-list li,
        .main-content .virtuoso-overview .feature-list li {
            max-width: 30em;
            margin: 0 auto 1em;
            padding-bottom: 0.5rem;
        }

        #pdp-corsair .virtuoso-overview .feature-list h4,
        .main-content .virtuoso-overview .feature-list h4 {
            cursor: pointer;
            text-transform: uppercase;
            position: relative;
            padding-left: 2.5em;
            margin-bottom: 1em;
            color: #19212b;
        }

        #pdp-corsair .virtuoso-overview .feature-list h4:before,
        .main-content .virtuoso-overview .feature-list h4:before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 2em;
            height: 2em;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            background: url('data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6IzFkMWQxYjt9LmNscy0yLC5jbHMtM3tmaWxsOm5vbmU7fS5jbHMtMntzdHJva2U6IzFkMWQxYjt9LmNscy0ze3N0cm9rZTojZmZmO3N0cm9rZS13aWR0aDoxLjVweDt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPnBsdXMtY2lyY2xlPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0yNSw0OUEyNCwyNCwwLDEsMCwxLDI1LDI0LDI0LDAsMCwwLDI1LDQ5Ii8+PGNpcmNsZSBjbGFzcz0iY2xzLTIiIGN4PSIyNSIgY3k9IjI1IiByPSIyNCIvPjxsaW5lIGNsYXNzPSJjbHMtMyIgeDE9IjI1IiB5MT0iMTIuNDgiIHgyPSIyNSIgeTI9IjM3LjUzIi8+PGxpbmUgY2xhc3M9ImNscy0zIiB4MT0iMzcuNTMiIHkxPSIyNSIgeDI9IjEyLjQ3IiB5Mj0iMjUiLz48L3N2Zz4=')
                center no-repeat;
            background-size: 2em 2em;
            -webkit-transition: -webkit-transform 0.2s ease-out;
            transition: -webkit-transform 0.2s ease-out;
            transition: transform 0.2s ease-out;
            transition: transform 0.2s ease-out, -webkit-transform 0.2s ease-out;
        }

        #pdp-corsair .virtuoso-overview .feature-list .is-active h4:before,
        .main-content .virtuoso-overview .feature-list .is-active h4:before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZmZGEwMDt9LmNscy0ye2ZpbGw6bm9uZTtzdHJva2U6I2ZmZjtzdHJva2Utd2lkdGg6MS41cHg7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5taW51cy15ZWxsb3c8L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTI1LDQ5QTI0LDI0LDAsMSwwLDEsMjUsMjQsMjQsMCwwLDAsMjUsNDkiLz48bGluZSBjbGFzcz0iY2xzLTIiIHgxPSIzNy41MyIgeTE9IjI1IiB4Mj0iMTIuNDciIHkyPSIyNSIvPjwvc3ZnPg==');
            -webkit-transform: translateY(-50%) rotate(180deg);
            transform: translateY(-50%) rotate(180deg);
        }

        #pdp-corsair .virtuoso-overview .feature-list--se .is-active h4:before,
        .main-content
            .virtuoso-overview
            .feature-list--se
            .is-active
            h4:before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6IzM3NzFkNTt9LmNscy0ye2ZpbGw6bm9uZTtzdHJva2U6I2ZmZjtzdHJva2Utd2lkdGg6MS41cHg7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5taW51cy1ibHVlPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0yNSw0OUEyNCwyNCwwLDEsMCwxLDI1LDI0LDI0LDAsMCwwLDI1LDQ5Ii8+PGxpbmUgY2xhc3M9ImNscy0yIiB4MT0iMzcuNTMiIHkxPSIyNSIgeDI9IjEyLjQ3IiB5Mj0iMjUiLz48L3N2Zz4=');
        }

        #pdp-corsair
            .virtuoso-overview
            .feature-list--se
            .is-yellow.is-active
            h4:before,
        .main-content
            .virtuoso-overview
            .feature-list--se
            .is-yellow.is-active
            h4:before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZmZGEwMDt9LmNscy0ye2ZpbGw6bm9uZTtzdHJva2U6I2ZmZjtzdHJva2Utd2lkdGg6MS41cHg7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5taW51cy15ZWxsb3c8L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTI1LDQ5QTI0LDI0LDAsMSwwLDEsMjUsMjQsMjQsMCwwLDAsMjUsNDkiLz48bGluZSBjbGFzcz0iY2xzLTIiIHgxPSIzNy41MyIgeTE9IjI1IiB4Mj0iMTIuNDciIHkyPSIyNSIvPjwvc3ZnPg==');
        }

        #pdp-corsair .virtuoso-overview .feature-list p,
        .main-content .virtuoso-overview .feature-list p {
            display: none;
            margin-bottom: 0.2em;
            margin-top: 0;
            color: #19312b;
            font-size: 12px;
            font-weight: 400;
            letter-spacing: 0.6px;
            line-height: 21.6px;
        }

        #pdp-corsair .virtuoso-overview .feature-list .is-active p,
        .main-content .virtuoso-overview .feature-list .is-active p {
            display: block;
        }

        @media (min-width: 1348px) {
            #pdp-corsair .virtuoso-overview .feature-list p,
            .main-content .virtuoso-overview .feature-list p {
                display: block !important;
                opacity: 0;
                -webkit-transform: translateY(3rem);
                transform: translateY(3rem);
                -webkit-transition: 0.2s;
                transition: 0.2s;
            }
            #pdp-corsair .virtuoso-overview .feature-list .is-active p,
            .main-content .virtuoso-overview .feature-list .is-active p {
                opacity: 1;
                -webkit-transform: translateY(0rem);
                transform: translateY(0rem);
            }
        }

        #pdp-corsair .virtuoso-overview .feature-list.no-hover p,
        .main-content .virtuoso-overview .feature-list.no-hover p {
            display: none;
        }

        #pdp-corsair .virtuoso-overview .feature-list.no-hover .is-active p,
        .main-content .virtuoso-overview .feature-list.no-hover .is-active p {
            display: block;
        }

        #pdp-corsair .virtuoso-overview .feature-list--left > h4,
        #pdp-corsair .virtuoso-overview .feature-list--right > h4,
        .main-content .virtuoso-overview .feature-list--left > h4,
        .main-content .virtuoso-overview .feature-list--right > h4 {
            text-align: left;
            border-bottom: 1px solid;
            margin-bottom: 1.5rem;
        }

        #pdp-corsair .virtuoso-overview .feature-list--left p,
        #pdp-corsair .virtuoso-overview .feature-list--right p,
        .main-content .virtuoso-overview .feature-list--left p,
        .main-content .virtuoso-overview .feature-list--right p {
            display: none !important;
        }

        #pdp-corsair .virtuoso-overview .feature-list--left .is-active p,
        #pdp-corsair .virtuoso-overview .feature-list--right .is-active p,
        .main-content .virtuoso-overview .feature-list--left .is-active p,
        .main-content .virtuoso-overview .feature-list--right .is-active p {
            display: block !important;
        }

        @media (min-width: 1348px) {
            #pdp-corsair .virtuoso-overview .feature-list--left,
            .main-content .virtuoso-overview .feature-list--left {
                position: absolute;
                top: 0;
                left: 0;
            }
            #pdp-corsair .virtuoso-overview .feature-list--right,
            .main-content .virtuoso-overview .feature-list--right {
                position: absolute;
                top: 0;
                right: 0;
            }
            #pdp-corsair
                .virtuoso-overview
                .feature-list--right
                .is-active
                h4:before,
            .main-content
                .virtuoso-overview
                .feature-list--right
                .is-active
                h4:before {
                background-image: url('data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6IzM3NzFkNTt9LmNscy0ye2ZpbGw6bm9uZTtzdHJva2U6I2ZmZjtzdHJva2Utd2lkdGg6MS41cHg7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5taW51cy1ibHVlPC90aXRsZT48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0yNSw0OUEyNCwyNCwwLDEsMCwxLDI1LDI0LDI0LDAsMCwwLDI1LDQ5Ii8+PGxpbmUgY2xhc3M9ImNscy0yIiB4MT0iMzcuNTMiIHkxPSIyNSIgeDI9IjEyLjQ3IiB5Mj0iMjUiLz48L3N2Zz4=');
            }
            #pdp-corsair .virtuoso-overview .feature-list--left li,
            #pdp-corsair .virtuoso-overview .feature-list--right li,
            .main-content .virtuoso-overview .feature-list--left li,
            .main-content .virtuoso-overview .feature-list--right li {
                position: static !important;
            }
        }

        .virtuoso-overview h2 {
            @apply font-tomorrow;
            color: #000 !important;
            font-weight: 600;
            font-size: 1.875rem;
            line-height: 2rem;
            --tw-text-opacity: 1;
            color: rgba(255, 255, 255, var(--tw-text-opacity));
            letter-spacing: 0.0625rem;
        }
        @media (min-width: 1024px) {
            .virtuoso-overview h2 {
                font-size: 3.75rem;
                line-height: 3.75rem;
            }
        }
        @media (max-width: 768px) {
            .feature-pre img {
                transform: translateX(-25%);
            }
        }
    }
}
