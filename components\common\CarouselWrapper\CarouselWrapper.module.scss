.carousel-container {
    --desktop-pt: 0;
    --desktop-pb: 0;
    --mobile-pt: 0;
    --mobile-pb: 0;

    @apply bg-cover bg-center bg-no-repeat;

    padding-top: var(--mobile-pt);
    padding-bottom: var(--mobile-pb);
    background-color: var(--bg-color);
    background-image: var(--mobile-bg-image);

    @screen md {
        padding-top: var(--desktop-pt);
        padding-bottom: var(--desktop-pb);
        background-image: var(--desktop-bg-image);
    }
}
.carousel-top-container {
    @apply flex items-center justify-between;
    padding-inline: 20px;
    padding-bottom: var(--mobile-pt);
    @screen md {
        padding-inline: 80px;
        padding-bottom: var(--desktop-pt);
    }

    .heading {
        @apply font-tomorrow font-medium;
        color: var(--primary);
        line-height: 100%;

        &.heading-h1 {
            font-size: 2.125rem;
        }
        &.heading-h2 {
            font-size: 2rem;
        }
        &.heading-h3 {
            font-size: 2rem;
        }
        &.heading-h4 {
            font-size: 2rem;
        }

        @screen md {
            &.heading-h1 {
                font-size: 3.375rem;
            }
            &.heading-h2 {
                font-size: 2.75rem;
            }
            &.heading-h3 {
                font-size: 2.25rem;
            }
            &.heading-h4 {
                font-size: 2rem;
            }
        }
    }
}
