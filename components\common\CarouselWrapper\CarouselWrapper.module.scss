.sticky-header {
    transition: all 0.2s ease-in;
    @apply bg-white w-full absolute;
    z-index: -1;
    overflow: hidden;
    transform: translateY(-120px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

    &[data-visible='true'] {
        transform: translateY(0);
        position: unset;
    }

    &--content {
        @apply flex items-center justify-between gap-3;
        height: 56px;
    }

    &--label {
        @apply font-tomorrow font-medium uppercase;
        font-size: 1rem;
        line-height: 1.5rem;

        @screen md-max {
            line-height: 1rem;
        }
    }

    &--button {
        @apply flex items-center z-50 font-medium leading-none m-auto;
        padding: 14px 30px;
        font-size: 18px;
        height: 46px;

        @screen md-max {
            padding: 14px 20px;
        }
    }
}
