import React from 'react'

import s from './Collapse.module.scss'
import { CollapseProps } from './types'

const Collapse: React.FC<CollapseProps> = ({ id, heading, children }) => {
    return (
        <details className={s['container']}>
            <summary id={id} className={s['trigger']}>
                <div className={s['trigger-content']}>
                    <h5 className={s['heading']} aria-label="heading">
                        {heading}
                    </h5>
                    <div className={s['icon']} />
                </div>
            </summary>
            <div className={s['content']}>{children}</div>
        </details>
    )
}

export default Collapse
