import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { CSSProperties } from 'react'
import BrandItem from './BrandItem'
import s from './BrandSelector.module.scss'
import { BrandSelectorInterface } from './BrandSelector.types'

type BrandSelectorProps = {
    contents: BrandSelectorInterface
}

const BrandSelector: React.FC<BrandSelectorProps> = ({
    contents
}: BrandSelectorProps) => {
    const { backgroundColor, brandItems, title, titleColor } = contents

    const isMobile = useMediaQuery('(max-width: 767px')

    return (
        <div
            className={s['brand-wrapper']}
            id={title}
            style={
                {
                    '--background-color': backgroundColor.value
                } as CSSProperties
            }
        >
            <div className="scuf-container md-max:px-0">
                <div className="title text-center">
                    <h2
                        style={
                            {
                                '--title-color': titleColor.value
                            } as CSSProperties
                        }
                    >
                        {title}
                    </h2>
                </div>
                <div className={s['brand-items']}>
                    {brandItems.map((item) => {
                        return (
                            <BrandItem
                                key={item.identifier}
                                item={item}
                                isMobile={isMobile}
                            />
                        )
                    })}
                </div>
            </div>
        </div>
    )
}

export default BrandSelector
