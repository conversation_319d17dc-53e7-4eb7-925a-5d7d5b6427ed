@mixin description {
    font-size: 1rem;
    letter-spacing: inherit;
    line-height: 1.5rem;
}

// Custom CSS properties support
.cta-button-root {
    color: var(--button-text-color, inherit);
    background-color: var(--button-bg-color, transparent);
    border-color: var(--button-border-color, currentColor);
    display: block;
    width: fit-content;
}

.component-cta {
    @apply text-base font-semibold not-italic flex items-center relative p-0;
    letter-spacing: 1.6px;
    padding: 14px 30px;

    &:focus-visible {
        outline: 2px solid #6780FF;
        outline-offset: 2px;
    }

    &[aria-disabled="true"] {
        @apply opacity-50 cursor-not-allowed;
        pointer-events: none;
    }

    &-wrapper {
        margin-top: 20px;

        [class*='scuf-button-link'] {
            .component-cta {
                padding: 0;
            }
        }

        @screen md-max {
            width: 100%;
            padding: 0 24px;
            margin-top: 10px;
        }
    }

    & a,
    button {
        @apply inline-flex items-center relative;
    }

    &-icon {
        @apply items-center justify-center inline-flex;
        width: 14px;
        height: 14px;

        @screen md {
            width: 24px;
            height: 24px;
        }
    }

    &.icon-left {
        flex-direction: row-reverse;
    }

    &.custom-icon {
        @apply gap-4;
    }

    &-background {
        padding: 1em 2em;
        line-height: 0.75rem;

        @screen md {
            line-height: 1.125rem;
        }
    }
}

.button-icon {
    width: 17px;
    height: 17px;
}

.video-modal-button {
    [class*='scuf-button-dark'] {
        @apply w-fit inline-flex justify-center items-center gap-x-4 font-tomorrow font-medium uppercase align-middle box-border;
        border-radius: 2px;
        font-size: 1rem;
        line-height: 0.875rem;
        color: var(--white);
        background-color: var(--secondary-off-black);
        border: 1px solid var(--tertiary-light-gray);
        transition: background-color 0.3s ease;

        &:hover {
            border: 1px solid transparent;
            color: var(--secondary-off-black);
            background-color: var(--tertiary-light-gray);
        }

        &:active {
            box-shadow: none;
        }

        &:focus-visible {
            background: var(--primary);
            color: var(--secondary-off-black);
            outline: 4px groove var(--feedback-vivid-blue);

            & .circle-play-icon {
                color: var(--secondary-off-black);
            }
        }
    }

    [class*='corsair-modal'] {
        @screen md-max {
            max-width: 100vw;
            overflow-x: hidden;
        }
    }

    [style*='height:500px'] {
        height: 85vh !important;
    }

    &--overlay {
        @apply invisible fixed top-0 left-0 bottom-0 right-0 opacity-0 pointer-events-none;
        z-index: 1000;

        &[data-open='true'] {
            @apply visible opacity-100;
            transition: opacity 0.3s ease;
        }
    }

    &--overlay-inner {
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        pointer-events: auto;
    }

    &--overlay-content {
        @apply flex items-center justify-center;
        width: 100%;
        padding: 0 20px;
        margin: auto;

        max-width: 1280px;
    }

    & .circle-play-icon {
        color: var(--primary);
        margin: -2px 0;
        text-decoration: none;
        line-height: 1;
    }

    &--transcript {
        margin-top: 8px;

        &[open] {
            & .video-modal-button--transcript-toggle-icon {
                transform: rotate(90deg);
            }

            & .video-modal-button--transcript-text {
                display: block;
                max-height: 15vh;
                overflow: auto;

                &::-webkit-scrollbar,
                &::-webkit-scrollbar-thumb {
                    width: 26px;
                    border-radius: 13px;
                    background-clip: padding-box;
                    border: 10px solid transparent;
                }

                &::-webkit-scrollbar-thumb {
                    box-shadow: inset 0 0 0 10px;
                    color: var(--white);
                }
            }
        }
    }

    &--transcript-toggle-icon {
        transform: rotate(0);
        // transition: transform 0.2s ease;
    }

    &--transcript-button {
        @apply text-white cursor-pointer flex justify-end items-center gap-2;
        font-size: 1rem;
        line-height: 1.5rem;
        height: 24px;

        &:hover {
            color: var(--colspan-light-bg);
        }

        &::marker,
        &::-webkit-details-marker {
            display: none;
        }
    }

    &--transcript-text {
        @apply text-white;
        display: none;
        font-family: 'Sofia Sans Semi Condensed', sans-serif;
        font-size: 1rem;
        max-width: 800px;
        margin: auto;
        animation: sweep 0.5s ease-in-out;
    }

    :global(.corsair-modal) {
        padding: 0 20px;
        // opacity: 0;

        &[open] :global(.corsair-modal--content) {
            animation: fadein 0.3s ease-in-out;
        }

        :global(.corsair-modal--content) {
            height: auto;
            max-height: 100%;
            width: 90vw;
            max-width: 1280px;
        }

        :global(.corsair-modal--close-btn) {
            top: 20px;
            left: 20px;
            width: 1.5rem;
            height: 1.5rem;
            box-shadow: 0 0 0 1px transparent;

            &:hover,
            &:focus {

                &::before,
                &::after {
                    box-shadow: 0 0 0 1px rgb(255, 255, 255);
                    transition: box-shadow 0.3s ease;
                }
            }
        }
    }
}

@keyframes sweep {
    0% {
        opacity: 0;
        margin-top: -10px;
    }

    100% {
        opacity: 1;
        margin-top: 0px;
    }
}

@keyframes fadein {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes slidein {
    0% {
        transform: translateY(-50%);
    }

    100% {
        transform: translateY(0);
    }
}
