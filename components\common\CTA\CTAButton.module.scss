/**
 * CTA Button Component Styles
 * Dedicated styles for CTAButton and related components
 */

// Base CTA component styles
.component-cta {
    @apply text-xs md:text-lg font-semibold not-italic flex relative p-0;
    line-height: 25px;
    letter-spacing: 1.6px;

    @screen md {
        line-height: 30px;
    }

    span {
        @apply flex items-center;
    }
}

// CTA wrapper for link buttons
.component-cta-wrapper {
    @apply inline-flex items-center justify-center;
    transition: all 0.2s ease-in-out;
    text-decoration: none;

    &:hover {
        text-decoration: none;
    }

    @screen md-max {
        @apply m-0 px-5;
    }
}

// Button type variants
.button-primary {
    @apply bg-yellow text-black border border-yellow;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease-in-out;

    &:hover {
        background: rgba(236, 232, 26, 0.8);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:focus {
        outline: 2px solid #6780FF;
        outline-offset: 2px;
    }

    &:active {
        transform: translateY(0);
    }
}

.button-secondary {
    @apply bg-transparent text-white border border-white;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease-in-out;

    &:hover {
        @apply bg-white text-black;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &:focus {
        outline: 2px solid #6780FF;
        outline-offset: 2px;
    }

    &:active {
        transform: translateY(0);
    }
}

.button-link {
    @apply bg-transparent text-current border-none underline;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease-in-out;

    &:hover {
        text-decoration: none;
        opacity: 0.8;
    }

    &:focus {
        outline: 2px solid #6780FF;
        outline-offset: 2px;
    }
}

// Icon styles
.button-icon {
    @apply ml-2 flex items-center justify-center;
    transition: transform 0.2s ease-in-out;

    .button-primary:hover &,
    .button-secondary:hover &,
    .button-link:hover & {
        transform: translateX(2px);
    }
}

// Modal specific styles
.video-modal-button {
    &--transcript {
        @apply mt-4 border border-gray-300 rounded;

        summary {
            @apply p-3 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors;
            list-style: none;

            &::-webkit-details-marker {
                display: none;
            }
        }
    }

    &--transcript-button {
        @apply flex items-center justify-between font-medium;
    }

    &--transcript-toggle-icon {
        @apply ml-2 transition-transform duration-200;
        
        details[open] & {
            transform: rotate(90deg);
        }
    }

    &--transcript-text {
        @apply p-4 border-t border-gray-200 bg-white;
        line-height: 1.6;

        // Sanitize and style HTML content
        p {
            @apply mb-3;
            
            &:last-child {
                @apply mb-0;
            }
        }

        ul, ol {
            @apply ml-4 mb-3;
        }

        li {
            @apply mb-1;
        }

        strong, b {
            @apply font-semibold;
        }

        em, i {
            @apply italic;
        }
    }
}

// Accessibility improvements
.button-primary,
.button-secondary,
.button-link {
    &:focus-visible {
        outline: 2px solid #6780FF;
        outline-offset: 2px;
    }

    &[aria-disabled="true"] {
        @apply opacity-50 cursor-not-allowed;
        pointer-events: none;
    }
}

// Loading state
.button-loading {
    @apply relative;

    &::after {
        content: '';
        @apply absolute inset-0 bg-current opacity-20 rounded;
        animation: pulse 1.5s ease-in-out infinite;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.4;
    }
}

// Responsive adjustments
@screen md-max {
    .component-cta {
        font-size: 14px;
        letter-spacing: 1.2px;
    }

    .button-primary,
    .button-secondary {
        padding: 10px 20px;
        font-size: 14px;
    }

    .button-link {
        padding: 6px 12px;
        font-size: 14px;
    }
}

// Custom CSS properties support
.cta-button-root {
    color: var(--button-text-color, inherit);
    background-color: var(--button-bg-color, transparent);
    border-color: var(--button-border-color, currentColor);
}
