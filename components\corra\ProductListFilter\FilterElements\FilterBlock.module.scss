.FilterBlockAccordion {
    @apply border-none;

    > div {
        padding: 0;
    }

    [class*='accordion-wrapper'] {
        border-top: none;
        border-left: none;
        border-right: none;
        border-bottom: 1px solid var(--steel-gray20);

        div[class*='accordion-body'] {
            padding: 0px 8px 5px 8px;
            max-height: 250px;
            @apply overflow-y-auto;
        }

        div[class*='accordion-header'] {
            margin-bottom: 0px;
            padding-block: 8px;
            [class*='accordion-header-title'] {
                font-size: 16px;
                line-height: 24px;
                color: var(--secondary-off-black);
                @apply font-sofiaSans uppercase mb-0 font-medium;
            }
        }
    }

    [class*='Accordion_active'] {
        div[class*='accordion-body'] {
            margin-bottom: 0px;
        }
    }

    [class*='FilterList_item-sub-cat'] {
        @apply m-0;

        ul {
            gap: 8px;
            @apply flex flex-col;

            li {
                @apply pb-0;
                div {
                    @apply pb-0;
                    button {
                        @apply m-0 p-0;
                        & > input[type='checkbox'] {
                            &:checked + label::before {
                                @apply bg-white #{!important};
                            }

                            &:checked + label:after {
                                transform: rotate(45deg);
                                width: 5px;
                                border-color: black !important;
                            }
                        }

                        label {
                            font-size: 16px;
                            line-height: 20px;
                            color: var(--secondary-off-black) !important;
                            padding: 0;
                            margin-left: 17px;
                            @apply font-light font-sofiaSans;
                            &::before {
                                top: 2px;
                                width: 12px;
                                height: 12px;
                                border: 1px solid #959595;
                            }
                            &::after {
                                left: 4px;
                                height: 7px;
                            }
                        }
                    }
                }
            }
        }
    }
}

.ExtraInfoButton {
    margin-left: 19px;
    @apply flex items-center justify-center cursor-pointer w-fit;
}
