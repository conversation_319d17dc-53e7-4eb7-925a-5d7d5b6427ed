import { BannerType } from '@components/common/HeroBanner/HeroBanner'
import type {
    CartItemPrices,
    PriceRange,
    ProductInterface
} from '@pylot-data/pylotschema'
import {
    cloudinaryDimensionsModelType,
    CTAType,
    EmbedVideoType,
    ImageLinkType,
    ImageType,
    URLType,
    VideoType
} from '../../framework/pylot/hooks/contentful/use-content-json'
import { BannerWithTwoImagesResponse } from './BannerWithTwoImages/BannerWithTwoImages'
import { CarouselHowType } from './CarouselHow/CarouselHow'
import { ContentCardTileInterface } from './ContentCardTiles/ContentCardTile'
import { CorsairOneBlockType } from './CorsairOneBlock/CorsairOneBlock'
import { HorizontalLightingGalleryData } from './HorizontalLightingGallery/HorizontalLightingGallery'
import { HydroXBlockResponse } from './HydroXBlock/HydroXBlock'
import { GridImage } from './ImageGrid/ImageGrid'
import { ImageTwoTileResponse } from './ImageTwoTile/ImageTwoTile'
import { IOverlayContent } from './OverlayProductBlock/BlockFullScreen'
import { ParallaxGalleryResponse } from './ParallaxGallery/ParallaxGallery'
import { ProductAwardsInterface } from './ProductAwards/ProductAwards'
import { Feature } from './ProductFeatures/ProductFeatures'
import { ProfileProps } from './ProfileGrid/types'
import { RichCarouselType } from './RichCarousel/RichCarousel'
import { SmartHomeProduct } from './SmartHome/SmartHome.interfaces'
import { VerticalLightingGalleryData } from './VerticalLightingGallery/VerticalLightingGallery'
import { VidGalleryResponse } from './VidGallery/VidGallery'

import { CusChartsGraphs } from './ChartsAndGraphs/types'
import {
    HorizontalGraphSingleBarsType,
    VerticalGraphDoubleBarsType,
    VerticalGraphSingleBarsType
} from './Graphs/types'
import { LeftRightTextResponse } from './LeftRightText/LeftRightText'
import { ISmal2TileWToggle } from './Smal2TileWToggle/types'
import { SmartCaseType } from './SmartCases/SmartCases.interfaces'
import { TwotilewithCalloutResponse } from './TwotilewithCallout/TwotilewithCallout'

import type { ETabTypes } from '@components/common/Tabs'
import { BannerTextAlignments } from './Banner23Tile/Banner2Tile/Banner2Tile'
import { Banner23TileProps } from './Banner23Tile/types'
import { BannerIntereactiveCalloutResponse } from './BannerIntereactiveCallout/BannerIntereactiveCallout'
import { BelowTheLine } from './BelowTheLine/BelowTheLineTypes'
import { BlockTwoTileResponse } from './BlockTwoTile/BlockTwoTileTypes'
import { BuiltMultiMount } from './BuiltInMultiMount/types'
import { BannerItemType, CarouselType, CtaButton } from './Carousel/Carousel'
import { CarouselImageTextType } from './CarouselImageText/types'
import { CslContainerTiles } from './CarouselProductTiles/CarouselProductTiles'
import { CategoryInterface } from './CategorySlider/Category'
import { CloudinaryImage } from './CloudinaryMedia/Cloudinary'
import { CompatibilityItem } from './CompatibilityBlock/CompatibilityBlock'
import { CompatibilityCheckInterface } from './CompatibilityCheck/CompatibilityCheckTypes'
import { CustomersAlsoBoughtContent } from './CustomersAlsoBought/CustomersAlsoBought'
import { FullPanelCardsType } from './FullPanelCards/FullPanelCards'
import { IMGGridSystemProps } from './IMGGridSystem/types'
import { ListsTableInterface } from './ListsTable/ListsTable'
import { LogoType } from './LogoSlider/LogoSlider'
import { MultiColumnResponse } from './MultiColumn/MultiColumnTypes'
import { OverlayProductContentCardsResponse } from './ProductOverviewTab/OverlayProductContentCards/OverlayProductContentCards'
import { StandardBannerResponse } from './StandardBanner/StandardBanner'
import { TabbedOverlayResponse } from './TabbedOverlay/TabbedOverlay'
import { Content } from './TableComparison/TableComparison.interfaces'
import { TextCardsResponse } from './TextCards/TextCardsTypes'
import { TileVideoResponse } from './TileVideo/TileVideoTypes'
import { TwoTileWithTabsResponse } from './TwoTileWithTabs/TwoTileWithTabsTypes'
import { VerticalTabsSectionResponse } from './VerticalTabsSection/VerticalTabsSection'

export interface Meta {
    contentType: string
}
export interface FAQItemType {
    question: string
    answer: string
    title: string
    meta: Meta
}

export interface RibbonData {
    active: boolean
    desktop: boolean
    mobile: boolean
    textsAndLinks: URLType[]
    cloudinaryBackground: CloudinaryImage[]
    cloudinaryBackgroundMobile: CloudinaryImage[]
    pagesToShow?: string[]
}

export interface ImageInterface {
    cloudinaryImage: CloudinaryImage[]
}

export interface RibbonsData {
    ribbons: RibbonData[]
}

export interface FAQModuleType {
    title: string
    identifier: string
    heading: string
    defaultNum: number
    meta: Meta
    showMore: string
    showLess: string
    faqItem: FAQItemType[]
    useFaqSchema: boolean
}
export interface FAQModuleProps {
    faqModule: FAQModuleType
}

export interface FAQModuleCollectionType {
    faqModule: FAQModuleType
    title: string
    identifier: string
    heading: string
    defaultNum: number
    showMore: string
    showLess: string
    meta: Meta
    useFaqSchema: boolean
    faqItem: FAQItemType[]
    headingType: string
}
export interface FAQModuleCollectionProps {
    items: FAQModuleCollectionType[]
}

export interface IHeroBannerType extends BannerType {
    meta: IMeta<'heroBanner'>
}
export interface IVidGalleryType extends VidGalleryResponse {
    meta: IMeta<'vidGallery'>
}

export interface IHydroXBlockResponse extends HydroXBlockResponse {
    meta: IMeta<'hydroXContentBlock'>
}

export interface IParallaxGalleryProps extends ParallaxGalleryResponse {
    meta: IMeta<'parallaxGallery'>
}

export interface IBlockTwoTileResponse extends BlockTwoTileResponse {
    meta: IMeta<'blockTwoTile'>
}

export interface IImageTwoTile extends ImageTwoTileResponse {
    meta: IMeta<'imageTwoTile'>
}
export interface ISmartCases extends SmartCaseType {
    meta: IMeta<'smartCases'>
}
export interface ITwotilewithCallout extends TwotilewithCalloutResponse {
    meta: IMeta<'twoTileWithCallout'>
}
export interface IBannerIntereactiveCalloutResponse
    extends BannerIntereactiveCalloutResponse {
    meta: IMeta<'bannerWithInteractiveCallouts'>
}
export interface ITwotilewithCallout extends TwotilewithCalloutResponse {
    meta: IMeta<'twoTileWithCallout'>
}
export interface IVerticalTabsSection extends VerticalTabsSectionResponse {
    meta: IMeta<'verticalTabsSection'>
}
export interface IVerticalGraphSingleBarsType
    extends VerticalGraphSingleBarsType {
    meta: IMeta<'verticalGraphSingleBars'>
}

export interface IHorizontalGraphSingleBarsType
    extends HorizontalGraphSingleBarsType {
    meta: IMeta<'horizontalGraphSingleBars'>
}

export interface IVerticalGraphDoubleBarsType
    extends VerticalGraphDoubleBarsType {
    meta: IMeta<'verticalGraphDoubleBars'>
}

export interface ICusChartsGraphs extends CusChartsGraphs {
    meta: IMeta<'cusChartsGraphs'>
}

export interface IStandardBannerProps extends StandardBannerResponse {
    meta: IMeta<'itStandardBanner'>
}

export interface ITabbedOverlayProps extends TabbedOverlayResponse {
    meta: IMeta<'imageVideoTabbedOverlay'>
}

export interface ILeftRightTextProps extends LeftRightTextResponse {
    meta: IMeta<'leftRightText'>
}

export interface IBannerWithTwoImagesProps extends BannerWithTwoImagesResponse {
    meta: IMeta<'bannerWith2Images'>
}

export interface IIMGGridSystemProps extends IMGGridSystemProps {
    meta: IMeta<'imgGridSystem'>
}

export interface IBanner23TileProps extends Banner23TileProps {
    meta: IMeta<'it23Banners'>
}

export interface ITileVideoProps extends TileVideoResponse {
    meta: IMeta<'itTileVideo'>
}

export interface ITextCardsProps extends TextCardsResponse {
    meta: IMeta<'itTextCards'>
}

export interface IMultiColumnProps extends MultiColumnResponse {
    meta: IMeta<'itMultiColumn'>
}

export interface ITwoTileWithTabsProps extends TwoTileWithTabsResponse {
    meta: IMeta<'twoTileWithTabs'>
}

export interface IMeta<T = string> {
    contentType: T
}

export interface ITabItem<T, C = unknown> {
    title?: string
    tabName: string
    url: string
    meta: IMeta<T>
    type: `${ETabTypes}`
    content?: C[]
    visible?: boolean
}

export interface ICalloutBlockItem {
    heading: string
    meta: IMeta<'calloutBlockItem'>
    subheading: string
    title: string
}

export interface IHTMLBlock {
    title: string
    meta: IMeta<'htmlMarkup'>
    markup?: string
    longDescription?: string
    customClassName?: string
    linkedScript?: JsCssScript[]
    undoMigrationStyling?: boolean
}
export interface IHTMLContentPage {
    title: string
    meta: IMeta<'htmlContentPage'>
    markup: string
    linkedScript?: JsCssScript[]
}

export interface IHtmlMarkup {
    title: string
    meta: IMeta<'htmlMarkupPage'>
    markup: string
    linkedScript?: JsCssScript[]
}

export type JsCssScript = {
    meta: IMeta<'linkedJsCss'>
    templateName: string
    linkedJs?: string[]
    linkedCss?: string[]
}
export interface ICalloutBlock {
    title: string
    heading: string
    image: ImageType
    calloutBlockItems: ICalloutBlockItem[]
    backgroundColor: string
    meta: IMeta<'calloutBlock'>
    markup: any
    url: string
    shopifyUrl?: string
    script: string
    subheading?: string
    headingsPosition?: boolean
    headingType: string
    theme: string
    cloudinaryImage: CloudinaryImage[]
}
export interface IProductSpecsCard extends CarouselType {
    productTitle: string
    price: string
    productDescription: string
    table: string
    cta: CtaButton[]
}
export interface ITwoColumnProductSpecs {
    heading?: string
    subheading?: string
    headingType?: boolean
    columnItems: IProductSpecsCard[]
    cta: CtaButton[]
}
export interface IProductContentfulResponse<T, D = string> {
    name: string
    sku: string
    headline: string
    shortDescription: string
    isEnabled: boolean
    isVisible: boolean
    identifier: string
    useFaqSchema: boolean
    tabs: ITabItem<T>[]
    meta: IMeta<D>
    promoMessage: {
        content: string
    }
    linkedProducts?: IProductContentfulResponse<T, D>[]
    calloutBlock: ICalloutBlock
    carouselContent: ICarouselContent
    embedVideo: EmbedVideoType
    cloudinaryDimensionsModel: cloudinaryDimensionsModelType
    howToLink: string
    features: Feature[]
    awards: ProductAwardsInterface
    copyBlock: ICopyBlock
    compatibilityCheck: CompatibilityCheckInterface
    verticalLightingGallery: VerticalLightingGalleryData
    horizontalLightingGallery: HorizontalLightingGalleryData
    imageTwoTileCallout: ImageTwoTileResponse
    nonInteractiveProductBlock: IProductBlocks
    packageContents?: IProductContentfulResponse<T, D>
    socialDefaultMedia: ImageType
    items: FAQModuleCollectionType[]
    carouselHow: CarouselHowType[]
    twoTileCallout: TwotilewithCalloutResponse
    bannerIntereactiveCallouts: BannerIntereactiveCalloutResponse
    verticalGraphSingleBars: VerticalGraphSingleBarsType
    horizontalGraphSingleBars: HorizontalGraphSingleBarsType
    verticalGraphDoubleBars: VerticalGraphDoubleBarsType
    contentModules: ContentModulesProps
    productFamily: ProductFamilyResponse
    customersAlsoBought: CustomersAlsoBoughtContent
    ribbons?: RibbonData[]
    banner: ImageWithDescriptionResponse
    onlineToDate?: string
    socialDefaultImage?: ImageType
    enableCanonical?: boolean
    canonicalOverride?: string
    metaRobotsOverrideFollow?: boolean
    metaRobotsOverride?: boolean
    featuredBundlesTitle?: string
    fallbackUrl?: string
    crossellingTitle?: string
    bundleAndSaveTitle?: string
    promotionMessage?: string
    compatibility?: CompatibilityItem[]
    buildYourOwnLink?: string
    longDescription?: string
}

export interface IGenericProductBlock<T = string> {
    heading?: string
    text?: string
    copy?: string
    imageWidth?: string
    imageHeight?: string
    productImage: ImageType
    mobileImage?: ImageType
    meta: IMeta<T>
    headingType: string
    cloudinaryDesktopImage?: CloudinaryImage[]
    cloudinaryMobileImage?: CloudinaryImage[]
}
export interface productLink {
    backendStatus?: string
    identifier?: string
    isEnabled?: boolean
    isVisible?: boolean
    jasperPimid?: number
    longDescription?: string
    meta?: {
        contentType?: string
    }
    metaDescription?: string
    metaTitle?: string
    name: string
    notSellable?: boolean
    onlineDate?: string
    onlineToDate?: string
    packageContents?: string
    sku: string
    url?: string
}
export interface IProductBlock {
    title: string
    heading: string
    image: ImageLinkType
    cta: CTAType
    meta: IMeta<'productBlock'>
    text: string
    sku: string
    link: URLType
    productLink: productLink
    available_regions: Array<{
        identifier: string
        name: string
        meta: IMeta<string>
    }>
}
export interface BackgroundType {
    backgroundImage?: {
        description: string
        file: {
            contentType: string
            details: {
                image: {
                    width: number
                    height: number
                }
            }
            filename: string
            url: string
        }
    }
    meta?: any
    title?: string
    opacity?: number
    colorHex?: string
    gradient?: string
    gradientColor?: boolean
    cloudinaryBackgroundImage?: CloudinaryImage[]
}

export interface ILearnMoreBlock {
    title: string
    products: IProductBlock[]
    backgroundImage: ImageLinkType
    meta: IMeta<'addToCartLearnMoreBlock'>
    heading: string
    subheading: string
    bodyCopy: string
    headingsPosition?: boolean
    headingType: string
    backgroundColor: BackgroundType
}

export type ImageComponent = {
    description: string
    heading: string
    headingColor: string
    image: ImageType
    newTab: boolean
    title: string
    url: string
    videoUrl: string
    cloudinaryImage?: CloudinaryImage[]
    cloudinaryBackgroundImage?: CloudinaryImage[]
}

export interface ICarouselContent {
    blockPadding?: inlineStyling
    title: string
    subHeading: string
    copy: string
    subtitle: string
    contents: ImageComponent[]
    backgroundColor: string
    subHeadingColor: string
    headingColor: string
    copyColor: string
    headerTitle: string
    meta: IMeta<'productImageContents'>
    markup: any
    url: string
    developmentUrl?: string
    script: string
    headingType: string
}
export interface IVerticalLightingGalleryData
    extends VerticalLightingGalleryData {
    meta: IMeta<'verticalLightingGallery'>
}

export interface ICopyBlock {
    title: string
    heading: string
    text: string
    meta: IMeta<'copyBlock'>
    backgroundColorHex?: string
    backgroundImage?: ImageType
    fontColorHex?: string
    image?: ImageInterface
    cta?: {
        displayText: string
        textColor?: string
        url: string
        openInANewTab?: boolean
        ctaImage?: CloudinaryImage[]
    }
}

export interface ICopyBlocksWrapper {
    contentCards: ICopyBlock[]
    title: string
    meta: IMeta<'copyBlocksWrapper'>
}
export interface IHorizontalLightingGalleryData
    extends HorizontalLightingGalleryData {
    meta: IMeta<'horizontalLightingGallery'>
}

export interface ISmartHome {
    title: string
    video: VideoType
    backgroundImage: ImageType
    headerTitle: string
    subtitle: string
    headerIcon: ImageType
    ctaLabel: string
    ctaUrl: string
    products: SmartHomeProduct[]
    fullWidth: boolean
    meta: IMeta<'smartHome'>
    videoDescription?: string
}

export interface ICarouselHowType extends CarouselHowType {
    meta: IMeta<'carouselHow'>
}
export interface IRichCarouselType extends RichCarouselType {
    meta: IMeta<'richCarousel'>
}

export interface ICorsairOneBlockType extends CorsairOneBlockType {
    meta: IMeta<'corsairOneBlock'>
}

// extend OverviewItem type with more interfaces ICalloutBlock | A | B
export type OverviewItem =
    | ICalloutBlock
    | ICarouselContent
    | ILearnMoreBlock
    | IHeroBannerType
    | IVerticalLightingGalleryData
    | IHorizontalLightingGalleryData
    | IProductBlocks
    | IInteractiveCardsSection
    | ISmartHome
    | IProductCallouts
    | IVidGalleryType
    | ITestimonialCarousel
    | IOverlayContent
    | IStandardCardsSection
    | ICarouselHowType
    | IINTProducts
    | IHydroXBlockResponse
    | ImageGridSystem
    | IParallaxGalleryProps
    | ImageGallerySystem
    | ICorsairOneBlockType
    | IBlockTwoTileResponse
    | ProfileProps
    | IImageTwoTile
    | ISmal2TileWToggle
    | ISmartCases
    | ITwotilewithCallout
    | IVerticalGraphSingleBarsType
    | IHorizontalGraphSingleBarsType
    | IVerticalGraphDoubleBarsType
    | ICusChartsGraphs
    | BuiltMultiMount
    | IStandardBannerProps
    | IBannerIntereactiveCalloutResponse
    | ITabbedOverlayProps
    | ISmartCases
    | CategorySliderProps
    | ILeftRightTextProps
    | BelowTheLine
    | IBannerWithTwoImagesProps
    | IVerticalTabsSection
    | IHTMLBlock
    | IIMGGridSystemProps
    | CarouselType
    | FullPanelCardsType
    | ImageWithDescriptionResponse
    | IBanner23TileProps
    | ListsTableInterface
    | Content
    | IPageSection
    | CarouselImageTextType
    | CslContainerTiles
    | OverlayProductContentCardsResponse
    | IHTMLContentPage
    | ITileVideoProps
    | ITextCardsProps
    | IMultiColumnProps
    | ITwoTileWithTabsProps

export type ContentModulesProps = [
    {
        title: string
        identifier: string
        bannerItems: BannerItemType[]
        textAlignment?: BannerTextAlignments
        isSpaceBetweenTiles?: boolean
        meta: IMeta<'banner2Tile'>
    },
    {
        autoPlay?: boolean
        bannerItems: BannerItemType[]
        duration?: number
        randomOrder: boolean
        meta: IMeta<'banners'>
    },
    {
        title?: string
        backgroundImage?: ImageType
        identifier: string
        heading?: string
        collections?: CategoryInterface[]
        meta: IMeta<'categorySlider'>
    },
    {
        title: string
        identifier: string
        bannerItems: BannerItemType[]
        meta: IMeta<'banneriCue'>
    },
    {
        title: string
        video: VideoType
        backgroundImage: ImageType
        headerTitle: string
        subtitle: string
        headerIcon: ImageType
        ctaLabel: string
        ctaUrl: string
        products: SmartHomeProduct[]
        fullWidth: boolean
        meta: IMeta<'smartHome'>
    },
    {
        title: string
        identifier: string
        bannerItems: BannerItemType[]
        layout?: string
        meta: IMeta<'banner3Tile'>
    }
]

export type CarouselProps = {
    autoPlay?: boolean
    bannerItems: BannerItemType[]
    duration?: number
    randomOrder: boolean
    meta: IMeta<'banners'>
}

export type CategorySliderProps = {
    title?: string
    backgroundImage?: ImageType
    identifier: string
    heading?: string
    collections?: CategoryInterface[]
    meta: IMeta<'categorySlider'>
}

export interface IOverviewItems {
    content: Array<OverviewItem>
}

export interface IProductBlock {
    heading: string
    text: string
    title: string
    productImage: {
        title: string
        description: string
        file: {
            contentType: string
            details: {
                image: {
                    width: number
                    height: number
                }
                size: number
            }
            fileName: string
            url: string
        }
        meta: Record<string, unknown>
    }
    meta: IMeta<'productBlock'>
}

export interface IProductBlocks {
    title: string
    cloudinaryBackgroundImage: CloudinaryImage[]
    cloudinaryMainImage: CloudinaryImage[]
    link: URLType
    heading: string
    secondaryHeading: string
    meta: IMeta<'nonInteractiveProductBlock'>
    block: IProductBlock[]
    secondaryBlock: IProductBlock[]
    headingType: string
}

export interface IInteractiveCard {
    title: string
    backgroundImage: ImageLinkType
    thumbnailImage: ImageType
    subheading: string
    subheadingPlacement: 'bottom' | 'top'
    textPlacement: string
    cta: CTAType
    heading: string
    text: string
    textWidth?: string
    productCards: IProductBlock[]
    meta: IMeta<'interactiveCard'>
    displayAsCta: 'cta' | 'icons'
    socialIcons: ImageLinkType[]
}
export interface IInteractiveCardsSection {
    title: string
    interactiveCardItems: IInteractiveCard[]
    backgroundColor: string
    heading: string
    subheading: string
    headingsPosition: boolean
    text: string
    meta: IMeta<'interactiveCardsSection'>
    headingType: string
}

export interface IProductCalloutItem {
    title: string
    heading: string
    text: string
    desktopItemSide: string
    meta: IMeta<'productCalloutItem'>
}

export interface IProductCallouts {
    title: string
    heading: string
    theme: string
    cloudinaryImage: CloudinaryImage[]
    imageWidth?: string
    imageHeight?: string
    meta: IMeta<'productCallout'>
    calloutItems: IProductCalloutItem[]
    subheading: string
    headingsPosition: boolean
    headingType: string
}
export interface ITestimonialBlock {
    title: string
    desktopMainImagePosition: string
    headingTitle: string
    mobileMainImagePosition: string
    paginationTitle: string
    text: string
    desktopTextPosition: string
    meta: IMeta<'testimonialBlock'>
    mainImage: ImageType
    logo?: ImageType
    logoHeading?: string
    cloudinaryLogoImage?: CloudinaryImage[]
    cloudinaryMainImage?: CloudinaryImage[]
}

export interface ITestimonialCarousel {
    title: string
    backgroundColorHex: string
    textColor: string
    heading: string
    meta: IMeta<'testimonialCarousel'>
    testimonialBlocks: ITestimonialBlock[]
    pageBackgroundImage?: ImageType
    headingType: string
}

export interface IDarkLightTheme {
    meta: IMeta<'darkAndLightTheme'>
    title: string
    theme: 'dark' | 'light'
    backgroundDark: string
    backgroundLight: string
    colorLight: string
    colorDark: string
}

export interface IStandardCard {
    theme?: IDarkLightTheme
    meta: IMeta<'standardContentCard'>
    image?: ImageLinkType
    text: string
    heading: string
    title: string
    backgroundColorHex?: string
    fontColorHex?: string
    useBorderBottom?: boolean
    cta: {
        displayText: string
        textColor?: string
        url: string
        openInANewTab?: boolean
    }
}

export interface IStandardCardsSection {
    title: string
    heading: string
    subheading: string
    standardCards: IStandardCard[]
    theme?: IDarkLightTheme | undefined
    text?: string
    meta: IMeta<'standardCardsSection'>
    pageBackgroundImage?: ImageType
    headingType: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
}

export interface MenuLink {
    color: string
    meta: { contentType: string }
    target: string
    text: string
    url: string
    openModal: boolean
}

export interface Text {
    text: string
    meta: { contentType: string }
    description: string
}
export interface IBlogArticle {
    articleName: string
    author: string
    featuredImage: ImageLinkType
    linkToArticle: string
    meta: IMeta<'blogPostEntry'>
    publishDate: string
    title: string
}

export interface IBlogTab {
    title: string
    tabName: string
    content: IBlogArticle[]
    meta: IMeta<'blogArticlesPdpTab'>
}

export interface IINTProductItem {
    title: string
    heading: string
    productImage: ImageLinkType
    meta: IMeta<'intProducts'>
    cta: CTAType
    theme: IDarkLightTheme
}

export interface IINTProducts {
    heading: string
    title: string
    text?: string
    mainImage: ImageLinkType
    cta: CTAType
    theme: IDarkLightTheme
    intProductList: IINTProductItem[]
    meta: IMeta<'intProducts'>
    headingType: string
}
export interface IContentCardTiles {
    backgroundImage?: ImageType
    copy?: string
    heading?: string
    meta: {
        contentType: string
    }
    subHeading?: string
    tile?: ContentCardTileInterface[]
    title?: string
}

export interface ILogoSlider {
    backgroundColor?: string
    backgroundImage?: ImageType
    heading?: string
    logos?: LogoType[]
    meta: {
        contentType: string
    }
    title: string
    headingType: string
}

export interface ImageGridSystem {
    identifier: string
    primaryGrid: GridImage
    secondaryGrid: GridImage[]
    title: string
    meta: IMeta<'imageGrid'>
    backgroundSilder?: ImageType[]
}

export interface ImageGallerySystem {
    title: string
    ratio: boolean
    cloudinaryImages: CloudinaryImage[]
    meta: IMeta<'img4Up'>
    pageBackgroundImage?: ImageType
    heading: string
    subheading: string
    headingsPosition: boolean
    headingType: string
    description: string
}

export interface IAccessoriesParts {
    title: string
    tabName: string
    description: string
    urlSlug: string
    url: string
    meta: IMeta<'accessoriesParts'>
}
export interface ProductFamilyBlockType {
    meta: Meta
    cloudinaryPrimaryImage: CloudinaryImage[]
    productSlug: string
    showPrice: boolean
    sku: string
    title: string
    description: string
    variants: ProductFamilyBlockVariantType[]
}

export interface ProductFamilyResponse {
    title: string
    identifier: string
    heading: string
    backgroundColor: string
    loopCarousel: boolean
    meta: Meta
    productBlocks: ProductFamilyBlockType[]
    subheading: string
    headingsPosition: boolean
    headingType: string
}

export interface ProductFamilyBlockVariantType {
    name: string
    sku: string
    meta: {
        contentType: string
    }
    cloudinaryColorSwatch: CloudinaryImage[]
    cloudinaryVariantImage: CloudinaryImage[]
}

export interface IBackgroundColor {
    colorHex: string
    gradientColor: boolean
    meta: IMeta<'backgroundColor'>
    opacity: number
    title: string
    gradient: string
    backgroundImage?: ImageType
    cloudinaryBackgroundImage?: CloudinaryImage[]
}

export interface IPageSection {
    backgroundColor: IBackgroundColor
    cloudinaryBackgroundImage: CloudinaryImage[]
    meta: IMeta<'pageSection'>
    title: string
    contentModules: Array<{
        identifier: string
        [k: string]: any
        meta: IMeta<string>
    }>
}
export interface ProductType {
    heading: string
    description: string
    image: ImageType
    price_range: CartItemPrices | PriceRange
    product: ProductInterface
    __typename: string
}

export type PimProductOption = {
    option_id: number
    option_name: string
    value: string
    sort_order: number
}

export type PimProductOptionValue = {
    option_id: number
    option_name: string
    value: {
        id: number
        value: string
    }
}

export type ProductRelation = {
    id: number
    name: string
    values: {
        id: number
        name: string
        sku: string
        web_id: string
        option_values: PimProductOption[]
    }[]
}
export type inlineStyling = {
    paddingBottomDesktop: number
    paddingBottomMobile: number
    paddingTopDesktop: number
    paddingTopMobile: number
    paddingLeftDesktop: number
    paddingLeftMobile: number
    paddingRightDesktop: number
    paddingRightMobile: number
}
export type blockSizing = {
    desktopWidth: number
    desktopHeight: number
    mobileWidth: number
    mobileHeight: number
}

export type PimParentOption = {
    id: number
    name: string
    values: {
        id: number
        option_id: number
        value: string
    }[]
}

export type ImageWithDescriptionResponse = {
    heading: string
    subheading: string
    headingText: string
    ctaButton: string
    backgroundColor: string
    meta: IMeta<'itImageWithDescription'>
    title1stImage: string
    disclaimerCopy: string
    titleFontColor: string
    title2ndImage: string
    titleFontColor2ndImage: string
    lineBelowColor: string
    lineBelowColor2ndImage: string
    copy2ndImage: string
    copy: string
    cloudinaryDesktopMedia: CloudinaryImage[]
    cloudinaryMobileMedia: CloudinaryImage[]
    disclaimerCopy2ndImage: string
    headingType: string
}

export type ImageWithDescriptionProps = {
    content: ImageWithDescriptionResponse | null
}

export interface verticalPaddings {
    desktopPaddingTop?: string
    desktopPaddingBottom?: string
    mobilePaddingTop?: string
    mobilePaddingBottom?: string
}

export interface PreviewData {
    time: number
}

export interface IThirdPartyPromoMessage {
    campaignName: string
    title: string
    description: string
    image: ImageType
    message: string
    urlViewMore: string
    enable3rdPartyPromoPopup: boolean
}
