import { Accordion } from '@components/common/Accordion'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import {
    ChangeEvent,
    FC,
    useCallback,
    useEffect,
    useRef,
    useState
} from 'react'
import s from './MultiRangeSlider.module.scss'
import { MultiRangeSliderInputProps } from './MultiRangeSlider.types'

export const MultiRangeSlider: FC<MultiRangeSliderInputProps> = ({
    min,
    max,
    title,
    currencyCode,
    onChangeValue,
    value
}) => {
    const { locale } = useRouter()
    const [minVal, setMinVal] = useState(min)
    const [maxVal, setMaxVal] = useState(max)
    const [isOpen, setIsOpen] = useState(true)
    const minValRef = useRef<HTMLInputElement>(null)
    const maxValRef = useRef<HTMLInputElement>(null)
    const range = useRef<HTMLDivElement>(null)
    const { t } = useTranslation()

    const getPercent = useCallback(
        (value): number => {
            return Math.round(((value - min) / (max - min)) * 100)
        },
        [min, max]
    )

    const onChangeAccordion = (_ev: unknown, isExpand: boolean) => {
        setIsOpen(isExpand)
    }

    const handleUpdateLatestSlideValue = (crrMin: number, crrMax: number) => {
        onChangeValue?.(crrMin, crrMax)
    }

    const getLocalePrice = (price: number) => {
        if (!currencyCode || !locale) {
            return `$ ${price}`
        }

        const formatCurrency = new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currencyCode ? (currencyCode as string) : 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        })

        return formatCurrency.format(price)?.replaceAll('’', ',')
    }

    const onChangeMinVal = (event: ChangeEvent<HTMLInputElement>) => {
        const value = Math.min(+event.target.value, maxVal - 1)
        setMinVal(value)
        event.target.value = value.toString()
    }

    const onChangeMaxVal = (event: ChangeEvent<HTMLInputElement>) => {
        const value = Math.max(+event.target.value, minVal + 1)
        setMaxVal(value)
        event.target.value = value.toString()
    }

    const onUpdateSliderValueWhenRelease = () => {
        handleUpdateLatestSlideValue(minVal, maxVal)
    }

    useEffect(() => {
        if (maxValRef.current) {
            const minPercent: number = getPercent(minVal)
            const maxPercent: number = getPercent(+maxValRef.current.value)

            if (range.current) {
                range.current.style.left = `${minPercent}%`
                range.current.style.width = `${maxPercent - minPercent}%`
            }
        }
    }, [minVal, getPercent])

    useEffect(() => {
        if (minValRef.current) {
            const minPercent = getPercent(+minValRef?.current?.value)
            const maxPercent = getPercent(maxVal)

            if (range.current) {
                range.current.style.width = `${maxPercent - minPercent}%`
            }
        }
    }, [maxVal, getPercent])

    useEffect(() => {
        if (value) {
            setMinVal(value.min)
            setMaxVal(value.max)
        }
    }, [value])

    return (
        <div className={s.MultiRangeWrapper}>
            <Accordion
                className={s.AccordionWrapper}
                isOpen={isOpen}
                title={title}
                onChange={onChangeAccordion}
            >
                <div className={s.MainSliderWrapper}>
                    <span>{t('Price range:')}</span>
                    <div className={s.PriceDescription}>
                        <span>{getLocalePrice(minVal)}</span>
                        <span>-</span>
                        <span>{getLocalePrice(maxVal)}</span>
                    </div>
                    <div className={s.RangeWrapper}>
                        <label className="sr-only" htmlFor="slider-price-min">
                            {t('Slider Price - Min Value')}
                        </label>
                        <input
                            id="slider-price-min"
                            type="range"
                            min={min}
                            max={max}
                            value={minVal}
                            ref={minValRef}
                            onChange={onChangeMinVal}
                            className={cn(s.thumb, 'z-3', {
                                'z-5': minVal > max - 100
                            })}
                            onMouseUp={onUpdateSliderValueWhenRelease}
                        />
                        <label className="sr-only" htmlFor="slider-price-max">
                            {t('Slider Price - Max Value')}
                        </label>
                        <input
                            id="slider-price-max"
                            type="range"
                            min={min}
                            max={max}
                            value={maxVal}
                            ref={maxValRef}
                            onChange={onChangeMaxVal}
                            className={cn(s.thumb, s.thumbRight, 'z-4')}
                            onMouseUp={onUpdateSliderValueWhenRelease}
                        />
                        <div className={s.slider}>
                            <div className={s['slider-track']} />
                            <div ref={range} className={s['slider-range']} />
                        </div>
                    </div>
                </div>
            </Accordion>
        </div>
    )
}
