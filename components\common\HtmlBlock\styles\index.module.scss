/**
 * HTML Block Index
 * Note: the order of these import is essential
 */
/**
* Libs
*/
// @import './libs/magnific-popup.module';
#HtmlBlock {
    :global {
        h1,
        .h1,
        h2,
        .h2,
        h3,
        .h3,
        h4,
        .h4,
        p,
        a,
        span:not(.lowercase),
        div {
            font-size: 12px;
        }

        .pdpWrapper {
            /**
         * Globals
         */
            @import './globals/global.module';
            @import './globals/container.module';
            @import './globals/backgrounds.module';
            @import './globals/tables.module';
            @import './globals/utilities.module';
            @import './globals/content.module';
            @import './globals/typography.module';
            @import './globals/keyframes.module';
            @import './globals/buttons.module';
            @import './globals/icons.module';
            @import './globals/variant.module';
            @import './globals/label.module';
            @import './globals/price.module'; /**
             * Components
             */
            @import './components/item.module';
            @import './components/cart.module';
            @import './components/cta.module';
            @import './components/slick-slider.module';
            @import './components/full-pic.module';
            @import './components/white-popup.module';
            @import './components/videos.module';
            @import './components/gallery.module';
            @import './components/indabox.module';
            @import './components/bundle.module';
            @import './components/promotion.module';
            @import './components/tab.module';
            @import './components/reviews.module';
            @import './components/notification.module';
            @import './components/blades.module';
            @import './components/interest-points.module';
            @import './components/html-selectors.module'; /**
             * Templates
             */
            @import './templates/panel.module';
            @import './templates/panel-award.module';
            @import './templates/panel-1.module';
            @import './templates/panel-2.module';
            @import './templates/panel-3.module';
            @import './templates/panel-4.module';
            @import './templates/panel-5.module';
            @import './templates/panel-6.module';
            @import './templates/panel-hero.module';
            @import './templates/chosen.module';
            @import './templates/tab-awards.module';
            @import './templates/mft.module';
            @import './templates/testimonial.module';
            @import './templates/product-details.module';
            @import './templates/tab-overview.module';
            @import './templates/care.module';
            @import './templates/btr.module';
            @import './templates/warrenty.module';
            @import './templates/find-retailer.module';
            @import './templates/versatile.module';
            @import './templates/lighting.module';
            @import './templates/dominator-platinum-rgb-ddr5.module';
            @import './templates/locked-interest-points.module';
            @import './templates/main-content.module';
            @import './templates/cmsitem_00026001.module';
            @import './templates/page_00002P80.module';
            @import './templates/page_00002P83.module';
            @import './templates/page_00002P84.module';
            @import './templates/page_00002UMH.module';
            @import './templates/page_00002VED.module';
            @import './templates/page_00002TUO.module';
            @import './templates/page_00002RJC.module';
            @import './templates/page_00002RJE.module';
            @import './templates/_page_00002LD7.module';
            @import './templates/cmsitem_00070001.module';
            @import './templates/cmsitem_00071001.module';
            @import './templates/page_00002RJH';
            @import './templates/page_00002LDA.module';
            @import './templates/_dominator-platinum-se-contrast-pdp.module';
            @import './templates/_page_000025XP.module';
            @import './templates/_page_000025XT.module';
            @import './templates/_page_000024E0.module';
            @import './templates/_page_00002892.module';
            @import './templates/_page_00002GQH.module';
            @import './templates/_page_00002KLD.module';
            @import './templates/_page_00002LDE.module';
            @import './templates/_page_00002NOI.module';
            @import './templates/_page_00002J1U.module';
            @import './templates/_page_00002J1W.module';
            @import './templates/page_00002OGB';
            @import './templates/page_00002P82.module';
            @import './templates/cv-2019';
            @import './templates/_page_00002LD8.module';
            @import './templates/pdp_template_keyboards';
            @import './templates/cmsitem_00103000.module';
            @import './templates/cmsitem_00001000.module';
            @import './templates/product_details.module';
            @import './templates/page_00002893.module';
            @import './templates/page_000025XS.module';
            @import './templates/cmsitem_00030003.module';
            @import './templates/cmsitem_00073001.module';
            @import './templates/cmsitem_00118013.module';
            @import './templates/cmsitem_00000002.module';
            @import './templates/cmsitem_00004004';
            @import './templates/cmsitem_00009000';
            @import './templates/cmsitem_00086002.module';
            @import './templates/tc200_pdp_template.module';
            @import './templates/pdp_template_fans.module';
            @import './templates/pdp_template_mice.module';
            @import './templates/_page_00002LDG.module';
            @import './templates/_page_00002LD5.module';
            @import './templates/cmsitem_00099001.module';
            @import './templates/pdp_template_cases.module';
            @import './templates/cmsitem_00053000_alt.module';

            /**
             * Page
             */
            @import './page/product.module'; /**
             * Virtuoso
             */
            @import './virtuoso/index.module';

            #panel2 .content #pdpWrapper div #smalcode {
                @apply w-full px-4 m-auto;
                @screen md {
                    @apply w-auto mr-0;
                }
            }
        }

        /**
       * Smalcode
       */
        #custom-smalcode {
            &:not(.nexus) {
                @apply relative;
                --vh: 1vh;
                z-index: 1;

                // #custom-smalcode.wingsuit-root .lg\.lg\:smal-w-2\/4
                &.wingsuit-root {
                    // #custom-smalcode.wingsuit-root *
                    * {
                        box-sizing: border-box !important;
                        border: 0 solid #e5e5e5;
                    }

                    // #custom-smalcode.wingsuit-root .lg\.lg\:smal-w-2\/4
                    &.wingsuit-root {
                        // #custom-smalcode.wingsuit-root *
                        * {
                            box-sizing: border-box !important;
                            border: 0 solid #e5e5e5;
                        }

                        // #custom-smalcode.wingsuit-root .lg\.lg\:smal-w-2\/4
                        .lg\:smal-w-2\/4 {
                            width: 48%;
                        }
                    }

                    // #custom-smalcode.wingsuit-root .lg\.lg\:smal-w-2\/4
                    .lg\:smal-w-2\/4 {
                        width: 48%;
                    }
                }

                @import './smalcode/helpers.module';
                @import './smalcode/typography.module';
                @import './smalcode/element.module';
                @import './smalcode/text.module';
                @import './smalcode/cta.module';
                @import './smalcode/panel-hero.module';
                @import './smalcode/panel-comfort.module';
                @import './smalcode/panel-connectivity.module';
                @import './smalcode/panel-features.module';
                @import './smalcode/panel-programable.module';
                @import './smalcode/panel-hero-cateogry.module';
                @import './smalcode/panel-meet-the-family.module';
                @import './smalcode/panel-audio-quality.module';
                @import './smalcode/product-sequence.module';
                @import './smalcode/panel-category-sticky.module';
                @import './smalcode/panel-programmable-black.module'; /**
                 * The CSS below this line is intentional and should remain on the
                 * bottom of this style sheet.
                 */
                // .#custom-smalcode.device-is-touch
                &.device-is-touch {
                    // .#custom-smalcode.device-is-touch .touch-hide
                    .touch-hide {
                        @apply hidden;
                    }

                    /**
             * The CSS below this line is intentional and should remain on the
             * bottom of this style sheet.
             */
                    // .#custom-smalcode.device-is-touch
                    &.device-is-touch {
                        // .#custom-smalcode.device-is-touch .touch-hide
                        .touch-hide {
                            @apply hidden;
                        }

                        // .#custom-smalcode.device-is-touch .mobile-touch-show
                        .mobile-touch-show {
                            @apply block;
                        }
                    }

                    // #custom-smalcode:not(.device-is-touch)
                    &:not(.device-is-touch) {
                        // #custom-smalcode:not(.device-is-touch) .touch-show
                        .touch-show {
                            @apply hidden;
                        }
                    }

                    // #custom-smalcode:not(.device-is-touch)
                    &:not(.device-is-touch) {
                        // #custom-smalcode:not(.device-is-touch) .touch-show
                        .touch-show {
                            @apply hidden;
                        }
                    }

                    // #custom-smalcode.device-is-touch .touch-hide,
                    // #custom-smalcode:not(.device-is-touch) .touch-hide
                    &.device-is-touch .touch-hide,
                    &:not(.device-is-touch) .touch-hide {
                        @media only screen and (max-width: 767px) {
                            @apply hidden;
                        }
                    }

                    // #custom-smalcode.device-is-touch .mobile.touch-show,
                    // #custom-smalcode:not(.device-is-touch) .mobile-touch-show
                    &.device-is-touch .mobile.touch-show,
                    &:not(.device-is-touch) .mobile.touch-show {
                        @media only screen and (max-width: 767px) {
                            @apply block;
                        }
                    }
                }

                &.nexus {
                    * {
                        font-family: Tomorrow, sans-serif;
                    }

                    h1.style-h2,
                    h2.style-h2 {
                        font-size: 95px;
                        line-height: 95px !important;
                        letter-spacing: 4px;
                        margin-bottom: 25px !important;
                        margin-top: 10px !important;
                        font-weight: normal;

                        span.lowercase {
                            font-size: 95px;
                            line-height: 95px !important;
                            letter-spacing: 4px;
                        }
                    }

                    h4.style-h3 {
                        @apply font-verveineRegular;
                        font-family: 'Verveine', sans-serif;
                        letter-spacing: 0.05rem;
                        font-weight: 400;
                    }

                    .body-p1 {
                        p {
                            font-size: 22px;
                            line-height: 36px;
                            font-weight: normal;
                            letter-spacing: 0;
                            color: #ffffff;
                        }
                    }

                    .body-p2.bottom-title {
                        p {
                            color: #ffffff;
                        }

                        // #custom-smalcode.device-is-touch .mobile.touch-show,
                        // #custom-smalcode:not(.device-is-touch) .mobile-touch-show
                        &.device-is-touch .mobile.touch-show,
                        &:not(.device-is-touch) .mobile.touch-show {
                            @media only screen and (max-width: 767px) {
                                @apply block;
                            }
                        }
                    }

                    &.nexus {
                        * {
                            font-family: Tomorrow, sans-serif;
                        }

                        h1.style-h2,
                        h2.style-h2 {
                            font-size: 95px;
                            line-height: 95px !important;
                            letter-spacing: 4px;
                            margin-bottom: 25px !important;
                            margin-top: 10px !important;
                            font-weight: normal;

                            span.lowercase {
                                font-size: 95px;
                                line-height: 95px !important;
                                letter-spacing: 4px;
                            }
                        }

                        h4.style-h3 {
                            @apply font-verveineRegular;
                            font-family: 'Verveine', sans-serif;
                            letter-spacing: 0.05rem;
                            font-weight: 400;
                        }

                        .body-p1 {
                            p {
                                font-size: 22px;
                                line-height: 36px;
                                font-weight: normal;
                                letter-spacing: 0;
                                color: #ffffff;
                            }
                        }

                        .body-p2.bottom-title {
                            p {
                                color: #ffffff;
                            }
                        }
                    }
                }

                #pdp #collapsible {
                    box-sizing: content-box !important;
                    height: 400px;
                    min-height: 0;
                    padding: 7vw 0;

                    .container {
                        box-sizing: border-box !important;
                        height: 100%;
                        position: relative;
                        text-align: center;
                    }
                }
            }
        }
    }
}
