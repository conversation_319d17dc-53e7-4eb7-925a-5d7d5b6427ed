import { FC, HTMLAttributes } from 'react'

interface Props extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
}

const PSCompatible: FC<Props> = ({ width = 31, height = 31, ...props }) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 31 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <rect
            x="0.75"
            y="0.75"
            width="29.5"
            height="29.5"
            rx="1.5"
            fill="black"
            stroke="black"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M27.2987 20.1797C26.8346 20.7712 25.6975 21.1933 25.6975 21.1933L17.2385 24.2629V21.9991L23.4637 19.7582C24.1702 19.5025 24.2787 19.141 23.7044 18.9513C23.1313 18.761 22.0935 18.8155 21.3865 19.0723L17.2385 20.5483V18.1989L17.4776 18.1171C17.4776 18.1171 18.6762 17.6885 20.3617 17.4999C22.0471 17.3123 24.1108 17.5255 25.7309 18.146C27.5567 18.7288 27.7623 19.5881 27.2987 20.1797ZM18.0437 16.3249V10.5357C18.0437 9.85584 17.9196 9.22992 17.2882 9.05272C16.8046 8.89624 16.5046 9.34987 16.5046 10.0292V24.5268L12.6345 23.2858V6C14.28 6.3086 16.6773 7.03811 17.966 7.47702C21.2435 8.61381 22.3547 10.0287 22.3547 13.2166C22.3547 16.3238 20.4561 17.5015 18.0437 16.3249ZM5.53344 21.7625C3.65914 21.2292 3.3472 20.1181 4.20151 19.478C4.99106 18.8869 6.33378 18.442 6.33378 18.442L11.8828 16.4487V18.7212L7.88968 20.165C7.18432 20.4207 7.07584 20.7827 7.64898 20.9724C8.22266 21.1622 9.261 21.1082 9.96744 20.8519L11.8828 20.1497V22.1828C11.7613 22.2046 11.6259 22.2265 11.5007 22.2477C9.58481 22.5639 7.54429 22.432 5.53344 21.7625ZM26.0661 22.6626C26.3084 22.6626 26.5356 22.7575 26.7067 22.9325C26.8778 23.1048 26.9722 23.3349 26.9722 23.5792C26.9722 23.8245 26.8778 24.0535 26.7067 24.2258C26.5356 24.3997 26.3084 24.4946 26.0661 24.4946C25.8238 24.4946 25.5971 24.3997 25.4255 24.2258C25.2544 24.0535 25.1605 23.8245 25.1605 23.5792C25.1605 23.0743 25.5663 22.6626 26.0661 22.6626ZM25.3127 23.5792C25.3127 23.7825 25.3909 23.9739 25.5334 24.1168C25.6753 24.2618 25.8653 24.3403 26.0661 24.3403C26.4822 24.3403 26.8195 23.999 26.8195 23.5792C26.8195 23.3752 26.7418 23.1839 26.5987 23.0405C26.4568 22.8954 26.2668 22.8175 26.0661 22.8175C25.8653 22.8175 25.6753 22.8954 25.5334 23.0405C25.3909 23.1839 25.3127 23.3752 25.3127 23.5792ZM26.3397 23.0857C26.439 23.1299 26.4892 23.2149 26.4892 23.336C26.4892 23.3981 26.4762 23.4505 26.4498 23.4914C26.4309 23.5208 26.4039 23.5448 26.3737 23.5655C26.3974 23.5797 26.4185 23.5966 26.4341 23.6162C26.4557 23.6446 26.4681 23.6898 26.4698 23.7514L26.4746 23.8818C26.4762 23.9167 26.4784 23.9434 26.4838 23.9597C26.4892 23.9837 26.4994 23.999 26.5124 24.0039L26.5259 24.0104V24.0252V24.0475V24.0699H26.5038H26.3435H26.3305L26.324 24.0584C26.3186 24.0475 26.3149 24.035 26.3111 24.0186C26.3084 24.0039 26.3057 23.9783 26.3041 23.939L26.296 23.7771C26.2938 23.7209 26.2749 23.686 26.2393 23.6675C26.2172 23.6571 26.1805 23.6522 26.1308 23.6522H25.8561V24.0475V24.0699H25.8335H25.7029H25.6807V24.0475V23.0732V23.0508H25.7029H26.1492C26.2258 23.0508 26.29 23.0623 26.3397 23.0857ZM25.8561 23.4963H26.1357C26.1918 23.4963 26.2366 23.4859 26.2685 23.4647C26.2976 23.4434 26.3116 23.4047 26.3116 23.3485C26.3116 23.2875 26.2917 23.2477 26.2496 23.2264C26.2264 23.215 26.194 23.2084 26.153 23.2084H25.8561V23.4963Z"
            fill="white"
        />
    </svg>
)

export default PSCompatible
