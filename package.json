{"name": "pylot-framework", "version": "1.0.0", "scripts": {"dev": "yarn pre-build && next dev", "dev-secure": "node https-server.js", "preload": "ts-node preload/preloadData", "pre-build": "yarn preload", "build": "yarn pre-build && cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" IS_BUILD=true next build --debug", "build:hook": "yarn pre-build && cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" IS_BUILD=true IS_GIT_HOOK=true next build --debug", "build:analyze": "yarn pre-build && cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" IS_BUILD=true BUNDLE_ANALYZE=true next build", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" next start", "start-secure": "cross-env NODE_ENV=production node https-server.js", "analyze": "cross-env BUNDLE_ANALYZE=both yarn build", "find:unused": "next-unused", "generate": "graphql-codegen", "generate:definitions": "node framework/bigcommerce/scripts/generate-definitions.js", "i18n-load": "ts-node i18n-load", "lint": "eslint '*/**/*.{js,ts,tsx}' --quiet --fix", "lint-next": "next lint", "deploy:heroku": "git push heroku master", "postinstall": "yarn patch-package", "contentful:migrate": "node migration/contentful/contentful-migration.js"}, "next-unused": {"alias": {"@lib/*": ["lib/*"], "@styles/*": ["styles/*"], "@config/*": ["config/*"], "@components/*": ["components/*"], "@pagestyles/*": ["styles/page-styles/*"], "@commonStyles/*": ["styles/common/*"], "@utils/*": ["utils/*"]}, "debug": true, "include": ["components", "lib", "pages"], "exclude": [], "entrypoints": ["pages"]}, "dependencies": {"@corsairitshopify/corsair-account-header": "^0.0.4", "@corsairitshopify/corsair-address-book": "^0.0.83", "@corsairitshopify/corsair-breadcrumbs": "^1.0.38", "@corsairitshopify/corsair-cart": "^1.3.05", "@corsairitshopify/corsair-cart-manager": "^1.0.96", "@corsairitshopify/corsair-filters-and-sort": "^1.1.45", "@corsairitshopify/corsair-icons": "^0.0.3", "@corsairitshopify/corsair-image": "^0.0.20", "@corsairitshopify/corsair-klarna": "^0.0.5", "@corsairitshopify/corsair-login-view": "^0.0.18", "@corsairitshopify/corsair-onetrust": "^0.0.4", "@corsairitshopify/corsair-orders": "^1.0.21", "@corsairitshopify/corsair-plp-grid": "^0.0.18", "@corsairitshopify/corsair-price": "^0.0.15", "@corsairitshopify/corsair-product-gallery": "^1.0.94", "@corsairitshopify/corsair-seo": "^0.0.65", "@corsairitshopify/corsair-ui": "^1.0.53", "@corsairitshopify/corsair-variant-selector": "^1.0.8", "@corsairitshopify/corsair-video-player": "^0.0.7", "@corsairitshopify/pylot-account-header": "npm:@corsairitshopify/corsair-account-header@^0.0.4", "@corsairitshopify/pylot-account-information": "^0.0.8", "@corsairitshopify/pylot-account-newsletter": "^0.0.5", "@corsairitshopify/pylot-auth": "1.0.11", "@corsairitshopify/pylot-breadcrumbs": "1.0.7", "@corsairitshopify/pylot-cart": "npm:@corsairitshopify/corsair-cart@1.2.93", "@corsairitshopify/pylot-cart-manager": "npm:@corsairitshopify/corsair-cart-manager@^1.0.96", "@corsairitshopify/pylot-dropdown": "1.0.3", "@corsairitshopify/pylot-filters-and-sort": "1.0.26", "@corsairitshopify/pylot-footer-newsletter": "^1.0.5", "@corsairitshopify/pylot-giftcard-account": "^0.0.4", "@corsairitshopify/pylot-gtm": "1.0.4", "@corsairitshopify/pylot-image": "1.0.2", "@corsairitshopify/pylot-locale-pickers": "1.0.3", "@corsairitshopify/pylot-parallax": "^1.0.11", "@corsairitshopify/pylot-plpsubcategory": "1.0.0", "@corsairitshopify/pylot-price": "npm:@corsairitshopify/corsair-price@^0.0.15", "@corsairitshopify/pylot-product-giftcard": "1.0.5", "@corsairitshopify/pylot-promobanner": "1.0.0", "@corsairitshopify/pylot-recently-viewed": "1.0.4", "@corsairitshopify/pylot-rich-content": "1.0.17", "@corsairitshopify/pylot-seo": "npm:@corsairitshopify/corsair-seo@0.0.3", "@corsairitshopify/pylot-store-credit": "^0.0.5", "@corsairitshopify/pylot-swatches": "1.0.5", "@corsairitshopify/pylot-tag-manager": "npm:@corsairitshopify/corsair-tag-manager@1.0.141", "@corsairitshopify/pylot-ui": "npm:@corsairitshopify/corsair-ui@^1.0.53", "@corsairitshopify/pylot-utils": "1.0.10", "@corsairitshopify/pylot-variant-selector": "npm:@corsairitshopify/corsair-variant-selector@^1.0.7", "@corsairitshopify/pylot-yotpo": "^0.0.8", "@corsairitshopify/pylot-zoom-image": "^0.0.2", "@reach/portal": "^0.11.2", "@tailwindcss/ui": "^0.7.0", "@types/swiper": "^5.4.2", "@vercel/fetch": "^6.1.0", "algoliasearch": "^4.13.0", "body-scroll-lock": "^3.1.5", "bowser": "^2.11.0", "classnames": "^2.2.6", "cookie": "^0.4.1", "create-hash": "^1.2.0", "cross-env": "^7.0.3", "dayjs": "^1.11.12", "email-validator": "^2.0.4", "fast-json-stable-stringify": "2.1.0", "jarallax": "^1.12.5", "js-cookie": "^2.2.1", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lodash.unescape": "^4.0.1", "next": "12.0.1", "next-i18next": "^8.9.0", "next-plugin-preact": "^3.0.6", "next-pwa": "^5.2.23", "next-seo": "^5.1.0", "next-themes": "^0.0.4", "oembed": "^0.1.2", "patch-package": "^8.0.0", "polyfill-object.fromentries": "^1.0.1", "postcss": "^8.4.35", "postcss-nesting": "^7.0.1", "preact": "^10.6.5", "preact-render-to-string": "^5.1.19", "react": "npm:@preact/compat", "react-cookie": "^4.0.3", "react-dom": "npm:@preact/compat", "react-feather": "^2.0.9", "react-focus-lock": "^2.9.6", "react-focus-on": "^3.9.1", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.1.1", "react-instantsearch-dom": "^6.23.3", "react-linkify": "^1.0.0-alpha", "react-loading-skeleton": "^2.2.0", "react-merge-refs": "^1.1.0", "react-outside-click-handler": "^1.3.0", "react-prismazoom": "^3.0.0", "react-slick": "^0.28.1", "react-ssr-prepass": "npm:preact-ssr-prepass", "react-tabs": "^3.1.2", "react-ticker": "^1.2.2", "react-zoom-pan-pinch": "^2.1.3", "recharts": "^2.1.9", "search-insights": "^2.2.1", "slick-carousel": "^1.8.1", "snarkdown": "^2.0.0", "swiper": "^7.4.1", "swr": "^0.5.1", "tabbable": "^5.1.5", "tailwindcss": "^2", "use-media": "^1.4.0", "web-vitals": "^4.1.1"}, "devDependencies": {"@corsairitshopify/eslint-config": "^2.0.2", "@graphql-codegen/cli": "^1.20.0", "@graphql-codegen/schema-ast": "^1.18.1", "@graphql-codegen/typescript": "^1.19.0", "@graphql-codegen/typescript-operations": "^1.17.13", "@manifoldco/swagger-to-ts": "^2.1.0", "@next/bundle-analyzer": "14.2.5", "@types/body-scroll-lock": "^2.6.1", "@types/braintree-web-drop-in": "^1.22.4", "@types/bunyan": "^1.8.6", "@types/bunyan-prettystream": "^0.1.31", "@types/classnames": "^2.2.10", "@types/cookie": "^0.4.0", "@types/create-hash": "^1.2.2", "@types/js-cookie": "^2.2.6", "@types/lodash.debounce": "^4.0.6", "@types/lodash.throttle": "^4.1.6", "@types/lodash.unescape": "^4.0.7", "@types/node": "^14.14.16", "@types/react": "^17.0.0", "@types/react-google-recaptcha": "^2.1.9", "@types/react-instantsearch-dom": "^6.12.2", "@types/react-linkify": "^1.0.1", "@types/react-slick": "^0.23.4", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "bunyan": "^1.8.14", "bunyan-prettystream": "^0.1.3", "commit-message-validator": "^1.0.0", "contentful": "^9.1.34", "contentful-export": "^7.18.4", "contentful-import": "^8.3.2", "cross-env": "^7.0.3", "eslint": "^7.30.0", "eslint-config-next": "^11.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-i18next": "^5.1.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-webpack-plugin": "^2.5.0", "husky": "^4.3.8", "inquirer": "^8.0.0", "lint-staged": "^10.5.4", "next-optimized-classnames": "^1.0.2", "next-transpile-modules": "^9.0.0", "next-unused": "^0.0.3", "postcss-flexbugs-fixes": "^4.2.1", "postcss-preset-env": "^6.7.0", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "react-tabs": "^3.1.2", "sass": "^1.32.5", "typescript": "^4.3.5"}, "resolutions": {"webpack": "5.11.1"}, "license": "UNLICENSED", "husky": {"hooks": {"commit-msg": "commit-message-validator", "pre-commit": "lint-staged", "pre-push": "yarn build:hook", "post-checkout": "yarn", "post-merge": "yarn", "post-rewrite": "yarn"}}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix --quiet"]}, "config": {"commit-message-validator": {"_comment": "Validate that all commits contain ticket number.", "pattern": "[^a-z]+-[0-9]+:.+", "errorMessage": "Commit message MUST begin with a ticket number, following this format: `XYZ-12: Your message here`"}}}