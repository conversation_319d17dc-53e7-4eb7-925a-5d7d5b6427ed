import {
    SearchCriteria,
    SearchCriteriaFilterActions
} from '@corsairitshopify/corsair-filters-and-sort'
import {
    FiltersStateInput,
    PLPManager,
    PLPState
} from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import {
    DEFAULT_CRITERIA_PARAMETER_NAME,
    parseAppliedFilterData,
    serializeSearchCriteria
} from '@corsairitshopify/corsair-filters-and-sort/src/hooks/use-plp-manager'
import type {
    Aggregation,
    Maybe,
    ProductAttributeSortInput
} from '@pylot-data/pylotschema'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useReducer } from 'react'
import { Action, ActionType } from './usePlpManager.types'
// Do not use single ':' as it may conflict with some values
const VALUE_SEPARATOR = '::'
const ITEM_SEPARATOR = '$'
export const ATTR_PRICE_FILTER = 'price_range'
const parseSearchCriteria = (criteriaString = ''): SearchCriteria[] => {
    const parsedCriteria: SearchCriteria[] = []
    const commaSeparatedCriteria = criteriaString.split(ITEM_SEPARATOR)
    commaSeparatedCriteria.forEach((csCriterion) => {
        const values = csCriterion.split(VALUE_SEPARATOR)
        if (values.length === 2) {
            const isPriceFilter = values[0] === ATTR_PRICE_FILTER
            parsedCriteria.push({
                attribute_code: values[0],
                filter_action: isPriceFilter
                    ? SearchCriteriaFilterActions.TO
                    : SearchCriteriaFilterActions.EQ,
                filter_value: values[1]
            })
        } else if (values.length === 1 && values[0] !== '') {
            parsedCriteria[parsedCriteria.length - 1].filter_value = `${
                parsedCriteria[parsedCriteria.length - 1].filter_value
            }-${csCriterion}`
        }
    })
    return parsedCriteria
}
const initialState: PLPState = {
    categoryUid: '',
    aggregations: [],
    filters: {},
    appliedFilterData: [],
    sort: {},
    filtersVisible: false,
    appliedSearchCritieria: []
}
const reducer = (state: PLPState, action: Action): PLPState => {
    switch (action.type) {
        case ActionType.CLEAR_PLP_STATE: {
            const payload = action.payload
            return {
                ...initialState,
                ...payload
            }
        }
        case ActionType.SET_CATEGORY_UID: {
            const payload = action.payload
            return {
                ...state,
                categoryUid: payload
            }
        }
        case ActionType.SET_FILTER: {
            const payload = action.payload as FiltersStateInput
            return {
                ...state,
                filters: payload.filters
                    ? {
                          ...state.filters,
                          ...payload.filters
                      }
                    : {},
                appliedFilterData: payload.appliedFilterData
            }
        }
        case ActionType.SET_AGGREGATIONS: {
            const payload = action.payload as Array<Maybe<Aggregation>>
            return {
                ...state,
                aggregations: payload
            }
        }
        case ActionType.SORT: {
            const payload = action.payload as ProductAttributeSortInput
            return {
                ...state,
                sort: payload
            }
        }
        case ActionType.SET_FILTERS_VISIBLE: {
            const payload = action.payload as boolean
            return {
                ...state,
                filtersVisible: payload
            }
        }
        case ActionType.SET_SEARCH_CRITERIA: {
            const payload = action.payload as SearchCriteria[]
            return {
                ...state,
                appliedSearchCritieria: payload
            }
        }
        default:
            return state
    }
}
export const usePLPManager = (
    args: Partial<PLPState> & {
        disableFilterUrlParams?: boolean
        unfilteredAggregations?: Maybe<Maybe<Aggregation>[]>
        criteriaParameterName?: string
    }
): PLPManager => {
    const {
        disableFilterUrlParams = false,
        unfilteredAggregations = initialState.aggregations,
        criteriaParameterName = DEFAULT_CRITERIA_PARAMETER_NAME,
        ...initialStateOverrides
    } = args
    // Used to get and set searchCriteria in the URL parameters
    const router = useRouter()
    const criteriaParam = router.query[criteriaParameterName]
    const criteriaString =
        criteriaParam &&
        typeof criteriaParam === 'string' &&
        criteriaParam.length > 0
            ? criteriaParam
            : typeof window !== 'undefined' &&
              new URLSearchParams(window.location.search).has(
                  criteriaParameterName
              )
            ? new URLSearchParams(window.location.search).get(
                  criteriaParameterName
              )
            : ''
    const arg = {
        ...initialState,
        ...initialStateOverrides,
        appliedSearchCritieria: parseSearchCriteria(criteriaString as string),
        appliedFilterData: parseAppliedFilterData(criteriaString as string),
        aggregations: unfilteredAggregations
    }
    const [plpState, dispatch] = useReducer(reducer, arg)
    const clearPLPState = useCallback(
        (newPLPState) => {
            dispatch({ type: ActionType.CLEAR_PLP_STATE, payload: newPLPState })
        },
        [dispatch]
    )
    const setCategoryUid = useCallback(
        (categoryUid) => {
            dispatch({
                payload: categoryUid,
                type: ActionType.SET_CATEGORY_UID
            })
        },
        [dispatch]
    )
    const setFilter = useCallback(
        (payload) => {
            dispatch({ payload, type: ActionType.SET_FILTER })
        },
        [dispatch]
    )
    const applySort = useCallback(
        (payload) => {
            dispatch({ payload, type: ActionType.SORT })
        },
        [dispatch]
    )
    const setAggregations = useCallback(
        (payload) => {
            dispatch({ payload, type: ActionType.SET_AGGREGATIONS })
        },
        [dispatch]
    )
    const setFiltersVisible = useCallback(
        (payload) => {
            dispatch({ payload, type: ActionType.SET_FILTERS_VISIBLE })
        },
        [dispatch]
    )
    const setSearchCriteria = useCallback(
        (searchCriteriaList) => {
            if (!disableFilterUrlParams && typeof window !== 'undefined') {
                const existingUrl = new URL(window.location.href)
                existingUrl.searchParams.delete(criteriaParameterName)
                const hasOtherParams = existingUrl.search.length > 0
                const hasQuestionMark =
                    searchCriteriaList.length > 0 || hasOtherParams
                const hasAndSign =
                    searchCriteriaList.length > 0 && hasOtherParams
                const urlSearchParams = new URLSearchParams(
                    window.location.search
                )
                const searchTermParam = urlSearchParams.has('q')
                    ? '&q=' + urlSearchParams.get('q')
                    : ''
                const pageSizeParam = urlSearchParams.has('pageSize')
                    ? '&pageSize=' + urlSearchParams.get('pageSize')
                    : ''
                const memoryFindbyCompatibilityParam = urlSearchParams.get(
                    'type'
                )
                    ? `&type=${urlSearchParams.get('type')}`
                    : ''

                const removedPriceRangeField = searchCriteriaList?.filter(
                    (criteria: any) =>
                        criteria?.attribute_code !== ATTR_PRICE_FILTER
                )

                const newUrlParams =
                    removedPriceRangeField.length > 0
                        ? `${criteriaParameterName}=${serializeSearchCriteria(
                              removedPriceRangeField
                          )}`
                        : ''
                const newUrl = `${window.location.pathname}${
                    hasQuestionMark ? '?' : ''
                }${newUrlParams}${hasAndSign ? '&' : ''}${
                    existingUrl.searchParams.toString().includes('page=')
                        ? `page=1${searchTermParam}${pageSizeParam}${memoryFindbyCompatibilityParam}`
                        : existingUrl.searchParams.toString()
                }${window.location.hash}`
                router.replace(newUrl, undefined, { shallow: true })
            }
            dispatch({
                payload: searchCriteriaList,
                type: ActionType.SET_SEARCH_CRITERIA
            })
        },
        [criteriaParameterName, disableFilterUrlParams, router]
    )
    useEffect(() => {
        setAggregations(unfilteredAggregations)
    }, [setAggregations, unfilteredAggregations])
    useEffect(() => {
        // `criteriaString` param from the url can be empty on first load - so we need to manually set searchCriteria
        // if we find criteria is set on the second render but searchCriteria is unset
        if (
            router.isReady &&
            criteriaString &&
            plpState.appliedSearchCritieria.length === 0
        ) {
            setSearchCriteria(parseSearchCriteria(criteriaString as string))
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router.isReady])
    useEffect(() => {
        if (
            JSON.stringify(plpState.appliedSearchCritieria) !=
            JSON.stringify(parseSearchCriteria(criteriaString as string))
        ) {
            setSearchCriteria(parseSearchCriteria(criteriaString as string))
            dispatch({ payload: arg, type: ActionType.SET_FILTER })
        }
    }, [criteriaString])
    return {
        plpState,
        clearPLPState,
        setCategoryUid,
        setFilter,
        setAggregations,
        applySort,
        setFiltersVisible,
        setSearchCriteria
    }
}
