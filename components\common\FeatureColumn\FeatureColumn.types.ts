import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface FeatureColumnItem {
    description?: string
    icon: CloudinaryImage[]
    title: string
}

export interface FeatureColumnContent {
    buttonContent?: string
    buttonIcon?: CloudinaryImage[]
    buttonLink?: string
    itemSpacingRightDesktop?: number
    gap: 'small' | 'medium' | 'large'
    itemType?: 'Vertical' | 'Horizontal'
    title: string
    items: FeatureColumnItem[]
}

export interface FeatureColumnItemProps
    extends Pick<
        FeatureColumnContent,
        'itemType' | 'itemSpacingRightDesktop' | 'gap'
    > {
    data: FeatureColumnItem
}

export interface FeatureColumnProps {
    content: FeatureColumnContent
}
