import CloudinaryVideoFilePlayer from '@components/common/Carousel/CloudinaryVideoFilePlayer'
import { DisclaimerBlock } from '@components/common/DisclaimerBlock'
import TopTextLayout from '@components/common/TopTextLayout'
import { ArrowDown } from '@components/icons'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import {
    CTAType,
    ImageType,
    VideoType,
    ctaLayout
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import {
    convertUrlFormat,
    generateVideoThumbnail
} from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { CSSProperties, useEffect, useRef, useState } from 'react'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { JsonToCss } from '../HeroBanner/Util/JsonToCss'
import { inlineStyling } from '../types'
import s from './HeroStandard.module.scss'
import { HeroStandardCTA } from './HeroStandardCTA'
import { HeroStandardIconsBlock } from './HeroStandardIconsBlock'
import { HeroStandardImageOrVideo } from './HeroStandardImageOrVideo'
import { HeroStandardLogo } from './HeroStandardLogo'

export type Icon = {
    title: string
    image: ImageType
    position: string
    color: string
    meta: { contentType: string }
    cloudinaryImage?: CloudinaryImage[]
}

export interface HeroStandardResponse {
    title: string
    contentPosition?: 'Top Center' | 'Top Left' | 'Center Left' | 'Bottom Left'
    headingEmphasis?: 'default' | 'italic'
    heading: string
    headingColor?: string
    subHeading: string
    subHeadingColor?: string
    text: string
    fontColor?: string
    backgroundColor?: string
    backgroundImage?: ImageType | VideoType
    meta: { contentType: string }
    logo?: ImageType
    logoWidth?: string
    logoHeight?: string
    desktopContentPosition: string
    mobileContentPosition: string
    disclaimerTextOne?: string
    desktopImageVideo: ImageType | VideoType
    desktopImageWidth?: string
    desktopImageHeight?: string
    mobileImageHeight?: string
    mobileImageVideo?: ImageType | VideoType
    iconsBlock?: [Icon]
    ctaButton: CTAType
    ctaButtons: CTAType[]
    ctaLocation?: string
    backgroundLinearGradientColor?: string
    headingType: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryBackgroundImageMobile?: CloudinaryImage[]
    cloudinaryDesktopMedia?: CloudinaryImage[]
    cloudinaryMobileMedia?: CloudinaryImage[]
    cloudinaryLogo?: CloudinaryImage[]
    cloudinaryLogoImage?: CloudinaryImage[]
    imageLayout?: boolean
    ctaLayout?: ctaLayout
    inlineStyling: inlineStyling
    videoDescription?: string
    scrollCta?: boolean
    paddingSize: 'small' | 'large' | 'medium'
    horizonPaddingSize: 'none' | 'large'
    textSize: 'medium' | 'small' | 'tiny'
}

export interface HeroStandardProps {
    content: HeroStandardResponse
}

const c = /*tw*/ {
    heroStandardWrapper: `${s['hero-standard-wrapper']} mx-auto block w-full h-full bg-center bg-no-repeat bg-cover absolute top-0 left-0`,
    heroStandard: `${s['hero-standard']} block opacity-0 relative`,
    heroStandardImage: `${s['hero-standard-image']} relative`,
    heroStandardText: `${s['hero-standard-text']} text-white`,
    heroStandardHeading: `${s['hero-standard-heading']} uppercase font-tomorrow`,
    heroStandardMain: 'overflow-hidden',
    heroStandardVideo: 'w-full h-full object-center object-cover',
    heroStandardDisclaimer: s['hero-standard-disclaimer']
}

// Helper functions to extract and handle background logic
const getBackgroundType = (
    content: HeroStandardResponse,
    isMobile: boolean
) => {
    const mobileMedia = content.cloudinaryBackgroundImageMobile?.[0]
    const desktopMedia = content.cloudinaryBackgroundImage?.[0]
    const media = isMobile ? mobileMedia || desktopMedia : desktopMedia

    return {
        isImage: media?.resource_type === 'image',
        isVideo: media?.resource_type === 'video',
        url: media?.secure_url
    }
}

const getBackgroundStyle = (content: HeroStandardResponse, isImage = false) => {
    const mobileUrl = content.cloudinaryBackgroundImageMobile?.[0]?.secure_url
    const desktopUrl = content.cloudinaryBackgroundImage?.[0]?.secure_url

    return {
        '--desktop-bg-image': `url(${desktopUrl})`,
        '--mobile-bg-image': `url(${mobileUrl || desktopUrl})`,
        '--bg-color':
            isImage && content.backgroundColor ? content.backgroundColor : ''
    } as React.CSSProperties
}

const HeroStandard = ({ content }: HeroStandardProps): JSX.Element => {
    const containerRef = useRef<HTMLDivElement>(null)
    const animateRef = useRef(null)
    const { isOnScreen } = useOnScreen(animateRef, true)
    const router = useRouter()
    const currentPage = router.pathname
    const isMobileHeader = useMediaQuery('(max-width: 911px)')
    const { t } = useTranslation(['common'])
    const isMobile = useMediaQuery('(max-width: 767px')

    const [backgroundType, setBackgroundType] = useState<{
        isImage: boolean
        isVideo: boolean
        url: string | undefined
    }>()

    useEffect(() => {
        setBackgroundType(getBackgroundType(content, isMobile))
    }, [content, isMobile])

    const {
        contentPosition,
        headingEmphasis,
        heading,
        subHeading,
        text,
        headingType,
        ctaLocation = 'Top',
        inlineStyling
    } = content

    const isLeftAlignment = contentPosition?.includes('Left')

    const imageOrVideoBlock = () => {
        let data = isMobile
            ? content?.cloudinaryMobileMedia
            : content?.cloudinaryDesktopMedia

        if (isMobile && !content?.cloudinaryMobileMedia) {
            data = content?.cloudinaryDesktopMedia
        }
        return (
            !isLeftAlignment &&
            data &&
            data?.length > 0 && (
                <HeroStandardImageOrVideo
                    data={data}
                    ctaLocation={ctaLocation}
                    cta={content}
                    desktopImageWidth={content?.desktopImageWidth}
                    desktopImageHeight={content?.desktopImageHeight}
                    imageLayout={content.imageLayout}
                    videoDescription={content?.videoDescription}
                />
            )
        )
    }

    const data = {
        headingEmphasis,
        heading,
        subHeading,
        text,
        headingType,
        isShowCTAButton: Boolean(
            content.ctaButton || content.ctaButtons?.length > 0
        ),
        textPosition: content.contentPosition
            ?.toLowerCase()
            .replace(/\s+/g, '-'),
        descriptionFontSize: content.textSize
    }

    const stylesDesktop = {
        ...(inlineStyling?.paddingTopDesktop !== undefined && {
            'padding-top': `${inlineStyling.paddingTopDesktop}rem;`
        }),
        ...(inlineStyling?.paddingBottomDesktop !== undefined && {
            'padding-bottom': `${inlineStyling.paddingBottomDesktop}rem;`
        }),
        ...(!inlineStyling?.paddingBottomDesktop && {
            'padding-bottom':
                content?.cloudinaryDesktopMedia && !isLeftAlignment
                    ? '0;'
                    : '67px;'
        }),
        ...(!inlineStyling?.paddingTopDesktop && {
            'padding-top': content?.cloudinaryLogoImage?.[0]?.secure_url
                ? '37px;'
                : '67px;'
        })
    }

    const stylesMobile = {
        ...(inlineStyling?.paddingTopMobile !== undefined && {
            'padding-top': `${inlineStyling.paddingTopMobile}rem;`
        }),
        ...(inlineStyling?.paddingBottomMobile !== undefined && {
            'padding-bottom': `${inlineStyling.paddingBottomMobile}rem;`
        }),
        ...(!inlineStyling?.paddingBottomMobile && {
            'padding-bottom':
                content?.cloudinaryDesktopMedia ||
                (content?.cloudinaryMobileMedia && !isLeftAlignment)
                    ? '0;'
                    : '24px;'
        }),
        ...(!inlineStyling?.paddingTopMobile && {
            'padding-top': content?.cloudinaryLogoImage?.[0]?.secure_url
                ? '5px;'
                : '30px;'
        })
    }

    const paddingsClassheading =
        (content?.title && content.title?.replace(/[^a-zA-Z0-9_-]+/g, '')) ??
        'HeroStandard'

    const handleScrollBtnClick = () => {
        const currentContent = containerRef.current
        const nextContent = currentContent?.nextElementSibling
        const isHomepage = currentPage === '/'

        if (nextContent) {
            const headerContentOffset = isMobileHeader
                ? 60
                : !isMobileHeader && isHomepage
                ? 72
                : 0
            const nextContentOffset = nextContent.getBoundingClientRect()
            const offsetPosition =
                window.scrollY + nextContentOffset.top - headerContentOffset
            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            })
        }
    }
    const ScrollCTAButton = () => {
        const [showButton, setShowButton] = useState(false)

        useEffect(() => {
            if (isOnScreen) {
                const timer = setTimeout(() => {
                    setShowButton(true)
                }, 150)

                return () => clearTimeout(timer)
            } else {
                setShowButton(false)
            }
        }, [isOnScreen])

        return (
            <button
                className={cn(
                    s['scroll-cta-btn'],
                    'flex items-center left-1/2 absolute justify-center',
                    showButton && s['btnOnScreen']
                )}
                onClick={handleScrollBtnClick}
                aria-label={t('common|Scrolls down the page')}
            >
                <ArrowDown />
            </button>
        )
    }

    return (
        <div ref={containerRef}>
            <section
                className={cn(
                    'relative',
                    {
                        [c.heroStandardMain]:
                            (backgroundType?.isImage &&
                                !content.backgroundColor &&
                                !content.backgroundLinearGradientColor) ||
                            backgroundType?.isVideo
                    },
                    s['hero-container']
                )}
                style={
                    {
                        '--desktop-height': content.desktopImageHeight
                            ? `${content.desktopImageHeight}px`
                            : '962px',
                        '--mobile-height': content.mobileImageHeight
                            ? `${content.mobileImageHeight}px`
                            : '500px'
                    } as CSSProperties
                }
            >
                {backgroundType?.isVideo && (
                    <div className="w-full h-full absolute hehe">
                        <CloudinaryVideoFilePlayer
                            videoUrl={convertUrlFormat(
                                backgroundType.url || ''
                            )}
                            alt={
                                isMobile
                                    ? content
                                          .cloudinaryBackgroundImageMobile?.[0]
                                          ?.context?.custom?.alt || ''
                                    : content.cloudinaryBackgroundImage?.[0]
                                          ?.context?.custom?.alt ||
                                      content.title
                            }
                            fallbackImgUrl={generateVideoThumbnail(
                                backgroundType?.url || ''
                            )}
                            className={c.heroStandardVideo}
                            videoDescription={content.videoDescription}
                        />
                    </div>
                )}
                {backgroundType?.isImage &&
                    !content.backgroundColor &&
                    !content.backgroundLinearGradientColor && (
                        <div
                            className={cn(
                                c.heroStandardWrapper,
                                s['hero-background-image']
                            )}
                            style={getBackgroundStyle(content)}
                        />
                    )}
                {content.backgroundColor &&
                    content.cloudinaryBackgroundImage?.length !== 0 && (
                        <div
                            className={cn(
                                c.heroStandardWrapper,
                                s['hero-background-image']
                            )}
                            style={getBackgroundStyle(
                                content,
                                backgroundType?.isImage
                            )}
                        />
                    )}

                {backgroundType?.isImage &&
                    !backgroundType?.isVideo &&
                    content.backgroundLinearGradientColor &&
                    content.backgroundColor && (
                        <div
                            className={cn(c.heroStandardWrapper)}
                            style={{
                                background:
                                    content.backgroundLinearGradientColor
                            }}
                        />
                    )}

                <div
                    className={cn(
                        c.heroStandard,
                        {
                            [s['onScreen']]: isOnScreen
                        },
                        `align${
                            isMobile
                                ? content?.mobileContentPosition
                                : content?.desktopContentPosition
                        }`,
                        `verticalPaddings-${paddingsClassheading}`,
                        s['hero-standard-content'],
                        !data.isShowCTAButton
                            ? s['no-cta-button-with-verticalContentPaddings']
                            : s['has-cta-button-with-verticalContentPaddings']
                    )}
                    ref={animateRef}
                    style={
                        {
                            background:
                                content.backgroundLinearGradientColor || ''
                        } as CSSProperties
                    }
                    horizon-padding-size={content.horizonPaddingSize}
                    vertical-padding-size={content.paddingSize}
                    content-position={content.contentPosition
                        ?.toLowerCase()
                        .replace(/\s+/g, '-')}
                >
                    <style jsx>{`
                    @media screen and (min-width: 768px){
                        .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                        stylesDesktop
                    )}
                    }
                    @media screen and (max-width: 767px) {
                        .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                        stylesMobile
                    )}
                    }`}</style>
                    {content?.cloudinaryLogoImage?.[0]?.secure_url && (
                        <HeroStandardLogo
                            logo={content?.cloudinaryLogoImage}
                            logoWidth={content?.logoWidth}
                            logoHeight={content?.logoHeight}
                        />
                    )}
                    {data && (
                        <TopTextLayout
                            data={data}
                            textClassName={cn(
                                s['hero-standard-description'],
                                'description-section'
                            )}
                            fontColor={content?.fontColor}
                            headingColor={content?.headingColor}
                            subHeadingColor={content?.subHeadingColor}
                        />
                    )}
                    {(content?.ctaButton ||
                        (content?.ctaButtons &&
                            content.ctaButtons.length > 0)) && (
                        <div
                            className={cn(
                                'block',
                                ctaLocation !== 'Top' && 'md:hidden',
                                isLeftAlignment &&
                                    s['hero-standard-button-left-align']
                            )}
                        >
                            <HeroStandardCTA content={content} />
                        </div>
                    )}
                    {!isLeftAlignment && content?.disclaimerTextOne && (
                        <DisclaimerBlock
                            disclaimer={content.disclaimerTextOne}
                            useBackground={false}
                            className={c.heroStandardDisclaimer}
                            fontColor="#999"
                        />
                    )}
                    {!isLeftAlignment && content?.iconsBlock && (
                        <HeroStandardIconsBlock icons={content?.iconsBlock} />
                    )}
                    {imageOrVideoBlock()}
                </div>
                {(content?.ctaButton ||
                    (content?.ctaButtons && content.ctaButtons.length > 0)) &&
                    content?.cloudinaryBackgroundImage &&
                    ctaLocation !== 'Top' && (
                        <div
                            className={cn(
                                ctaLocation !== 'Top' && 'hidden md:block'
                            )}
                        >
                            <HeroStandardCTA content={content} />
                        </div>
                    )}
            </section>
            {content?.scrollCta && <ScrollCTAButton />}
        </div>
    )
}

export default HeroStandard
