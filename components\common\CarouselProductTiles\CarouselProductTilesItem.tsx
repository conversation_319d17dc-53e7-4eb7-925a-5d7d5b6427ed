import { useBaseUrl } from '@config/hooks/useBaseUrl'
import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { ChevronRight } from 'react-feather'
import { ProductsSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselProductTiles.module.scss'
import Link from 'next/link'

export type CarouselProductTilesItemProps = {
    product: ProductsSlideType
}

const PRIMARY_COLOR = 'var(--primary)'

const CarouselProductTilesItem: FC<CarouselProductTilesItemProps> = ({
    product
}) => {
    const { t } = useTranslation(['common'])

    const { region, language } = useBaseUrl(
        typeof window !== 'undefined' ? window.location.href : ''
    )

    const Item = (
        <div
            className={cn(s['slider-item-container'])}
            style={{
                backgroundColor:
                    product?.backgroundColor ?? 'var(--secondary-off-black)'
            }}
        >
            <div className={s['slider-item-text-block']}>
                <p
                    className={s['slider-item-heading']}
                    style={{
                        color: product.headingColor
                    }}
                >
                    {product.heading}
                </p>
                {product?.cta && (
                    <a
                        href={
                            product?.cta?.url.includes('/s/') ||
                            product?.cta?.url.includes('/c/') ||
                            product?.cta?.url.includes('/p/')
                                ? `/${region.toLowerCase()}/${language}${
                                      product?.cta?.url
                                  }`
                                : product?.cta?.url
                        }
                        role="button"
                        rel="noreferrer"
                        target={
                            product?.cta?.openInANewTab ? '_blank' : '_self'
                        }
                        aria-label={product?.cta?.displayText}
                        className={cn(s['slider-item-cta'])}
                        style={{
                            color: product?.textColor ?? PRIMARY_COLOR
                        }}
                    >
                        <p className={s['slider-item-cta-text']}>
                            {product?.cta?.displayText}
                        </p>
                        <ChevronRight
                            className={s['slider-icon']}
                            color={PRIMARY_COLOR}
                        />
                    </a>
                )}
            </div>
            <div
                className={cn(
                    s['slider-item-image-container'],
                    'relative w-full h-full'
                )}
            >
                <CorsairImage
                    keepOrigin
                    src={convertUrlFormat(
                        product.cloudinaryMainImage?.[0]?.secure_url
                    )}
                    alt={t(
                        `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                    )}
                    layout="fill"
                    objectFit="contain"
                />
            </div>
        </div>
    )

    if (product.url) {
        return (
            <Link href={product.url}>
                <a>{Item}</a>
            </Link>
        )
    }

    return Item
}

export default CarouselProductTilesItem
