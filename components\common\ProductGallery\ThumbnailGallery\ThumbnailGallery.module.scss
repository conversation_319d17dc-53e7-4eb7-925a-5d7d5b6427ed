.thumbnail-gallery {
    display: flex;
    width: 100%;
    position: relative;

    .thumbnails-container {
        display: flex;
        flex-direction: column;
        width: 80px;
        margin-right: 20px;

        .thumbnail {
            width: 80px;
            height: 80px;
            margin-bottom: 10px;
            cursor: pointer;
            border: 1px solid transparent;
            transition: border-color 0.2s ease;

            &.active {
                border-color: #333132;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }

    .main-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: calc(100% - 80px);
        position: relative;
    }
}

@media (max-width: 767px) {
    .thumbnail-gallery {
        flex-direction: column-reverse;

        .thumbnails-container {
            flex-direction: row;
            width: 100%;
            margin-right: 0;
            margin-top: 10px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;

            .thumbnail {
                width: 60px;
                height: 60px;
                margin-right: 10px;
                margin-bottom: 0;
            }
        }
    }
}

.scroll-image-container {
    @apply w-auto;
}

.thumb-mask-more-items {
    @apply absolute w-full h-full flex justify-center items-center left-0 bottom-0 z-1;
    cursor: default;

    >span {
        @apply text-black text-3xl flex justify-center items-center font-semibold h-full w-full cursor-pointer font-sairaHeader pr-2.5;
        background-color: #ffffffb3;
    }
}

.gallery-thumbnails-vertical {
    @screen md {
        top: 0;
        left: -114px;
        width: auto !important;

        :global {
            .accessory-thumbnails {
                .swiper-slide {
                    width: 80px;
                    height: 80px;
                    background: #F5F5F5;
                    padding-right: 4px;
                }

                .swiper-slide .swiper-slide-image-container {
                    border: 2px solid transparent;
                }

                .swiper-slide.swiper-slide-thumb-active .swiper-slide-image-container {
                    border-color: #333132;
                    border-radius: 0;

                    &::after {
                        content: none !important;
                    }
                }
            }
        }
    }
}

.accessory-gallery-container {
    :global {
        .accessory-mobile-slider-bar {
            background-color: #333132;

            span {
                background-image: none !important;
                background-color: #FF8F1C;
                border-radius: 9999px;
            }
        }
    }

    .accessory-gallery-slider {
        @screen md {
            max-width: min(82vw, 817px) !important;
            width: 100vw !important;
        }

        @screen lg {
            max-width: min(50vw, 817px) !important;
        }

    }
}