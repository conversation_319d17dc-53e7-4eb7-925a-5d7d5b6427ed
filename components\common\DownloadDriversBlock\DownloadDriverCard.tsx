import React from 'react'
import cn from 'classnames'

import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import CorsairImage from '@corsairitshopify/corsair-image'

import { DownloadDriverCardProps } from './types'
import { useDownloadDriverNewsletter } from './DownloadDriverNewsletterContext'

import s from './DownloadDriverCard.module.scss'
import { useTranslation } from 'next-i18next'

const DownloadDriverCard: React.FC<DownloadDriverCardProps> = ({ content }) => {
    const {
        softwareImage = [],
        softwareTitle = '',
        softwarePlatform = '',
        releaseInfo = '',
        releaseNotes = '',
        link = ''
    } = content

    const { t } = useTranslation()
    const { openForm } = useDownloadDriverNewsletter()

    return (
        <div className={cn(s['download-driver-card'])}>
            <div className={s['download-driver-card--image']}>
                <CorsairImage
                    src={convertUrlFormat(softwareImage?.[0]?.secure_url)}
                    alt={softwareTitle}
                    keepOrigin
                    layout="fill"
                    objectFit="contain"
                />
            </div>
            <div className={s['download-driver-card--content']}>
                <div className={s['download-driver-card--download-info']}>
                    <h2
                        className={s['download-driver-card--title']}
                        aria-label={softwareTitle}
                    >
                        {softwareTitle}
                    </h2>
                    {!!softwarePlatform && (
                        <div
                            className={s['download-driver-card--platform']}
                            aria-label={softwarePlatform}
                        >
                            {softwarePlatform}
                        </div>
                    )}
                    {!!releaseNotes && (
                        <div
                            className={s['download-driver-card--release-info']}
                            dangerouslySetInnerHTML={{
                                __html: releaseNotes.replaceAll(
                                    '<p></p>',
                                    '<br />'
                                )
                            }}
                        />
                    )}
                    {!!releaseInfo && (
                        <div
                            className={s['download-driver-card--release-info']}
                            aria-label={releaseInfo}
                        >
                            {releaseInfo}
                        </div>
                    )}
                </div>
                <div>
                    <button
                        className={cn(
                            'scuf-button-secondary',
                            s['download-driver-card--download-button']
                        )}
                        data-download-link={link}
                        data-version-name={softwareTitle}
                        onClick={() => openForm(link, softwareTitle)}
                    >
                        <span>{t('Download')}</span>
                    </button>
                </div>
            </div>
        </div>
    )
}

export default DownloadDriverCard
