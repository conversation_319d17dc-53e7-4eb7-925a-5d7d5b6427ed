/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable i18next/no-literal-string */
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useMemo, useRef, VFC } from 'react'
import { A11y, Navigation, Pagination, SwiperOptions } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'

import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import { generateRandomString } from 'helpers/stringHelper'

import {
    CarouselWrapperType,
    ImageTextSlideType
} from '../CarouselWrapper/CarouselWrapper.types'
import { JsonToCss } from '../HeroBanner/Util/JsonToCss'
import s from './CarouselImageText.module.scss'
import CarouselImageTextItem from './CarouselImageTextItem'
import EffectShadowSide from './EffectShadowSide'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import CarouselImageTextCTAItem from '../CarouselImageTextCTA/CarouselImageTextCTAItem'

const c = /*tw*/ {
    root: `${s['root']} text-white m-auto h-full`,
    heading: `${s['heading']} text-center uppercase font-tomorrow`
}

type CarouselImageTextProps = {
    content: CarouselWrapperType
    variants?: 'cta' | 'no-cta'
}

const ARROW_COLOR = 'var(--secondary-off-black)'

export const CarouselImageText: VFC<CarouselImageTextProps> = ({
    content,
    variants = 'no-cta'
}) => {
    const { t } = useTranslation(['common'])
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)

    const isMobile = useMediaQuery('(max-width: 767px)')

    const productSlides = content.productSlide as ImageTextSlideType[]

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const carouselImageTextId = useMemo(() => generateRandomString(5), [
        content
    ])
    const paddingsClassheading = useMemo(
        () =>
            `${
                (content?.heading && content?.heading?.replace(/\s+/g, '')) ??
                'ImageTextBlockImage'
            }-${carouselImageTextId}`,
        [carouselImageTextId, content?.heading]
    )
    const formattedName = useMemo(() => {
        return generateRandomString(10)
    }, [])

    const stylesDesktop = {
        ...(content?.desktopPaddingTop && {
            'padding-top': `${content?.desktopPaddingTop}rem;`
        }),
        ...(content?.desktopPaddingBottom && {
            'padding-bottom': `${content?.desktopPaddingBottom}rem;`
        }),
        ...(content?.cloudinaryBackgroundImage && {
            background: `no-repeat center center / cover url(${content?.cloudinaryBackgroundImage?.[0].secure_url});`,
            'background-size': 'cover'
        }),
        ...(content?.backgroundColor && {
            background: `${content?.backgroundColor};`
        })
    }

    const stylesMobile = {
        ...(content?.mobilePaddingTop && {
            'padding-top': `${content?.mobilePaddingTop}rem;`
        }),
        ...(content?.mobilePaddingBottom && {
            'padding-bottom': `${content?.mobilePaddingBottom}rem;`
        }),
        ...(content?.cloudinaryMobileBackgroundImage && {
            background: `no-repeat center center / cover url(${content?.cloudinaryMobileBackgroundImage?.[0].secure_url});`,
            'background-size': 'cover'
        }),
        ...(content?.backgroundColor && {
            background: `${content?.backgroundColor};`
        })
    }

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: any) => {
        animatePagination(swiper)
    }

    const renderSlideItem = (prod: ImageTextSlideType) => {
        if (variants === 'cta') {
            return <CarouselImageTextCTAItem product={prod} />
        }
        return <CarouselImageTextItem product={prod} />
    }

    return (
        <div
            id={content?.identifier}
            className={cn(
                `verticalPaddings-${paddingsClassheading}`,
                s['carousel-wrapper']
            )}
        >
            <style jsx>{`
                @media screen and (min-width: 768px){
                    .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                stylesDesktop
            )}
                }
                @media screen and (max-width: 767px) {
                    .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                stylesMobile
            )}
                }
            `}</style>

            <div className={s['product-tiles-top-container']}>
                {content?.heading && (
                    <h3
                        className={s['header']}
                        style={{ color: content?.headingColor }}
                    >
                        {content?.heading}
                    </h3>
                )}
            </div>

            <div className={cn(s['image-text-swiper-container'])}>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['prev'],
                        `${formattedName}-prev`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                    )}
                    aria-label={t('previous')}
                >
                    <ArrowRightIcon />
                </button>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['next'],
                        `${formattedName}-next`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                    )}
                    aria-label={t('next')}
                >
                    <ArrowRightIcon />
                </button>

                <Swiper
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    className={cn(s['swiper-container'])}
                    modules={[Navigation, Pagination, A11y, EffectShadowSide]}
                    effect={'shadow-side' as SwiperOptions['effect']}
                    mousewheel
                    spaceBetween={10}
                    slidesPerView={1.2}
                    slidesPerGroup={1}
                    slidesOffsetBefore={20}
                    slidesOffsetAfter={20}
                    loop={!isMobile}
                    breakpoints={{
                        992: {
                            centeredSlides: true,
                            slidesOffsetBefore: 0,
                            slidesOffsetAfter: 0,
                            spaceBetween: 20,
                            slidesPerView: 1.4,
                            slidesPerGroup: 1
                        }
                    }}
                    onBreakpoint={animatePagination}
                    onSlideChange={(swiper) => {
                        animatePagination(swiper)
                        handleSlideChange(swiper)
                    }}
                    navigation={{
                        prevEl: `.${formattedName}-prev`,
                        nextEl: `.${formattedName}-next`,
                        disabledClass: '.disabled:opacity-50'
                    }}
                    pagination={{
                        el: `.${formattedName}-pagination`,
                        clickable: true,
                        renderBullet: (idx, className) => {
                            return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                        }
                    }}
                >
                    {productSlides.map((prod, key) => (
                        <SwiperSlide key={key}>
                            {renderSlideItem(prod)}
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            <div
                className={cn(
                    s['pagination-container'],
                    s['pagination-container-mobile']
                )}
                ref={containerRef}
            >
                <div className="inline-block relative">
                    <div
                        ref={paginationRef}
                        className={`${formattedName}-pagination flex gap-4`}
                    />
                    <div className="dots-line" />
                    <div ref={activeDotRef} className="active-animated-dot" />
                </div>
            </div>
        </div>
    )
}

export default CarouselImageText
