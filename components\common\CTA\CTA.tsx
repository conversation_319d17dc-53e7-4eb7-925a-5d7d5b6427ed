import { CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import { ButtonLabel } from '@pylot-data/hooks/product/use-product-ui'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC } from 'react'

export type CTAProps = {
    cta: CTAType
    className?: string
    showDisplayText?: boolean
    rightIcon?: React.ReactElement
    rightIconClassName?: string
    isLearnMoreBtn?: boolean
    style?: React.CSSProperties
    containerClassname?: string
    buttonType?: 'primary' | 'secondary'
}

const CTA: FC<CTAProps> = ({
    cta = {},
    className,
    children,
    showDisplayText = true,
    rightIcon,
    isLearnMoreBtn = false,
    rightIconClassName,
    style,
    containerClassname = ''
}) => {
    const { t } = useTranslation(['common'])
    return (
        <div className={cn('flex items-center', containerClassname)}>
            <a
                href={cta.url}
                target={cta.openInANewTab ? '_blank' : ''}
                rel="noreferrer"
                className={cn('corsair-links', className)}
                style={{
                    ...style,
                    backgroundColor:
                        cta?.backgroundColor ?? style?.backgroundColor
                }}
            >
                {isLearnMoreBtn
                    ? t(ButtonLabel.LEARN_MORE)
                    : showDisplayText && cta.displayText}
                {children}
            </a>
            {rightIcon && (
                <span className={rightIconClassName}>{rightIcon}</span>
            )}
        </div>
    )
}

export default CTA
