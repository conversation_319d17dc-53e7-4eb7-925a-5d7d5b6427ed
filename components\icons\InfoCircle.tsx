import { FC, HTMLAttributes } from 'react'

interface InfoCircleProps extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
    color?: string
}

const InfoCircle: FC<InfoCircleProps> = ({
    color = '#151515',
    width = 18,
    height = 19
}) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 18 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M9 14.5C8.58579 14.5 8.25 14.1642 8.25 13.75V8.75C8.25 8.33579 8.58579 8 9 8C9.41421 8 9.75 8.33579 9.75 8.75V13.75C9.75 14.1642 9.41421 14.5 9 14.5Z"
            fill={color}
        />
        <path
            d="M9 4.5C8.44772 4.5 8 4.94772 8 5.5C8 6.05228 8.44772 6.5 9 6.5C9.55229 6.5 10 6.05228 10 5.5C10 4.94772 9.55229 4.5 9 4.5Z"
            fill={color}
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9 18.5C13.9706 18.5 18 14.4706 18 9.5C18 4.52944 13.9706 0.5 9 0.5C4.02944 0.5 0 4.52944 0 9.5C0 14.4706 4.02944 18.5 9 18.5ZM9 17C13.1421 17 16.5 13.6421 16.5 9.5C16.5 5.35786 13.1421 2 9 2C4.85786 2 1.5 5.35786 1.5 9.5C1.5 13.6421 4.85786 17 9 17Z"
            fill={color}
        />
    </svg>
)

export default InfoCircle
