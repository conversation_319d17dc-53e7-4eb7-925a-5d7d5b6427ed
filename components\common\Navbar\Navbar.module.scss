.header {
    @apply flex bg-black items-center relative z-10 border-gray-800  border-b py-2;
    height: 72px;
    @media (min-width: 912px) {
        @apply block border-none py-0 bg-t-1;
    }
    @screen md-max {
        z-index: 51;
    }

    &.overlay-header {
        @apply absolute left-0 right-0 transition-all duration-300;
        background-color: unset;

        .root {
            @apply transition-all duration-300;

            &.shadow-magical {
                @apply sticky shadow-sm bg-primary transition-all duration-300;
                z-index: 99;
            }

            @media (min-width: 912px) {
                border: unset;
                background-color: unset;
            }
        }

        .nav-container {
            background-color: unset;
        }
    }

    &.overlay-header.header-bg-display {
        @apply transition-all duration-300;
    }
}

.root {
    @apply relative top-0 bg-primary transition-all duration-150 w-full h-full z-10 border-gray-800;
    z-index: 51;
    &.shadow-magical {
        @apply sticky shadow-sm bg-primary transition-all duration-150;
        z-index: 99;
    }
    @media (min-width: 912px) {
        @apply z-50 border-b bg-t-1;
    }
    @media (max-width: 911px) {
        padding: 0 20px;
    }
}

.root.search-bar-display {
    @media (max-width: 911px) {
        padding: 0 8px;
        .nav-right-section {
            display: none;
        }
    }
}

.megamenu-cms {
    @apply mr-5 ml-4 md:mx-0 md:py-3;
}

.nav-container {
    @apply bg-primary transition-all duration-150 xl:px-0 mx-auto items-center border-r-0 relative z-50 h-full;
    &-inner {
        @media (min-width: 912px) {
            @apply relative xl:static m-auto flex flex-row-reverse justify-between items-center h-full;
            @apply flex-row;
        }
        @media (max-width: 911px) {
            @apply relative xl:static m-auto flex justify-end items-center h-full;
        }
    }
}
.nav-section {
    @apply flex-1 flex z-50;
    @media (min-width: 912px) {
        max-width: 120px;
    }
}

.nav-right-section {
    @apply md:relative flex-1 flex z-50 justify-end;
    max-width: 120px;
    padding-right: 10px;
    height: 100%;
}

.icon-container {
    margin-right: 12px;
    @apply z-1 inset-y-0 md:left-auto md:right-0.5 items-center flex bg-transparent;
    height: 60px;
    max-width: 46px;
    @media (max-width: 912px) {
        height: 100%;
    }
}

.search-section {
    @apply md:relative justify-start md:flex h-full items-center;
    max-width: 50%;
    > div {
        > div {
            @apply cursor-pointer;
        }
    }
}

.mobile-search-icon {
    @apply absolute top-2.5 left-9 md:hidden;
}
.desktop-search-display {
    @apply flex-1 hidden lg:flex absolute max-w-screen-xl mx-auto bg-primary w-full z-50;
}
.mobile-search-display {
    @apply relative flex lg:px-6 lg:hidden border border-accents-3 mb-3;
}
.toggle-search {
    @apply cursor-pointer flex items-center text-accents-6 lg:text-primary w-5 h-9 lg:w-auto lg:h-auto mr-1.5 lg:mr-0;
}
.link {
    @apply inline-flex items-center leading-6 font-medium transition ease-in-out duration-75 cursor-pointer text-accents-6;
}

.link:hover {
    @apply text-accents-9;
}

.link:focus {
    @apply outline-none text-accents-8;
}

.logo {
    @apply col-end-13;
    @media (min-width: 912px) {
        @apply ml-5;
    }
    &:focus {
        outline: none;
    }

    svg {
        @apply cursor-pointer;
    }

    @media (min-width: 912px) {
        svg {
            height: 2.05rem;
        }
    }
}

.invert-color {
    @apply transition-all duration-150;
    filter: invert(1);
}

@media (max-width: 911px) {
    .nav-right-section {
        height: 40px;
        z-index: 50 !important;
    }

    .search-icon {
        height: 48px;
        width: 48px;
    }

    .header {
        height: 60px;
        padding: unset;
        border: unset;

        > .root > div:not(.nav-container) {
            @apply absolute left-0 z-5;
            top: 54px;

            > button {
                @apply absolute -top-13 left-7;
            }
        }

        &.overlay-header {
            .root {
                background-color: unset;
            }
        }
    }

    .search-section {
        @apply w-full ml-16 z-50 flex h-full;
        padding-right: 12px;
    }
}

@media (max-width: 767px) {
    .nav-right-section {
        z-index: 50;
    }
}

@media (min-width: 912px) and (max-width: 1100px) {
    .nav-section {
        max-width: 135px;
        .logo svg {
            height: 2rem;
        }
    }

    .nav-right-section {
        max-width: 100px;
        z-index: 56;
    }
}

@media (min-width: 912px) {
    .nav-right-section {
        @apply order-3;
        min-width: 118px;
    }
}
