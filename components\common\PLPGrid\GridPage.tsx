import React, { Dispatch, Fragment, SetStateAction, useMemo } from 'react'
import { getKeyString } from '@corsairitshopify/pylot-utils'
import { ProductInterface } from '@pylot-data/fwrdschema'

type GridPageInput<T> = {
    items: T[]
    ProductTile: React.ComponentType<T>
    getMarketing: (idx: number) => JSX.Element | null
    startingIndex: number
    hideMarketingBlock?: boolean
    compareItems?: ProductInterface[]
    setCompareVisibility?: Dispatch<SetStateAction<boolean>>
    setCompareItems?: Dispatch<SetStateAction<any>>
}

const GridPage = ({
    items,
    ProductTile,
    getMarketing,
    compareItems,
    startingIndex,
    hideMarketingBlock = false,
    setCompareItems,
    setCompareVisibility
}: GridPageInput<any>): JSX.Element => {
    const keyString = useMemo(() => getKeyString(items), [items])
    const itemElements = useMemo(
        () =>
            items.map((item, idx) => (
                // This must be a fragment, not a div
                <Fragment key={idx}>
                    <ProductTile
                        setCompareItems={setCompareItems}
                        compareItems={compareItems}
                        product={item}
                        setCompareVisibility={setCompareVisibility}
                    />
                    {!hideMarketingBlock && getMarketing(startingIndex + idx)}
                </Fragment>
            )),
        [keyString, startingIndex, hideMarketingBlock, compareItems]
    )
    return <>{itemElements}</>
}

export default GridPage
