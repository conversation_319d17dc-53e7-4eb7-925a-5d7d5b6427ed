import React, { FC, useState, useMemo, useCallback } from 'react'
import { AccordionInfoSectionProps } from './types'
import cn from 'classnames'
import Accordion from '../Accordion/Accordion'
import s from './AccordionInfoSection.module.scss'
import { IconClose, IconOpen } from '../ProductCallouts'
import decode from '../../../lib/utils/htmlencoder'

const AccordionInfoSection: FC<AccordionInfoSectionProps> = ({
    content
}: AccordionInfoSectionProps) => {
    // Memoize the content styles to prevent unnecessary recalculations
    const contentStyles = useMemo(
        () => ({
            ...(content?.cloudinaryBackgroundImage && {
                backgroundImage: `url(${content.cloudinaryBackgroundImage[0]?.secure_url})`
            }),
            ...(content?.backgroundColor && {
                background: content.backgroundColor
            }),
            container: {
                ...(content?.maxWidth && {
                    maxWidth: content.maxWidth
                })
            },
            text: {
                heading: {
                    ...(content?.headingColor && {
                        color: content.headingColor
                    })
                },
                description: {
                    ...(content?.textColor && {
                        color: content.textColor
                    })
                }
            }
        }),
        [content]
    )

    const [expands, setExpands] = useState<string[]>([])

    // Generate a unique ID for each accordion item
    const getAccordionId = useCallback((heading: string, idx: number) => {
        return `accordion-${heading.replace(/\s+/g, '-').toLowerCase()}-${idx}`
    }, [])

    // Optimize the handleChange function with useCallback
    const handleChange = useCallback(
        (panelTitle: string, idx: number) => (
            _event: React.MouseEventHandler<HTMLButtonElement>,
            isExpanded: boolean
        ) => {
            const accordionId = getAccordionId(panelTitle, idx)
            if (isExpanded) {
                setExpands((prev) => [...prev, accordionId])
            } else {
                setExpands((prev) => prev.filter((el) => el !== accordionId))
            }
        },
        [getAccordionId]
    )

    // Sanitize the description HTML
    const sanitizedDescription = useMemo(() => {
        if (!content?.description) return ''
        return decode(content.description.replace(/\n/g, '<br/>'))
    }, [content?.description])

    if (!content) {
        return null
    }

    return (
        <section style={contentStyles}>
            <div
                className={cn(
                    'mx-auto py-13',
                    s['accordion-info-section'],
                    'accordion-info-section'
                )}
                style={contentStyles.container}
            >
                {content.heading && (
                    <h2
                        style={contentStyles.text.heading}
                        className="text-3xl md-max:text-2xl"
                    >
                        {content.heading}
                    </h2>
                )}
                {content.description && (
                    <p
                        className="my-5 text-base font-normal font-sofiaSans md-max:text-sm"
                        style={contentStyles.text.description}
                        dangerouslySetInnerHTML={{
                            __html: sanitizedDescription
                        }}
                    />
                )}
                <div>
                    {content.items?.map((item, idx) => {
                        const accordionId = getAccordionId(item.heading, idx)
                        return (
                            <Accordion
                                key={`accordion-item-${accordionId}`}
                                classNames={{
                                    root: s['dark-accordion'],
                                    iconAccordion: s['icon-accordion'],
                                    iconOpen: s['icon-open']
                                }}
                                isOpen={expands.includes(accordionId)}
                                title={item.heading}
                                onChange={handleChange(item.heading, idx)}
                                iconOpen={
                                    <IconOpen
                                        classNames={{
                                            root: s['icon-container'],
                                            icon: s['icon-svg']
                                        }}
                                    />
                                }
                            >
                                <div>
                                    <p className="mt-4 font-normal text-md font-sofiaSans">
                                        {item.text}
                                    </p>
                                </div>
                            </Accordion>
                        )
                    })}
                </div>
            </div>
        </section>
    )
}

export default AccordionInfoSection
