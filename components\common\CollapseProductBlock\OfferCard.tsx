import { ProductPrice } from '@components/common/ProductPrice/ProductPrice'
import { Badge } from '@components/common/ProductTile/Badge'
import { ModelTypeEnum } from '@config/base'
import { useStoreConfig } from '@config/hooks/useStoreConfig'
import Image from '@corsairitshopify/corsair-image/src/Image'
import { useUser } from '@corsairitshopify/pylot-auth-manager'
import { Button } from '@corsairitshopify/pylot-ui'
import { useBuildProductUrl } from '@lib/hooks/useBuildProductUrl'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { ClickFrom } from '@pylot-data/enums/ClickFromEnum.d'
import { PageType } from '@pylot-data/enums/VisibilityEnum'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { ButtonLabel } from '@pylot-data/hooks/product/use-product-ui'
import { useVisibility } from '@pylot-data/hooks/use-visibility'
import { ProductInterface } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { DEFAULT_FORMAT_DATE_BY_REGION, isTransactionalView } from 'helpers'
import { handleSidebarCartFocus } from 'helpers/AdaHelper'
import { processBackorderData } from 'helpers/backorderHelper'
import { convertImageFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { FC, useMemo } from 'react'
import { ICollapseProductBlockProps } from './CollapseProductBlock'
import s from './CollapseProductBlock.module.scss'

type OfferCardProps = {
    product: ProductInterface
    content: ICollapseProductBlockProps
    addProductHandler: (product: ProductInterface, clickFrom: string) => void
    className?: { readonly [key: string]: string }
    mouseEnterImage?: () => void
    mouseLeaveImage?: () => void
}

const OfferCard: FC<OfferCardProps> = ({
    product,
    content,
    addProductHandler,
    className,
    mouseEnterImage,
    mouseLeaveImage
}) => {
    const { isAdding } = useAddToCart()
    const { t } = useTranslation('common')
    const router = useRouter()
    const { locale } = router
    const { customer } = useUser()
    const isMobile = useMediaQuery('(max-width: 1024px)')
    const isTransactional = useMemo(
        () => isTransactionalView('addToCart', locale, customer),
        [locale, customer]
    )
    const {
        base: { dateFormatByRegion }
    } = useStoreConfig()
    const productUrl = useBuildProductUrl({
        page: ModelTypeEnum.PRODUCT,
        product
    })

    const { stock_status } = useVisibility({
        page_type: PageType.PDP,
        product
    })

    const { backorder, backend_status, bundle_products } = product
    const { backorder_date_range, translation_entry } = processBackorderData(
        dateFormatByRegion ?? DEFAULT_FORMAT_DATE_BY_REGION,
        backorder,
        backend_status
    )
    const { plp_cta } = useVisibility({
        page_type: PageType.TLC,
        product
    })

    const showATC = useMemo(() => {
        if (
            plp_cta.visible &&
            plp_cta.display_text === ButtonLabel.ADD_TO_CART &&
            isTransactional
        )
            return true

        return false
    }, [isTransactional, isAdding, plp_cta])

    const ctaLabel = useMemo(() => {
        if (isAdding) return t('Adding...')
        return showATC
            ? t(ButtonLabel.ADD_TO_CART)
            : t(ButtonLabel.LEARN_MORE_CAPITAL_CASE)
    }, [isAdding, showATC])

    const handleOnCTAClick = () => {
        if (showATC) {
            return addProductHandler(product, ClickFrom.OTHER)
        }
        return router.push(productUrl)
    }
    if (!product) return null

    const handleOnKeyPress = () => {
        handleOnCTAClick()
        handleSidebarCartFocus(
            `#offer-card-item-atc-btn-${product.sku?.replace(
                /[^a-zA-Z0-9_-]+/g,
                ''
            )}`
        )
    }

    //fallback if image node not present for recently viewed old cache products
    const pickImageUrl =
        product?.image?.url ||
        product?.media_gallery?.[0]?.url ||
        product?.small_image?.url

    // in case image has standard cloudinary format replace with the correct format for this page
    const imageUrl =
        pickImageUrl && convertImageFormat(pickImageUrl, 'c_pad,q_auto', 'webp')

    // {productItemsLimitedLength.map((product: ProductInterface) => {
    return (
        <div
            key={product?.uid}
            className={cn(
                s['collapse-product-block__item'],
                className?.['no-slide-wrapper']
            )}
        >
            <Link href={productUrl}>
                <a
                    rel="noreferrer"
                    className="corsair-links"
                    aria-label={product?.name || ''}
                >
                    <div
                        className={`${s['collapse-product-block__item-image']} relative flex justify-center`}
                    >
                        {(product?.badge || product?.custom_badge) &&
                            content?.showBadge && (
                                <Badge
                                    className={
                                        s[
                                            'collapse-product-block__item-image-badge'
                                        ]
                                    }
                                    label={product?.badge || null}
                                    customBadge={product?.custom_badge}
                                />
                            )}
                        <Image
                            className={`${s['card-image']} object-contain w-full h-full`}
                            src={
                                imageUrl ||
                                '/images/default-product-image.png?w=1920&q=75'
                            }
                            alt={product?.image?.label || ''}
                            width={isMobile ? 300 : 400}
                            height={isMobile ? 275 : 368}
                            onMouseEnter={mouseEnterImage}
                            onMouseLeave={mouseLeaveImage}
                        />
                    </div>
                </a>
            </Link>

            <div className={s['content-wrapper']}>
                <Link href={productUrl}>
                    <a
                        rel="noreferrer"
                        className="corsair-links"
                        aria-label={product?.name || ''}
                    >
                        <div
                            className={`${cn(
                                s['collapse-product-block__item-title'],
                                className?.['no-slide-wrapper-title']
                            )}`}
                        >
                            {product?.name}
                        </div>
                    </a>
                </Link>
                {isTransactional ? (
                    <div className={s['product-price-wrapper']}>
                        <ProductPrice
                            className={`text-sm text-black ${s['collapse-product-block__price']}`}
                            priceRange={product?.price_range}
                            showDiscount
                        />
                    </div>
                ) : null}
                <div>
                    <Button
                        aria-label={ctaLabel}
                        type="button"
                        className={cn(
                            'corsair-links',
                            s['collapse-product-block__add-to-cart-button']
                        )}
                        onClick={handleOnCTAClick}
                        onKeyPress={(e: React.KeyboardEvent<HTMLElement>) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault()
                                handleOnKeyPress()
                            }
                        }}
                        loading={false}
                        id={`offer-card-item-atc-btn-${product.sku?.replace(
                            /[^a-zA-Z0-9_-]+/g,
                            ''
                        )}`}
                    >
                        <span
                            className={
                                s['collapse-product-block__icon-wrapper']
                            }
                        >
                            <span>{ctaLabel}</span>
                        </span>
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default OfferCard
