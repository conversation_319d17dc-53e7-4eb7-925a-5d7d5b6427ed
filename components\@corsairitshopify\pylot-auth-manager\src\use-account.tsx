import { SwrUseQueryOutput, useQuery } from '@pylot-data/hooks/use-query'
import { CUSTOMER_QUERY } from './graphql/customerQuery'
import type { RequestInit } from '@vercel/fetch'
import type { SWRConfiguration } from 'swr'
import type { Customer } from '@pylot-data/fwrdschema'
import { graphqlFetch } from '@pylot-data/graphqlFetch'
import { useRouter } from 'next/router'

export const useAccount = (
    swrOptions?: SWRConfiguration,
    fetchOptions?: RequestInit
): SwrUseQueryOutput<{ customer?: Customer }> => {
    const isClient = typeof window !== 'undefined'
    const pylotToken = isClient && window.localStorage.getItem('pylot_token')

    const { locale } = useRouter()

    // We need this override so that we can get the token from local storage AGAIN every time this runs, on-demand
    // @ts-ignore
    const customerFetcher: typeof graphqlFetch = (
        query: string,
        nullVariables: null,
        fetchOptions: RequestInit
    ) => {
        const isClient = typeof window !== 'undefined'
        const isSignedIn =
            isClient && window.localStorage.getItem('pylot_token')
        if (!isSignedIn) return null
        return graphqlFetch({
            // @ts-ignore
            query: query.query,
            variables: null,
            fetchOptions,
            locale
        })
    }

    return useQuery<null, { customer?: Customer }>(
        pylotToken ? CUSTOMER_QUERY : null,
        // no variables for this query because identifier comes from the auth cookie or header (see PYLOT-101)
        null,
        swrOptions,
        fetchOptions,
        {
            overrideFetcher: customerFetcher
        }
    )
}
