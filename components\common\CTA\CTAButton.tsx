import {
    btnType,
    CTAButtonType
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import React, { useState, useMemo, useCallback, memo } from 'react'
import CorsairModal from '../CorsairModal/CorsairModal'
import CorsairVideo from '../CorsairVideo/CorsairVideo'
import { HtmlContentPage } from '../HtmlContentPage'
import { IHTMLContentPage } from '../types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import decode from '../../../lib/utils/htmlencoder'
import s from './CTAButton.module.scss'

/**
 * Maps button types to their corresponding CSS classes
 * @param type - The button type variant
 * @returns CSS class name for the button type
 */
const getButtonTypeClass = (type?: btnType): string => {
    if (!type) return ''

    switch (type) {
        case 'primary':
            return s['button-primary']
        case 'secondary':
            return s['button-secondary']
        case 'link':
            return s['button-link']
        default:
            return ''
    }
}

/**
 * Sanitizes HTML content to prevent XSS attacks
 * @param html - Raw HTML string
 * @returns Sanitized HTML string
 */
const sanitizeHTML = (html: string): string => {
    if (!html) return ''
    return decode(html)
}

// Enhanced className structure for better customization
interface CTAClassNames {
    /** Root container class */
    root?: string
    /** Text content class */
    text?: string
    /** Icon container class */
    icon?: string
    /** Button wrapper class */
    wrapper?: string
}

/**
 * Props for the common button content component
 */
interface CommonButtonContentProps {
    /** Text to display in the button */
    displayText: string
    /** Custom text color */
    textColor?: string
    /** Additional content to render */
    children?: React.ReactNode
}

/**
 * Base props for all button variants
 */
interface CommonButtonProps extends CommonButtonContentProps {
    /** Button style variant */
    buttonType?: btnType
    /** Custom CSS classes */
    className?: CTAClassNames
    /** Inline styles */
    style?: React.CSSProperties
    /** Click handler */
    onClick?: () => void
    /** Accessibility label */
    'aria-label'?: string
    /** Whether button is disabled */
    disabled?: boolean
    /** Loading state */
    loading?: boolean
}

/**
 * Props for link button component
 */
interface LinkButtonProps extends CommonButtonProps {
    /** Destination URL */
    url: string
    /** Whether to open in new tab */
    openInANewTab?: boolean
    /** Optional icon images */
    icon?: CloudinaryImage[]
}

/**
 * Props for modal button component
 */
interface ModalButtonProps extends CommonButtonProps {
    /** Video URL for popup */
    popupVideoUrl?: string
    /** Video transcript content */
    videoTranscript?: string
    /** HTML content for popup */
    popupHtmlContent?: IHTMLContentPage
}

/**
 * Common button content component with memoization for performance
 * Renders the shared content structure for all button variants
 */
const CommonButtonContent = memo<CommonButtonContentProps>(
    ({ displayText, textColor, children }) => {
        const { t } = useTranslation(['common'])

        const contentStyle = useMemo(
            () => ({
                color: textColor || 'inherit'
            }),
            [textColor]
        )

        return (
            <div className={cn(s['component-cta'])} style={contentStyle}>
                <span>{t(displayText)}</span>
                {children}
            </div>
        )
    }
)

// Add display name for debugging
CommonButtonContent.displayName = 'CommonButtonContent'

/**
 * Common button component with enhanced accessibility and performance
 * Supports all button variants with consistent styling and behavior
 */
const CommonButton = memo<CommonButtonProps>(
    ({
        displayText,
        textColor,
        buttonType,
        className,
        style,
        children,
        onClick,
        disabled = false,
        loading = false,
        'aria-label': ariaLabel
    }) => {
        const buttonTypeClass = getButtonTypeClass(buttonType)

        const handleClick = useCallback(() => {
            if (!disabled && !loading && onClick) {
                onClick()
            }
        }, [disabled, loading, onClick])

        const buttonClassName = useMemo(
            () =>
                cn(
                    'flex items-center',
                    s['cta-button-root'],
                    className?.root,
                    buttonTypeClass,
                    {
                        [s['button-loading']]: loading
                    }
                ),
            [className?.root, buttonTypeClass, loading]
        )

        const mergedStyle = useMemo(
            () =>
                ({
                    ...style,
                    '--button-text-color': textColor
                } as React.CSSProperties),
            [style, textColor]
        )

        return (
            <button
                type="button"
                onClick={handleClick}
                style={mergedStyle}
                className={buttonClassName}
                aria-label={ariaLabel || displayText}
                aria-disabled={disabled}
                disabled={disabled || loading}
            >
                <CommonButtonContent
                    displayText={displayText}
                    textColor={textColor}
                >
                    {children}
                </CommonButtonContent>
            </button>
        )
    }
)

// Add display name for debugging
CommonButton.displayName = 'CommonButton'

/**
 * Validates if a URL is safe to use
 * @param url - URL to validate
 * @returns boolean indicating if URL is valid
 */
const isValidURL = (url: string): boolean => {
    if (!url) return false
    try {
        const urlObj = new URL(url, window.location.origin)
        return ['http:', 'https:', 'mailto:', 'tel:'].includes(urlObj.protocol)
    } catch {
        // Relative URLs are also valid
        return url.startsWith('/') || url.startsWith('#')
    }
}

/**
 * Link button component with enhanced security and accessibility
 * Renders as a Next.js Link with proper SEO and security attributes
 */
const LinkButton = memo<LinkButtonProps>(
    ({
        url,
        openInANewTab = false,
        icon,
        className,
        style,
        buttonType,
        children,
        disabled = false,
        loading = false,
        ...commonButtonProps
    }) => {
        const buttonTypeClass = getButtonTypeClass(buttonType)

        const linkClassName = useMemo(
            () =>
                cn(
                    className?.root,
                    s['component-cta-wrapper'],
                    s['cta-button-root'],
                    'corsair-links',
                    buttonTypeClass,
                    {
                        [s['button-loading']]: loading
                    }
                ),
            [className?.root, buttonTypeClass, loading]
        )

        const mergedStyle = useMemo(
            () =>
                ({
                    ...style,
                    '--button-text-color': commonButtonProps.textColor,
                    pointerEvents: disabled
                        ? ('none' as const)
                        : ('auto' as const),
                    opacity: disabled ? 0.5 : 1
                } as React.CSSProperties),
            [style, commonButtonProps.textColor, disabled]
        )

        // Don't render if URL is invalid
        if (!isValidURL(url)) {
            console.warn(`CTAButton: Invalid URL provided: ${url}`)
            return null
        }

        const linkContent = (
            <CommonButtonContent
                displayText={commonButtonProps.displayText}
                textColor={commonButtonProps.textColor}
            >
                {children}
            </CommonButtonContent>
        )

        return (
            <div className={className?.wrapper}>
                <Link href={url}>
                    <a
                        target={openInANewTab ? '_blank' : undefined}
                        rel={openInANewTab ? 'noreferrer noopener' : undefined}
                        className={linkClassName}
                        style={mergedStyle}
                        aria-label={
                            commonButtonProps['aria-label'] ||
                            commonButtonProps.displayText
                        }
                        aria-disabled={disabled}
                        tabIndex={disabled ? -1 : 0}
                    >
                        {linkContent}
                    </a>
                </Link>
                {icon && icon.length > 0 && (
                    <span className={cn(className?.icon, s['button-icon'])}>
                        {icon.map((img, index) => (
                            <img
                                key={index}
                                src={img.secure_url}
                                alt=""
                                role="presentation"
                                loading="lazy"
                            />
                        ))}
                    </span>
                )}
            </div>
        )
    }
)

// Add display name for debugging
LinkButton.displayName = 'LinkButton'

/**
 * Modal button component with enhanced security and accessibility
 * Supports video and HTML content with proper sanitization
 */
const ModalButton = memo<ModalButtonProps>((props) => {
    const { t } = useTranslation(['common'])
    const [openModal, setOpenModal] = useState(false)

    const {
        popupVideoUrl,
        videoTranscript,
        popupHtmlContent,
        ...commonButtonProps
    } = props

    // Memoize computed values for performance
    const isYoutubeVideo = useMemo(
        () =>
            popupVideoUrl?.includes('youtube.com') ||
            popupVideoUrl?.includes('youtu.be'),
        [popupVideoUrl]
    )

    const isHTMLContent = useMemo(() => Boolean(popupHtmlContent), [
        popupHtmlContent
    ])

    const sanitizedTranscript = useMemo(
        () => (videoTranscript ? sanitizeHTML(videoTranscript) : ''),
        [videoTranscript]
    )

    // Memoized event handlers
    const handleOpenModal = useCallback(() => {
        setOpenModal(true)
    }, [])

    const handleCloseModal = useCallback(() => {
        setOpenModal(false)
    }, [])

    // Enhanced YouTube URL validation and security
    const secureVideoUrl = useMemo(() => {
        if (!popupVideoUrl) return ''

        if (isYoutubeVideo) {
            try {
                const url = new URL(popupVideoUrl)
                // Ensure HTTPS for YouTube
                if (url.protocol !== 'https:') {
                    url.protocol = 'https:'
                }
                return url.toString()
            } catch {
                console.warn('CTAButton: Invalid YouTube URL provided')
                return ''
            }
        }

        return popupVideoUrl
    }, [popupVideoUrl, isYoutubeVideo])

    return (
        <div>
            <CommonButton
                {...commonButtonProps}
                onClick={handleOpenModal}
                aria-haspopup="dialog"
                aria-expanded={openModal}
            />
            <CorsairModal
                isOpen={openModal}
                toggleModal={handleCloseModal}
                aria-label={`${commonButtonProps.displayText} modal`}
            >
                {isHTMLContent && popupHtmlContent && (
                    <HtmlContentPage content={popupHtmlContent} />
                )}

                {secureVideoUrl && isYoutubeVideo ? (
                    <iframe
                        width="100%"
                        height="100%"
                        src={secureVideoUrl}
                        title={`${commonButtonProps.displayText} - YouTube video player`}
                        style={{ border: 'none', minHeight: '400px' }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowFullScreen
                        loading="lazy"
                    />
                ) : secureVideoUrl ? (
                    <>
                        <CorsairVideo
                            secure_url={secureVideoUrl}
                            autoPlay={false}
                            controls
                            aria-label={`${commonButtonProps.displayText} video`}
                        />
                        {sanitizedTranscript && (
                            <details
                                className={s['video-modal-button--transcript']}
                                aria-label="Video transcript"
                            >
                                <summary
                                    className={
                                        s[
                                            'video-modal-button--transcript-button'
                                        ]
                                    }
                                    aria-expanded="false"
                                >
                                    {t('Transcript')}
                                    <span
                                        className={
                                            s[
                                                'video-modal-button--transcript-toggle-icon'
                                            ]
                                        }
                                        aria-hidden="true"
                                    >
                                        <ChevronRightIcon />
                                    </span>
                                </summary>
                                <div
                                    className={
                                        s['video-modal-button--transcript-text']
                                    }
                                    dangerouslySetInnerHTML={{
                                        __html: sanitizedTranscript
                                    }}
                                    role="region"
                                    aria-label="Video transcript content"
                                />
                            </details>
                        )}
                    </>
                ) : null}
            </CorsairModal>
        </div>
    )
})

// Add display name for debugging
ModalButton.displayName = 'ModalButton'

/**
 * Enhanced ChevronRight icon component with accessibility
 */
const ChevronRightIcon = memo(() => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 320 512"
        width={20}
        height={20}
        fill="currentColor"
        aria-hidden="true"
        role="presentation"
    >
        <path d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z" />
    </svg>
))

ChevronRightIcon.displayName = 'ChevronRightIcon'

/**
 * Generates CSS custom properties for theming
 * @param cta - CTA button props
 * @returns CSS properties object
 */
const getThemeStyle = (cta: CTAButtonType): React.CSSProperties => {
    return {
        '--button-color': cta.textColor,
        '--button-bg-color':
            cta.buttonType === 'primary' ? '#ECE81A' : 'transparent',
        '--button-border-color': cta.textColor || 'currentColor'
    } as React.CSSProperties
}

/**
 * Main CTA Button component with enhanced functionality and performance
 * Automatically chooses between LinkButton and ModalButton based on props
 */
const CTAButton = memo<CTAButtonType>((props) => {
    const {
        className,
        children,
        style,
        displayText,
        url,
        openInANewTab = false,
        openInPopup = false,
        popupVideoUrl,
        videoTranscript,
        popupHtmlContent,
        icon,
        buttonType,
        textColor,
        meta
    } = props

    // Compute styles before any early returns to avoid hook order issues
    const themeStyle = useMemo(() => getThemeStyle(props), [props])
    const mergedStyle = useMemo(
        () => ({
            ...themeStyle,
            ...style
        }),
        [themeStyle, style]
    )

    // Early return for invalid content type
    if (meta?.contentType !== 'componentCta') {
        console.warn('CTAButton: Invalid content type. Expected "componentCta"')
        return null
    }

    // Validate required props
    if (!displayText) {
        console.warn('CTAButton: displayText is required')
        return null
    }

    if (!openInPopup && !url) {
        console.warn('CTAButton: url is required when not using popup mode')
        return null
    }

    // Use ModalButton for popup functionality
    if (openInPopup) {
        return (
            <ModalButton
                displayText={displayText}
                textColor={textColor}
                buttonType={buttonType}
                className={className}
                style={mergedStyle}
                popupVideoUrl={popupVideoUrl}
                videoTranscript={videoTranscript}
                popupHtmlContent={popupHtmlContent}
            />
        )
    }

    // Use LinkButton for regular link functionality
    return (
        <div style={mergedStyle}>
            <LinkButton
                displayText={displayText}
                textColor={textColor}
                buttonType={buttonType}
                className={className}
                style={style}
                url={url}
                openInANewTab={openInANewTab}
                icon={icon}
            >
                {children}
            </LinkButton>
        </div>
    )
})

// Add display name for debugging
CTAButton.displayName = 'CTAButton'

// Export the common components for reuse
export {
    CommonButtonContent,
    CommonButton,
    LinkButton,
    ModalButton,
    ChevronRightIcon
}

// Export enhanced type definitions
export type {
    CommonButtonContentProps,
    CommonButtonProps,
    LinkButtonProps,
    ModalButtonProps,
    CTAClassNames
}

// Export utility functions for external use
export { getButtonTypeClass, sanitizeHTML, isValidURL }

export default CTAButton
