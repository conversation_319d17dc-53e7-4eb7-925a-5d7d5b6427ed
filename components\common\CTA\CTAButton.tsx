import { CTAButtonType } from '@pylot-data/hooks/contentful/use-content-json'
import React, { useMemo, memo } from 'react'
import { LinkButton, ModalButton } from './CommonCTA'

// Main CTA Button component
const CTAButton = memo<CTAButtonType>((props) => {
    const {
        className,
        children,
        style,
        displayText,
        url,
        openInANewTab = false,
        openInPopup = false,
        popupVideoUrl,
        videoTranscript,
        popupHtmlContent,
        icon,
        iconPosition,
        buttonType,
        textColor,
        showRightArrow = false,
        meta
    } = props

    const themeStyle = useMemo(
        () => ({
            '--button-color': textColor
        }),
        [textColor]
    )

    const mergedStyle = useMemo(() => ({ ...themeStyle, ...style }), [
        themeStyle,
        style
    ])

    // Validation
    if (meta?.contentType !== 'componentCta') return null
    if (!displayText) return null
    if (!openInPopup && !url) return null

    // Use ModalButton for popup functionality
    if (openInPopup) {
        return (
            <ModalButton
                displayText={displayText}
                textColor={textColor}
                buttonType={buttonType}
                className={className}
                style={mergedStyle}
                popupVideoUrl={popupVideoUrl}
                videoTranscript={videoTranscript}
                popupHtmlContent={popupHtmlContent}
                showRightArrow={showRightArrow}
                iconPosition={iconPosition}
                icon={icon}
            />
        )
    }

    // Use LinkButton for regular link functionality
    return (
        <LinkButton
            displayText={displayText}
            textColor={textColor}
            buttonType={buttonType}
            className={className}
            style={{ ...mergedStyle, ...style }}
            url={url}
            openInANewTab={openInANewTab}
            icon={icon}
            showRightArrow={showRightArrow}
            iconPosition={iconPosition}
        >
            {children}
        </LinkButton>
    )
})
CTAButton.displayName = 'CTAButton'

// Exports
export { LinkButton, ModalButton }

export default CTAButton
