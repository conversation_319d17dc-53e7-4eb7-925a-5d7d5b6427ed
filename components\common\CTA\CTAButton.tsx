import {
    btnType,
    CTAButtonType
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import React, { FC, useState } from 'react'
import CorsairModal from '../CorsairModal/CorsairModal'
import CorsairVideo from '../CorsairVideo/CorsairVideo'
import { HtmlContentPage } from '../HtmlContentPage'
import s from '../BlockTwoTile/BlockTwoTile.module.scss'
import { IHTMLContentPage } from '../types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

// Utility function for button type classes
const getButtonTypeClass = (type?: btnType) => {
    if (!type) return ''

    switch (type) {
        case 'primary':
            return 'scuf-button-primary'
        case 'secondary':
            return 'scuf-button-secondary'
        case 'link':
            return 'scuf-button-link'
        default:
            return ''
    }
}

// Common Button Content Props Interface
interface CommonButtonContentProps {
    displayText: string
    textColor?: string
    children?: React.ReactNode
}

// Common Button Props Interface
interface CommonButtonProps extends CommonButtonContentProps {
    buttonType?: btnType
    className?: {
        root?: string
        text?: string
        icon?: string
    }
    style?: React.CSSProperties
    onClick?: () => void
    'aria-label'?: string
}

// Link Button Props Interface
interface LinkButtonProps extends CommonButtonProps {
    url: string
    openInANewTab?: boolean
    icon?: CloudinaryImage[]
}

// Modal Button Props Interface
interface ModalButtonProps extends CommonButtonProps {
    popupVideoUrl?: string
    videoTranscript?: string
    popupHtmlContent?: IHTMLContentPage
}

// Common Button Content Component (shared content structure)
const CommonButtonContent: FC<CommonButtonContentProps> = ({
    displayText,
    textColor,
    children
}) => {
    const { t } = useTranslation(['common'])

    return (
        <div className={cn(s['component-cta'])} style={{ color: textColor }}>
            <span>{t(displayText)}</span>
            {children}
        </div>
    )
}

// Common Button Component
const CommonButton: FC<CommonButtonProps> = ({
    displayText,
    textColor,
    buttonType,
    className,
    style,
    children,
    onClick,
    'aria-label': ariaLabel
}) => {
    const buttonTypeClass = getButtonTypeClass(buttonType)

    return (
        <button
            onClick={onClick}
            style={style}
            className={cn(
                'flex items-center',
                className?.root,
                buttonTypeClass
            )}
            aria-label={ariaLabel || displayText}
        >
            <CommonButtonContent
                displayText={displayText}
                textColor={textColor}
            >
                {children}
            </CommonButtonContent>
        </button>
    )
}

// Link Button Component
const LinkButton: FC<LinkButtonProps> = ({
    url,
    openInANewTab,
    icon,
    className,
    style,
    buttonType,
    children,
    ...commonButtonProps
}) => {
    const buttonTypeClass = getButtonTypeClass(buttonType)

    return (
        <div>
            <Link href={url}>
                <a
                    target={openInANewTab ? '_blank' : ''}
                    rel="noreferrer noopener"
                    className={cn(
                        className?.root,
                        s['component-cta-wrapper'],
                        'corsair-links',
                        buttonTypeClass
                    )}
                    style={style}
                >
                    <CommonButtonContent
                        displayText={commonButtonProps.displayText}
                        textColor={commonButtonProps.textColor}
                    >
                        {children}
                    </CommonButtonContent>
                </a>
            </Link>
            {icon && <span className={className?.icon}>{icon}</span>}
        </div>
    )
}

// Modal Button Component
const ModalButton: FC<ModalButtonProps> = (props) => {
    const { t } = useTranslation(['common'])
    const [openModal, setOpenModal] = useState(false)

    const {
        popupVideoUrl,
        videoTranscript,
        popupHtmlContent,
        ...commonButtonProps
    } = props

    const isYoutubeVideo = popupVideoUrl?.includes('youtube.com')
    const isHTMLContent = popupHtmlContent

    return (
        <div>
            <CommonButton
                {...commonButtonProps}
                onClick={() => setOpenModal(true)}
            />
            <CorsairModal isOpen={openModal} toggleModal={setOpenModal}>
                {isHTMLContent && (
                    <HtmlContentPage content={popupHtmlContent!} />
                )}
                {isYoutubeVideo ? (
                    <iframe
                        width="100%"
                        height="100%"
                        src={popupVideoUrl}
                        title="YouTube video player"
                        style={{ border: 'none' }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowFullScreen
                    />
                ) : (
                    <>
                        <CorsairVideo
                            secure_url={popupVideoUrl!}
                            autoPlay={false}
                            controls
                        />
                        {!!videoTranscript && (
                            <details
                                className={s['video-modal-button--transcript']}
                            >
                                <summary
                                    className={
                                        s[
                                            'video-modal-button--transcript-button'
                                        ]
                                    }
                                >
                                    {t('Transcript')}
                                    <span
                                        className={
                                            s[
                                                'video-modal-button--transcript-toggle-icon'
                                            ]
                                        }
                                    >
                                        <ChevronRightIcon />
                                    </span>
                                </summary>
                                <div
                                    className={
                                        s['video-modal-button--transcript-text']
                                    }
                                    dangerouslySetInnerHTML={{
                                        __html: videoTranscript
                                    }}
                                />
                            </details>
                        )}
                    </>
                )}
            </CorsairModal>
        </div>
    )
}

const CTAButton: FC<CTAButtonType> = (props: CTAButtonType) => {
    const {
        className,
        children,
        style,
        displayText,
        url,
        openInANewTab,
        openInPopup,
        popupVideoUrl,
        videoTranscript,
        popupHtmlContent,
        icon,
        buttonType,
        textColor,
        meta
    } = props

    if (meta?.contentType !== 'componentCta') return null

    // Use ModalButton for popup functionality
    if (openInPopup) {
        return (
            <ModalButton
                displayText={displayText}
                textColor={textColor}
                buttonType={buttonType}
                className={className}
                style={style}
                popupVideoUrl={popupVideoUrl}
                videoTranscript={videoTranscript}
                popupHtmlContent={popupHtmlContent}
            />
        )
    }

    // Use LinkButton for regular link functionality
    return (
        <div style={getStyle(props)}>
            <LinkButton
                displayText={displayText}
                textColor={textColor}
                buttonType={buttonType}
                className={className}
                style={style}
                url={url}
                openInANewTab={openInANewTab}
                icon={icon}
            >
                {children}
            </LinkButton>
        </div>
    )
}

const getStyle = (cta: CTAButtonType) => {
    return {
        '--button-color': cta.textColor
    } as React.CSSProperties
}

const ChevronRightIcon = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 320 512"
            width={20}
            height={20}
            fill="currentColor"
        >
            <path d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z" />
        </svg>
    )
}

// Export the common components for reuse
export { CommonButtonContent, CommonButton, LinkButton, ModalButton }
export type {
    CommonButtonContentProps,
    CommonButtonProps,
    LinkButtonProps,
    ModalButtonProps
}

export default CTAButton
