import React, { useEffect, useRef } from 'react'
import {
    clearAllBodyScrollLocks,
    disableBodyScroll,
    enableBodyScroll
} from 'body-scroll-lock'
import cn from 'classnames'
import { useRouter } from 'next/router'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'next-i18next'
import { createPortal } from 'preact/compat'

import {
    ClickOutside,
    getIsValidEmailAddress
} from '@corsairitshopify/pylot-utils'
import { getCookie } from '@corsairitshopify/corsair-cart-manager/src/utils/getCookies'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'

import { useDownloadDriverNewsletter } from './DownloadDriverNewsletterContext'
import {
    DownloadDriverNewsletterSignupFormProps,
    NewsletterSignupFormFields
} from './types'

import s from './DownloadDriverNewsletterSignupForm.module.scss'

const DownloadDriverNewsletterSignupForm: React.FC<DownloadDriverNewsletterSignupFormProps> = ({
    title,
    content,
    emailLabel,
    skipLabel
}) => {
    const { t } = useTranslation(['common'])
    const { locale } = useRouter()
    const getUserLanguage =
        typeof window !== 'undefined' ? window.navigator.language : ''
    const userLanguage = (getUserLanguage && getUserLanguage.includes('-')
        ? getUserLanguage.split('-')[0]
        : getUserLanguage
    ).toLowerCase()

    const containerRef = useRef<HTMLInputElement>(null)

    const {
        urlDriver,
        softwareVersion,
        displayForm,
        closeForm
    } = useDownloadDriverNewsletter()

    const {
        register,
        handleSubmit,
        reset,
        getValues,
        formState: { errors }
    } = useForm<NewsletterSignupFormFields>({
        mode: 'onSubmit',
        shouldFocusError: true
    })

    const closePopup = () => {
        closeForm()
        reset()
    }

    const onSubmit = async (data: NewsletterSignupFormFields) => {
        try {
            pushToDataLayer({
                event: 'downloadSoftware',
                downloadSoftware: {
                    customerEmail: data.email,
                    registrationLocation: 'SoftwareDownload',
                    marketingConsent: 'true',
                    softwareVersion: softwareVersion,
                    downloadUrl: urlDriver,
                    downloadStatus: 'success'
                },
                location: {
                    siteLanguage: locale?.split('-')[0].toLowerCase(),
                    siteCountry: locale?.split('-')[1].toLowerCase(),
                    userLanguage:
                        typeof window !== 'undefined' ? userLanguage : '',
                    userCountry: getCookie('Country_Code')?.toLowerCase()
                }
            })
            window.open(urlDriver, '_blank', 'noopener,noreferrer')
            closePopup()
        } catch (e) {
            console.log(e)
        }
    }

    const onError = async () => {
        try {
            pushToDataLayer({
                event: 'downloadSoftware',
                downloadSoftware: {
                    customerEmail: getValues('email'),
                    registrationLocation: document.title,
                    marketingConsent: 'true',
                    softwareVersion: softwareVersion,
                    downloadUrl: urlDriver,
                    downloadStatus: 'fail'
                },
                location: {
                    siteLanguage: locale?.split('-')[0].toLowerCase(),
                    siteCountry: locale?.split('-')[1].toLowerCase(),
                    userLanguage:
                        typeof window !== 'undefined' ? userLanguage : '',
                    userCountry: getCookie('Country_Code')?.toLowerCase()
                }
            })
        } catch (e) {
            console.log(e)
        }
    }

    useEffect(() => {
        if (containerRef.current != null) {
            containerRef.current.style.top = `${window.scrollY}px`
            if (displayForm) {
                disableBodyScroll(containerRef.current)
            } else {
                enableBodyScroll(containerRef.current)
            }
        }
        return () => {
            clearAllBodyScrollLocks()
        }
    }, [displayForm])

    return createPortal(
        <div
            data-show={displayForm}
            className={s['container']}
            role="dialog"
            ref={containerRef}
        >
            <ClickOutside active={displayForm} onClick={closePopup}>
                <div className={s['dialog']}>
                    <div className={s['form']}>
                        <div className={s['form-header']}>
                            <div className={s['form-title']} aria-label={title}>
                                {title}
                            </div>
                            <div
                                className={s['form-content']}
                                aria-label={content}
                            >
                                {content}
                            </div>
                            <button
                                className={s['form-close']}
                                onClick={closePopup}
                                onKeyDown={closePopup}
                            >
                                <span className="text-lg">&#10006;</span>
                            </button>
                        </div>
                        <div className={s['form-body']}>
                            <form onSubmit={handleSubmit(onSubmit, onError)}>
                                <div
                                    className={cn(s['form-field'], {
                                        [s['form-field-error']]: errors?.email
                                    })}
                                >
                                    <label
                                        className={s['form-label']}
                                        htmlFor="email"
                                    >
                                        {emailLabel} *
                                    </label>
                                    <input
                                        className={s['form-input']}
                                        type="email"
                                        {...register('email', {
                                            required: t(
                                                'This field is required'
                                            ) as string,
                                            validate: getIsValidEmailAddress
                                        })}
                                    />
                                    {errors?.email && (
                                        <div className={s['form-error']}>
                                            {errors?.email?.message}
                                        </div>
                                    )}
                                </div>
                                <button
                                    className={cn(
                                        'scuf-button-primary',
                                        s['form-submit-button']
                                    )}
                                    type="submit"
                                >
                                    {t('submit')}
                                </button>
                            </form>
                        </div>
                        <div className={s['form-footer']}>
                            <a
                                className={s['form-link']}
                                aria-label={skipLabel}
                                href={urlDriver}
                                target="_blank"
                                rel="noreferrer"
                            >
                                {skipLabel}
                            </a>
                        </div>
                    </div>
                </div>
            </ClickOutside>
        </div>,
        document.getElementById('__next')!
    )
}

export default DownloadDriverNewsletterSignupForm
