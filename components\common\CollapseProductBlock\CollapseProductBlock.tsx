/* eslint-disable jsx-a11y/img-redundant-alt */
import {
    SearchCriteria,
    SearchCriteriaFilterActions
} from '@corsairitshopify/corsair-filters-and-sort'
import { ToastType, useUI } from '@corsairitshopify/pylot-ui/context'
import { useCategoryFrom } from '@lib/hooks/useCategoryFrom'
import { getProductsBySKUs } from '@pylot-data/api/operations/get-products'
import { ProductStockStatus } from '@pylot-data/enums/ProductStockStatus.d'
import { SortEnum } from '@pylot-data/enums/SortEnum.d'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { processEnteredOptions } from '@pylot-data/hooks/cart/utils/processEnteredOptions'
import { useCategory } from '@pylot-data/hooks/category/use-category'
import { CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import { Categories, ProductInterface } from '@pylot-data/pylotschema'
import { useCartItemHelper } from 'helpers/cartItemHelper'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { useRouter } from 'next/router'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { BundleList } from './BundleList'
import cn from 'classnames'
import s from './CollapseProductBlock.module.scss'

const CrosssellCarousel = dynamic(() => import('../CrosssellCarousel'), {
    ssr: false
})

export interface ICollapseProductBlockProps {
    title: string
    titleColor?: string
    container?: string
    category: Categories
    ctaButton?: CTAType
    isBundle: boolean
    showSku: boolean
    showBadge: boolean
    gridDisplay: boolean
    productSKUs?: string[]
    showOutOfStockProducts?: boolean
    showItemCategory?: boolean
    variants?: string
}

interface CollapseProductBlockProps {
    content: ICollapseProductBlockProps
}

const TIMEOUT_AFTER_OPENING_SIDEBAR = 300

const MAX_PRODUCTS_SHOW = 2

const CollapseProductBlock: FC<CollapseProductBlockProps> = ({ content }) => {
    const [maxVisible, setMaxVisible] = useState(MAX_PRODUCTS_SHOW)
    const [isExpanded, setIsExpanded] = useState(false)
    const [loadingContentSKUs, setLoadingContentSKUs] = useState(false)
    const [productItems, setProductItems] = useState<any[]>([])
    const updateCartItemsLocalStorage = useCartItemHelper()

    const { addToCart } = useAddToCart()
    const { t } = useTranslation('common')
    const router = useRouter()
    const { locale } = router
    const { openSidebar, openCartToast, LoadingIndicator } = useUI()

    const searchCriteria: SearchCriteria[] = []

    const addProductHandler = useCallback(
        async (product: any, clickFrom: string) => {
            const variantProduct = product
            // 'variant' of a SimpleProduct is the same as 'product'
            updateCartItemsLocalStorage(product, clickFrom)
            const entered_options = processEnteredOptions(variantProduct)
            const response = await addToCart(
                [
                    {
                        sku: variantProduct.sku,
                        uid: variantProduct.uid,
                        selected_options: null,
                        entered_options,
                        quantity: 1,
                        max_quantity: variantProduct.max_allowed_quantity,
                        bundle_and_save_items:
                            variantProduct?.bundle_and_save_skus || [],
                        deliver_by: variantProduct?.deliverBy || 0,
                        excluded_delivery_dates: variantProduct?.excludedDeliveryDate
                            ? variantProduct?.excludedDeliveryDate!.join('-')
                            : ''
                    }
                ],
                [variantProduct]
            )
            // eslint-disable-next-line react-hooks/rules-of-hooks
            useCategoryFrom(variantProduct.sku, locale || '')

            const errorMessage =
                response?.errors?.[0]?.message ??
                response?.user_errors?.[0]?.message ??
                null
            const successMessage = t('cart|Item added to cart', {
                name: variantProduct.name
            })

            openSidebar()

            if (response) {
                // This function is disabled by CRS-4605: ClientUAT feedback
                // The type does not match any event in pylot-gtm so it's triggering empty event
                // dataLayerAction({
                //     type: 'ADD_TO_CART_WITH_QUERY_ID',
                //     data: { product: response.data.cart, queryID }
                // })
                setTimeout(
                    () =>
                        openCartToast(
                            errorMessage
                                ? errorMessage
                                : successMessage.replace('cart|', ''),
                            errorMessage ? ToastType.Warning : ToastType.Success
                        ),
                    TIMEOUT_AFTER_OPENING_SIDEBAR
                )
            }
        },
        [
            addToCart,
            updateCartItemsLocalStorage,
            openSidebar,
            openCartToast,
            t,
            locale
        ]
    )

    const getProductBySKUs = useCallback(
        async (skus: string[], locale: string) => {
            const products = await getProductsBySKUs(skus, locale)
            const productsData: any[] = []
            const showOOSProducts = content.showOutOfStockProducts

            products.forEach((productData) => {
                const index = productData?.productData?.findIndex(
                    (product) => product?.sku === productData.productSku
                )

                if (index != null && index !== -1) {
                    const productDatum = productData.productData[index]
                    if (
                        showOOSProducts ||
                        (productDatum?.stock_status ===
                            ProductStockStatus.InStock &&
                            productDatum.not_sellable === false)
                    ) {
                        productsData.push(productDatum)
                    }
                }
            })

            return productsData?.filter(Boolean)
        },
        [content.showOutOfStockProducts]
    )

    useEffect(() => {
        const productSkus: string[] = content?.productSKUs || []
        if (productSkus.length) {
            setLoadingContentSKUs(true)
            getProductBySKUs(productSkus, locale as string)
                .then((data) => {
                    let result: ProductInterface[] = []
                    if (data.length) {
                        result = [
                            ...data.sort(
                                (a, b) =>
                                    productSkus.indexOf(a.sku) -
                                    productSkus.indexOf(b.sku)
                            )
                        ]
                    }
                    setProductItems(result)
                })
                .finally(() => {
                    setLoadingContentSKUs(false)
                })
        } else {
            setProductItems([])
        }
    }, [locale, content?.title])

    const { productItems: categoryProductItems, isLoading } = useCategory(
        [],
        content?.category?.identifier,
        '',
        content?.category?.identifier,
        {
            pageSize: 10,
            filter: {},
            sort: { featured: SortEnum.Asc },
            searchCriteria: [
                ...searchCriteria,
                {
                    attribute_code: 'features_and_availability',
                    filter_action: SearchCriteriaFilterActions.EQ,
                    filter_value: 'stock_status:true'
                }
            ],
            currentPage: 1
        },
        { revalidateOnMount: true, revalidateOnFocus: false, initialSize: 2 },
        {}
    )

    // Memoize styles and class names to prevent unnecessary recalculations
    const titleStyles = useMemo(
        () => ({
            ...(content?.titleColor && { color: content.titleColor })
        }),
        [content?.titleColor]
    )

    const containerClassName = useMemo(() => {
        const containerMap: Record<string, string> = {
            'Scuf Container': 'scuf-container mx-auto'
        }

        return containerMap[content?.container || ''] || ''
    }, [content?.container])

    const variantClassName = useMemo(() => {
        const variantMap: Record<string, string> = {
            'Max Width Variant': 'max-width-variant'
        }
        return variantMap[content?.variants || ''] || ''
    }, [content?.variants])

    if (
        !productItems?.length &&
        !categoryProductItems.length &&
        !isLoading &&
        !loadingContentSKUs
    ) {
        return null
    }

    if (!content?.isBundle) {
        return (
            <CrosssellCarousel
                products={{
                    productData: productItems?.length
                        ? productItems
                        : categoryProductItems
                }}
                title={t(content.title)}
                titleStyles={titleStyles}
                showItemCategory={content?.showItemCategory}
                containerClassName={containerClassName}
                variantClassName={variantClassName}
            />
        )
    } else {
        return (
            <div
                className={cn(
                    s['collapse-product-block'],
                    containerClassName,
                    s[variantClassName]
                )}
            >
                <div className={s['collapse-product-block__header']}>
                    <h2
                        className={`text-black ${s['collapse-product-block__title']}`}
                    >
                        {t(content.title)}
                    </h2>
                </div>
                {loadingContentSKUs || isLoading ? (
                    <LoadingIndicator />
                ) : (
                    <div>
                        <BundleList
                            bundles={
                                productItems?.length
                                    ? productItems
                                    : categoryProductItems
                            }
                            content={content}
                            maxVisible={maxVisible}
                            addProductHandler={addProductHandler}
                        />
                        {productItems?.length > MAX_PRODUCTS_SHOW ? (
                            <button
                                className={
                                    s['collapse-product-block__see-more']
                                }
                                onClick={() => {
                                    setIsExpanded(!isExpanded)
                                    setMaxVisible(
                                        isExpanded
                                            ? MAX_PRODUCTS_SHOW
                                            : productItems.length
                                    )
                                }}
                            >
                                <span className="relative px-2 text-white bg-black z-2">
                                    {isExpanded ? t('See less') : t('See more')}
                                </span>
                            </button>
                        ) : null}
                    </div>
                )}
            </div>
        )
    }
}

export default CollapseProductBlock
