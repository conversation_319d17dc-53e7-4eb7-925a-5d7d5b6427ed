import { CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export type GeneralInformation = {
    description: string
    title: string
}

export interface GalleryTextInterface {
    description: string
    displayType: 'Carousel' | 'Grid'
    generalInformation: GeneralInformation[]
    galleryImages: CloudinaryImage[]
    heading: string
    identifier: string
    textPosition: 'Image Left' | 'Image Right'
    socialMedia: SocialMedia[]
    button: CTAType
}

export interface SocialMedia {
    identifier: string
    link: string
    icon: CloudinaryImage[]
}

export interface GalleryGridProps {
    identifier: string
    galleryImages: CloudinaryImage[]
}
