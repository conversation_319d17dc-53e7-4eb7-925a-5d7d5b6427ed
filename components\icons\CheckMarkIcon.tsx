import { FC, HTMLAttributes } from 'react'

interface Props extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
}

const CheckMarkIcon: FC<Props> = ({
    width = 20,
    height = 17,
    color = '#ffffff'
}) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 20 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M19.3738 0.953125H17.7358C17.5058 0.953125 17.2879 1.05914 17.1479 1.23914L7.48483 13.4811L2.85086 7.60913C2.78086 7.52113 2.69184 7.44911 2.58984 7.39911C2.48784 7.34911 2.37584 7.3241 2.26284 7.3241H0.623835C0.466835 7.3241 0.380832 7.5041 0.476832 7.6261L6.89584 15.7591C7.19584 16.1381 7.77285 16.1381 8.07485 15.7591L19.5218 1.25311C19.6178 1.13411 19.5308 0.953125 19.3738 0.953125Z"
            fill={color}
        />
    </svg>
)

export default CheckMarkIcon
