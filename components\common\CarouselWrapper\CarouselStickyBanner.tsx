import { createPortal } from 'preact/compat'
import React from 'react'
import cn from 'classnames'

import { CTA } from '../CTA'
import { ATC_HEADER_ID } from '../Layout/Layout'

import s from './CarouselStickyBanner.module.scss'
import { useCheckScrollEnd } from 'hooks/useCheckScrollEnd'

interface CarouselStickyBannerProps {
    identifier?: string
    label?: string
    buttonLabel?: string
}

const CarouselStickyBanner: React.FC<CarouselStickyBannerProps> = ({
    identifier = '',
    label = '',
    buttonLabel = ''
}) => {
    const { isScrollEndOfBlock } = useCheckScrollEnd(
        identifier ? `#${identifier}` : null
    )

    if (!identifier || !global?.document) return null

    return createPortal(
        <div
            data-visible={isScrollEndOfBlock}
            className={cn(s['sticky-header'])}
        >
            <div className={cn('scuf-container', s['sticky-header--content'])}>
                {!!label && (
                    <div
                        className={cn(s['sticky-header--label'])}
                        aria-label={label}
                    >
                        {label}
                    </div>
                )}
                {!!buttonLabel && (
                    <CTA
                        className={cn(
                            'scuf-button-primary',
                            s['sticky-header--button']
                        )}
                        cta={{
                            displayText: buttonLabel,
                            openInANewTab: false,
                            url: `#${identifier}`,
                            buttonType: 'primary'
                        }}
                    />
                )}
            </div>
        </div>,
        document.querySelector(`#${ATC_HEADER_ID}`)!
    )
}

export default CarouselStickyBanner
