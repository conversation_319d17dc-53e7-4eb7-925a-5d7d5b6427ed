import type { PylotFrontendConfig } from '../config.d'
export const config: PylotFrontendConfig = {
    base: {
        algolia: { apiKey: '', appId: '', defaultUserToken: '', index: '' },
        brands: ['https://www.elgato.com/'],
        currencyConfig: {
            corsair: {
                currencyMap: {
                    $: '$',
                    CAD: '$',
                    CHF: 'CHF',
                    CNY: '¥',
                    EUR: '€',
                    GBP: '£',
                    KRW: '₩',
                    PLN: 'zł',
                    SEK: 'kr',
                    TWD: 'NT$',
                    USD: '$',
                    kr: 'kr',
                    zł: 'zł',
                    '£': '£',
                    '¥': '¥',
                    '₩': '₩',
                    '€': '€'
                },
                currencySymbolRegex:
                    '/[$€¢£¥₩]|USD|EUR|CHF|GBP|TWD|PLN|CNY|CAD|KRW|CHF|kr|zł/gi',
                discountCurrencySymbol:
                    '/^[$€¢£¥₩]?(USD|EUR|GBP|TWD|PLN|CNY|CAD|KRW|CHF|kr|zł)?0[.,]00[  ]?[$€¢£¥₩]?(USD|EUR|GBP|TWD|PLN|CNY|CAD|KRW|CHF|kr|zł)?/gm'
            },
            elgato: {
                currencyMap: [
                    {
                        countryCode: ['EU', 'FR', 'DE', 'IT', 'ES', 'PT'],
                        position: 'right',
                        symbol: '€'
                    },
                    {
                        countryCode: ['CA', 'US'],
                        position: 'left',
                        symbol: '$'
                    },
                    { countryCode: ['UK'], position: 'left', symbol: '£' },
                    { countryCode: ['PL'], position: 'right', symbol: 'zł' },
                    { countryCode: ['SE'], position: 'left', symbol: 'kr' }
                ],
                formatRegex: '^\\(\\D+|)(\\d+)(,|\\.)(\\d+)(\\D+|)$'
            }
        },
        customMetaTags: [
            {
                contentKey:
                    'li6sqjv7ywbjj9felf4ve_staging, 7e199l7cgumbjans331htv_staging, xqdt7knglda0a81in9n794g_staging',
                name: 'zd-site-verification'
            },
            {
                contentKey: '9ca9af4214f42d70afbac84a0452343f85bd14f5_staging',
                name: 'naver-site-verification'
            }
        ],
        dateFormatByRegion: {
            'DD.MM.YYYY': 'DE,PL,CH',
            'DD/MM/YYYY': 'UK,FR,IT,ES,PT,EU',
            'YYYY/MM/DD': 'SE'
        },
        exceptionalCategories: {
            categoriesFromContentful: [
                { title: 'Weeklydeals', url_path: 'weeklydeals' }
            ],
            categoriesFromMegamenu: [
                { title: 'PC COMPONENTS', url_path: 'pc-cases' },
                { title: 'PC COMPONENTS', url_path: 'pc-case-comparison' },
                { title: 'PC COMPONENTS', url_path: 'certified-refurbished' },
                { title: 'PC COMPONENTS', url_path: 'refurb-cpu-coolers' },
                { title: 'PC CASE FANS', url_path: 'case-fans' },
                {
                    title: 'PC WATER COOLING PARTS',
                    url_path: 'custom-liquid-cooling'
                },
                { title: '120mm CASE FANS', url_path: '120mm-fans' }
            ]
        },
        externalUrls: ['/downloads'],
        freeShipping: [
            { amount: 79, currency: 'USD' },
            { amount: 99, currency: 'CAD' },
            { amount: 79, currency: 'GBP' },
            { amount: 79, currency: 'EUR' },
            { amount: 0, currency: 'TWD' },
            { amount: 330, currency: 'PLN' }
        ],
        gigya: {
            apiKey: '4_1M8KcZQw_A1o00SELaeYiA',
            baseUrl: 'https://gigya.corsair.com',
            enabled: true,
            reCaptchaInvisibleSiteKey:
                '6LdmCcQUAAAAAH8xlSiHlsVPnB-KTMxu24hLTqo9',
            reCaptchaV2SiteKey: '6Lc2z8MUAAAAAKFYfJtF-kH4h_YfU_DPhpPkDrbr'
        },
        klarna: {
            clientId: '3476206f-8387-5edd-9c3b-ee1d10e7b1fc',
            enabled: true,
            osmScriptLink: 'https://js.klarna.com/web-sdk/v1/klarna.js',
            regions: ['US']
        },
        liveChat: {
            enabled: false,
            key: 'e672beee-1f86-4883-ba2f-fd126f5db17d',
            settings: {
                webWidget: {
                    chat: {
                        departments: {
                            enabled: [
                                'Pre-Sales',
                                'Question about existing order'
                            ]
                        }
                    }
                }
            }
        },
        menu: { submenu: 'cms_block', submenu_maxitems: null },
        noFreeShipCategories: null,
        oneTrust: {
            dataDomainScript: '019234e6-0183-7aa4-9c63-bf1a9c633c15-test',
            enabled: true
        },
        product: {
            default_max_quantity: 2,
            visibleListAttributes: [
                'sku',
                'price_range',
                'small_image',
                'name',
                'stock_status',
                'backend_status',
                'max_allowed_quantity'
            ]
        },
        regionMapLanguages: [
            {
                defaultLanguage: 'en',
                languages: ['en', 'es', 'de', 'fr', 'it', 'ja'],
                redirectLanguage: null,
                redirectRegion: null,
                region: 'ca'
            },
            {
                defaultLanguage: null,
                languages: ['en', 'es', 'de', 'fr', 'it', 'ja'],
                redirectLanguage: null,
                redirectRegion: null,
                region: 'eu'
            },
            {
                defaultLanguage: null,
                languages: ['en', 'es', 'de', 'fr', 'it', 'ja'],
                redirectLanguage: null,
                redirectRegion: null,
                region: 'ww'
            },
            {
                defaultLanguage: null,
                languages: ['en', 'es', 'de', 'fr', 'it', 'ja'],
                redirectLanguage: null,
                redirectRegion: null,
                region: 'us'
            },
            {
                defaultLanguage: null,
                languages: ['en', 'es', 'de', 'fr', 'it', 'ja'],
                redirectLanguage: null,
                redirectRegion: null,
                region: 'au'
            }
        ],
        returns: { enabled: true, url: 'https://scufstage.returnscenter.com' },
        rollbar: null,
        route: { cms_home_page: 'home', cms_no_route: 'no-route' },
        shopifyUrl: 'https://scuf-stage.myshopify.com/',
        shortIo: { domain: 'm.cor.sr', token: 'pk_actBhH7i9YdfIyV2' },
        url: {
            baseUrl: 'https://staging-pwa.scufgaming.com/',
            localHostBaseUrl: 'http://localhost:3000/',
            mediaBackend: { baseMediaUrl: '', enabled: false },
            model: {
                category: { directory: 'c', suffix: '' },
                cmsPage: { directory: 's', suffix: '' },
                noLinkOut: { directory: '' },
                product: { directory: 'p', suffix: '' }
            }
        },
        wishlist: {
            enabled: false,
            multiple_wishlist_number: 0,
            multiple_wishlists: false
        },
        yotpo: { api_key: '', enabled: false },
        zendesk: { contentKey: '42954de1-b42e-4d96-adf4-b851f921204b' }
    },
    currencyCode: 'USD',
    is_fallback: true,
    preload: {
        footer: { role: null, variables: null },
        header: {},
        menu: {
            queryPath:
                'framework/pylot/hooks/contentful/graphql/getContentJsonQuery.ts',
            variables: {
                category_uid: null,
                contentType: 'navigation',
                identifier: ['mega-menu'],
                onlyTopLevel: null,
                options: { level: 1 }
            }
        }
    },
    seo: {
        checkoutSteps: { cart: 1, payment: 3, shipping: 2 },
        customPages: {
            account: {
                children: {
                    'account-information': {
                        pageName: 'My Account | Account Information',
                        pageType: 'account-information'
                    },
                    address: {
                        pageName: 'My Account | Address',
                        pageType: 'account-address'
                    },
                    'gift-card': {
                        pageName: 'My Account | Gift Card',
                        pageType: 'account-gift-card'
                    },
                    'my-returns': {
                        pageName: 'My Account | Returns',
                        pageType: 'account-returns'
                    },
                    newsletter: {
                        pageName: 'My Account | Newsletter Subscription',
                        pageType: 'account-newsletter'
                    },
                    orders: {
                        pageName: 'My Account | Orders',
                        pageType: 'account-orders'
                    },
                    'payment-details': {
                        pageName: 'My Account | Payment Details',
                        pageType: 'account-payment-details'
                    },
                    'store-credit': {
                        pageName: 'My Account | Store Credit',
                        pageType: 'account-store-credit'
                    },
                    wishlist: {
                        pageName: 'My Account | Wishlist',
                        pageType: 'account-wishlist'
                    }
                },
                pageName: 'My Account',
                pageType: 'account'
            },
            checkout: { pageName: 'Checkout page', pageType: 'checkout' },
            'k70-keyboard-builder': {
                pageName: 'K70 Keyboard Builder',
                pageType: 'k70-keyboard-builder'
            },
            search: { pageName: 'Search page', pageType: 'searchresults' }
        },
        default: {
            description: 'Scuf',
            openGraph: {
                images: [
                    {
                        alt: 'Scuf',
                        url:
                            'https://cwsmgmt.corsair.com/img/reusable/corsair-logo.svg'
                    }
                ],
                locale: 'en',
                site_name: 'Scuf',
                type: 'website',
                url: 'https://staging-pwa.scuf.com/ww/en/'
            },
            title: 'Scuf',
            titleTemplate: '%s',
            twitter: { cardType: 'summary', handle: '@Scuf', site: 'Scuf' }
        },
        organization: {
            allowedPath: '/',
            content: {
                address: {
                    '@type': 'PostalAddress',
                    addressLocality: 'Milpitas',
                    addressRegion: 'CA',
                    postalCode: '95035',
                    streetAddress: '115 N. McCarthy Blvd.'
                },
                contactPoint: [
                    {
                        areaServed: ['US', 'CA'],
                        availableLanguage: {
                            alternateName: 'en',
                            name: 'English'
                        },
                        contactOption: 'TollFree',
                        contactType: 'customer service',
                        telephone: '************'
                    }
                ],
                email: '<EMAIL>',
                legalName: 'Corsair Gaming Inc.',
                logo:
                    'https://cwsmgmt.corsair.com/img/reusable/corsair-logo.svg',
                name: 'Corsair',
                sameAs: [
                    'https://facebook.com/CORSAIR/',
                    'https://twitter.com/CORSAIR',
                    'https://www.instagram.com/corsair/',
                    'https://www.tiktok.com/@corsair',
                    'https://www.youtube.com/corsair'
                ],
                tickerSymbol: 'NYSE:CRSR',
                type: 'Corporation'
            }
        },
        product: {
            attributes: {
                alternate_name: ['alternate_name'],
                brand: ['brand'],
                description: ['description', 'html'],
                gtin: ['gtin'],
                image: ['media_gallery', '0', 'url'],
                item_condition: null,
                manufacturer: ['manufacturer'],
                model: ['model'],
                mpn: ['mpn'],
                name: ['name'],
                price_amount: [
                    'price_range',
                    'minimum_price',
                    'final_price',
                    'value'
                ],
                price_currency: [
                    'price_range',
                    'minimum_price',
                    'final_price',
                    'currency'
                ],
                review_count: ['review_count'],
                review_summary: ['review_summary'],
                seller: ['seller'],
                sku: ['sku']
            },
            bestRating: 5,
            defaultBrand: 'Corsair',
            defaultSeller: null,
            description: { maxLength: 160, regExp: null },
            image: { count: 1, placeholder: '', width: 620 },
            itemConditionMap: null,
            price: { currency: 'USD', precision: 2, validUntilDays: 14 }
        },
        searchBox: {
            allowedPath: '/',
            potentialActions: [
                { queryInput: 'search_term_string', target: '/search?q' }
            ]
        },
        tagManager: {
            convert: { containerId: '', enabled: false },
            gtm: {
                containerId:
                    'GTM-N7XM3WPT&gtm_auth=PG63tU7GtQL6TaDsj3uxFA&gtm_preview=env-3&gtm_cookies_win=x',
                enabled: true
            }
        }
    },
    static: { categories: { count: 0, root: '' }, products: { count: 0 } }
}
