import cn from 'classnames'
import s from './BlockTwoTile.module.scss'
import { BlockTwoTileCharts } from './BlockTwoTileCharts'
import { BlockTwoTileCta } from './BlockTwoTileCta'
import { BlockTwoTileLogo } from './BlockTwoTileLogo'
import { BlockTwoTileTables } from './BlockTwoTileTables'
import { BlockTwoTileResponse } from './BlockTwoTileTypes'
import { BlockWithIcons } from './BlockWithIcons'
import { CTAButton } from '../CTA'

type Props = {
    content: BlockTwoTileResponse
}

const twClasses = /*tw*/ {
    infoHeading:
        'leading-none font-secondary text-center lg:text-left md-max:text-left font-semibold',
    infoDescription: 'leading-6 md:leading-9 text-2xl font-light',
    subHeading: `
      ${s['info-subHeading']}
        font-tomorrow font-semibold text-center md:text-left
    `,
    copyBlock: `
      ${s['info-copy-block']}
        font-tomorrow font-normal mb-11
    `,
    calloutsText: `
      ${s['info-callouts']}
        font-tomorrow my-5 uppercase text-center lg:text-left
    `,
    disclaimerBlock: cn(s['info-disclaimer'], `mt-5 text-left order-last`)
}
export const leftImage = 'ImageLeft'

export const BlockTwoTileContent = ({ content }: Props) => {
    const {
        layoutPosition,
        layoutType,
        subheadingColor = '#ece81a',
        ctaBackground = 'None',
        ctaBorder = 'None'
    } = content
    const icons = content?.iconAndText
    const fontColor = {
        color: content.fontColor
    }
    const headingColor = {
        color: content.headingFontColor
    }
    const contentIncludedList = (content?: string) => {
        return (
            !(content?.includes('<ul>') || content?.includes('<ol>')) &&
            'text-center lg:text-left'
        )
    }

    return (
        <div
            className={cn(
                s[`Block-Content-${content.verticalAlignmentText}`],
                'text-2xl',
                !content.frame && 'md-max:mt-10 md:w-1/2 lg:mt-0',
                s['blockTwoTileContent']
            )}
        >
            <div
                className={cn(
                    s['inner-wrapper'],
                    'inner-wrapper',
                    s[`${layoutType}_wrapperMaxWidth`],
                    'lg:px-8 text-2xl md-max:px-0',
                    layoutPosition === leftImage ? '' : 'ml-auto'
                )}
            >
                {content.subHeading && (
                    <p
                        className={twClasses.subHeading}
                        style={{
                            color: subheadingColor
                        }}
                        dangerouslySetInnerHTML={{
                            __html: content.subHeading
                        }}
                    />
                )}
                {content?.heading &&
                    (content?.headingType === 'H1' ? (
                        <h1
                            className={cn(
                                s['info-heading'],
                                twClasses.infoHeading
                            )}
                            style={headingColor}
                            dangerouslySetInnerHTML={{
                                __html: content?.heading
                            }}
                        />
                    ) : (
                        <h2
                            className={cn(
                                s['info-heading'],
                                twClasses.infoHeading
                            )}
                            style={headingColor}
                            dangerouslySetInnerHTML={{
                                __html: content?.heading
                            }}
                        />
                    ))}
                {content.description && (
                    <div
                        className={cn(
                            s['info-description'],
                            twClasses.infoDescription,
                            contentIncludedList(content?.description)
                        )}
                        dangerouslySetInnerHTML={{
                            __html: content.description
                        }}
                        style={fontColor}
                    />
                )}
                {content.copyBlock && (
                    <div
                        className={cn(
                            twClasses.copyBlock,
                            contentIncludedList(content?.copyBlock?.text)
                        )}
                        dangerouslySetInnerHTML={{
                            __html: content.copyBlock.text
                        }}
                        style={fontColor}
                    />
                )}
                {content?.cta?.map((d) => (
                    <>
                        <CTAButton {...d} />
                        <BlockTwoTileCta
                            ctaProps={{
                                ...d,
                                ctaBackground,
                                ctaBorder
                            }}
                            key={d.title}
                            fontColor={content.fontColor}
                        />
                    </>
                ))}
                {content.callouts && (
                    <div className="mb-11">
                        {content.callouts.map((d) => (
                            <p
                                key={d.title}
                                className={twClasses.calloutsText}
                                dangerouslySetInnerHTML={{
                                    __html: d.content
                                }}
                                style={fontColor}
                            />
                        ))}
                    </div>
                )}
                {content?.sectionTitle && (
                    <h3
                        className={cn(
                            s['info-section-title'],
                            ' mb-8 text-center md:text-left md:self-start md:font-semibold lg:text-left'
                        )}
                        dangerouslySetInnerHTML={{
                            __html: content.sectionTitle
                        }}
                        style={fontColor}
                    />
                )}
                {icons && (
                    <BlockWithIcons
                        icons={icons}
                        fontColor={content.fontColor}
                    />
                )}
                {content?.tables?.length > 0 && (
                    <BlockTwoTileTables content={content.tables} />
                )}
                {content?.charts?.length > 0 && (
                    <BlockTwoTileCharts content={content.charts} />
                )}
                {content?.cloudinaryLogo &&
                    content.cloudinaryLogo.length > 0 && (
                        <BlockTwoTileLogo
                            cloudinaryLogo={content.cloudinaryLogo}
                            logoPosition={content.logoPosition}
                            logoWidth={content?.logoWidth}
                            logoHeight={content?.logoHeight}
                        />
                    )}

                {content?.disclaimerBlock && (
                    <div
                        className={twClasses.disclaimerBlock}
                        dangerouslySetInnerHTML={{
                            // __html: content.disclaimerBlock.replace(
                            //     /&lt;br\s?\/&gt;/gm,
                            //     '<br />'
                            // )
                            __html: content.disclaimerBlock.replaceAll(
                                '<p></p>',
                                '<br />'
                            )
                        }}
                        style={fontColor}
                    />
                )}
            </div>
        </div>
    )
}
