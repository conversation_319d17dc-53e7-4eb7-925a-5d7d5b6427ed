.hero-video-cta {
    &--section {
        @apply relative bg-cover bg-center;
        background-image: var(--mobile-bg-image);
        background-color: #00000000;
        background-blend-mode: darken;
        height: var(--mobile-bg-height);

        @screen md {
            background-image: var(--desktop-bg-image);
            height: var(--desktop-bg-height);
        }
    }

    &--content {
        @screen md-max {
            @apply h-full flex flex-col;
            width: var(--mobile-content-width);
            padding: 20px;

            &[data-mobile-position="left"],
            &[data-mobile-position="right"],
            &[data-mobile-position="center"] {
                @apply justify-center;
            }

            &[data-mobile-position="bottom-left"],
            &[data-mobile-position="bottom-center"],
            &[data-mobile-position="bottom-right"] {
                @apply justify-end;
            }

            &[data-mobile-position="top-left"],
            &[data-mobile-position="top-center"],
            &[data-mobile-position="top-right"] {
                @apply justify-start;
            }

            &[data-mobile-position="center"],
            &[data-mobile-position="top-center"],
            &[data-mobile-position="bottom-center"] {
                @apply items-center text-center;
            }

            &[data-mobile-position="left"],
            &[data-mobile-position="top-left"],
            &[data-mobile-position="bottom-left"] {
                @apply items-start text-left;
            }

            &[data-mobile-position="right"],
            &[data-mobile-position="top-right"],
            &[data-mobile-position="bottom-right"] {
                @apply items-end text-right;
            }
        }

        @screen md {
            @apply absolute;
            width: var(--desktop-content-width);

            &[data-desktop-position="left"] {
                top: 50%;
                transform: translateY(-50%);
            }
            &[data-desktop-position="top-left"] {
                top: 6%;
            }
            &[data-desktop-position="bottom-left"] {
                bottom: 6%;
            }

            &[data-desktop-position="left"],
            &[data-desktop-position="top-left"],
            &[data-desktop-position="bottom-left"] {
                text-align: left;
                left: 3%;

                // @media (min-width: 1366px) {
                //     left: calc((100vw - 1366px) / 2 + 20px);
                // }

                @screen xl-1440 {
                    left: 80px;
                }
            }

            &[data-desktop-position="right"] {
                top: 50%;
                transform: translateY(-50%);
            }
            &[data-desktop-position="top-right"] {
                top: 6%;
            }
            &[data-desktop-position="bottom-right"] {
                bottom: 6%;
            }

            &[data-desktop-position="right"],
            &[data-desktop-position="top-right"],
            &[data-desktop-position="bottom-right"] {
                text-align: right;
                right: 3%;

                // @media (min-width: 1366px) {
                //     right: calc((100vw - 1366px) / 2 + 20px);
                // }

                @screen xl-1440 {
                    right: 80px;
                }
            }

            &[data-desktop-position="center"] {
                text-align: center;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
            &[data-desktop-position="top-center"] {
                text-align: center;
                left: 50%;
                top: 6%;
                transform: translateX(-50%);
            }
            &[data-desktop-position="bottom-center"] {
                text-align: center;
                left: 50%;
                bottom: 6%;
                transform: translateX(-50%);
            }
        }
    }

    &--heading {
        @apply font-tomorrow font-medium uppercase;
        line-height: 100%;
        letter-spacing: 0;
        color: var(--primary);

        &.heading-h1 {
            font-size: 2.125rem;
        }
        &.heading-h2 {
            font-size: 2rem;
        }

        @screen md {
            font-size: 2.75rem;
            line-height: 110%;
            margin-bottom: 10px;

            &.heading-h1 {
                font-size: 3.375rem;
            }
            &.heading-h2 {
                font-size: 2.75rem;
            }
        }
    }

    &--subheading {
        @apply font-normal font-sofiaSans;
        font-size: 1rem;
        line-height: 1.5rem;
        color: var(--white);
        padding-right: 5px;
        margin-top: 20px;

        @media (min-width: 767px) {
            font-size: 1.25rem;
            line-height: 1.875rem;
            padding-right: 20px;
            margin-top: 10px;
        }
    }

    &--button {
        margin-top: 20px;

        @media (min-width: 767px) {
            margin-top: 40px;
        }
    }
}
