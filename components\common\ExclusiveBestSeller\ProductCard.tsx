import React from 'react'
import s from '@pagestyles/ExclusiveBestseller.module.scss'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { RichContent } from '@corsairitshopify/pylot-rich-content'
import { ProductPrice } from '../ProductPrice/ProductPrice'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { convertImageFormat } from 'helpers/cloudinaryHelper'
import { useProductUrlBuilder } from '@lib/hooks/useBuildProductUrl'
import { ModelTypeEnum } from '@config/base'
export const ProductCard = ({ product }: { product: any }): JSX.Element => {
    const { t } = useTranslation('common')
    const locale = window.location.pathname.split('/s')
    const priceStyle = `
        .regular-price {
            margin-right: 10px;
            text-decoration: none;
        }
        .compare-price {
            text-decoration: line-through;
        }
    `
    const productUrlBuilder = useProductUrlBuilder({
        page: ModelTypeEnum.PRODUCT
    })
    const isMobile = useMediaQuery('(max-width: 767px)')

    return (
        <div className={cn(s['crs-product-tile-child'])}>
            <div className={cn(s['crs-product-img'])}>
                <a href={productUrlBuilder({ product })}>
                    <span>
                        <img
                            src={
                                product?.image?.url
                                    ? convertImageFormat(
                                          product?.image?.url,
                                          'c_pad,q_auto,h_300,w_300',
                                          'webp'
                                      )
                                    : '/images/default-product-image.png'
                            }
                            alt={product.name}
                        />
                    </span>
                </a>
            </div>
            <div className={cn(s['crs-product-info'])}>
                <a href={productUrlBuilder({ product })}>
                    <h2 className={cn(s['crs-product-title'])}>
                        {product.name}
                    </h2>
                </a>
                <div className={cn(s['crs-product-price-main'])}>
                    <ProductPrice
                        priceRange={product.price_range}
                        notSellable={product?.not_sellable}
                        className={String(product.__typename).toLowerCase()}
                    />
                    <style>{priceStyle}</style>
                </div>
                {isMobile && !product.description?.html ? null : (
                    <p className={cn(s['crs-product-description'])}>
                        {product?.description?.html?.includes('<ul>') ? (
                            <RichContent
                                html={product.description?.html}
                                classes={{ root: cn(s['list']) }}
                            />
                        ) : (
                            <RichContent
                                html={product.description?.html}
                                classes={{ root: cn(s['paragraph']) }}
                            />
                        )}
                    </p>
                )}
                <a
                    href={productUrlBuilder({ product })}
                    className={cn(s['crs-product-shop-now'])}
                >
                    {t('SHOP NOW')}
                    <svg
                        width="24px"
                        height="24px"
                        viewBox="0 0 24 24"
                        fill="#ece81a"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"
                            fill="#ece81a"
                        />
                    </svg>
                </a>
            </div>
        </div>
    )
}
