import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { ProductsSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import ds from '../CarouselPillarsItem/CarouselPillars.module.scss'
import s from './CarouselPillarsDoubleImage.module.scss'
import { CTA } from '../CTA'
import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import sHeroStandard from '../HeroStandard/HeroStandard.module.scss'

export type CarouselProductTilesItemProps = {
    product: ProductsSlideType
}

const CarouselPillarsDoubleImage: FC<CarouselProductTilesItemProps> = ({
    product
}) => {
    const { t } = useTranslation(['common'])
    return (
        <div
            className={cn(ds['slider-item-container'], s['double-image'])}
            style={{
                backgroundColor:
                    product?.backgroundColor ?? 'var(--secondary-off-black)'
            }}
        >
            <div
                className={cn(
                    ds['slider-item-image-container'],
                    s['slider-item-image-container']
                )}
            >
                <CorsairImage
                    keepOrigin
                    src={convertUrlFormat(
                        product.cloudinaryMainImage?.[0]?.secure_url
                    )}
                    alt={t(
                        `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                    )}
                    layout="fill"
                    objectFit="cover"
                />
            </div>
            {product?.cloudinarySecondImage ? (
                <div
                    className={cn(
                        s['slider-item-second-image-container'],
                        'slider-item-second-image-container'
                    )}
                >
                    <CorsairImage
                        keepOrigin
                        src={convertUrlFormat(
                            product.cloudinarySecondImage?.[0]?.secure_url
                        )}
                        alt={t(
                            `alt|${product.cloudinarySecondImage?.[0]?.context?.custom?.alt}`
                        )}
                        layout="fill"
                        objectFit="cover"
                    />
                </div>
            ) : null}
            <div
                className={cn(
                    ds['slider-item-text-block'],
                    s['slider-item-text-block']
                )}
            >
                <h5
                    className={cn(
                        ds['slider-item-heading'],
                        s['slider-item-heading'],
                        'slider-item-heading'
                    )}
                    style={{
                        color: product.headingColor ?? 'var(--white)'
                    }}
                >
                    {product.heading}
                </h5>

                {product?.description && (
                    <p
                        className={cn(
                            ds['slider-item-description'],
                            s['slider-item-description'],
                            'slider-item-description'
                        )}
                        style={{
                            color: product?.textColor ?? 'var(--white)'
                        }}
                        dangerouslySetInnerHTML={{
                            __html: product.description
                        }}
                    />
                )}

                {product?.cta ? (
                    <div className="block">
                        <CTA
                            cta={product.cta}
                            className={cn()}
                            containerClassname={cn(
                                sHeroStandard['hero-standard-cta-container'],
                                s['slider-item-cta'],
                                'scuf-button-dark',
                                'scuf-show-right-arrow'
                            )}
                            rightIcon={
                                <div className={s['suffix-icon']}>
                                    <ArrowRightIcon />
                                </div>
                            }
                        />
                    </div>
                ) : null}
            </div>
        </div>
    )
}

export default CarouselPillarsDoubleImage
