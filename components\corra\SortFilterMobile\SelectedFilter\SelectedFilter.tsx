import {
    fromApplyFilterDataToUpdatedFilterData,
    getSortedAggregationListWithFeature
} from '@components/corra/ProductListFilter/util/transform'
import { CloseSVGIcon } from '@components/icons/VideoPlayer/CloseSVGIcon'
import { getCurrencySymbol } from '@corsairitshopify/corsair-filters-and-sort/src/FilterElements/FilterList/formatPriceLabel'
import {
    ApplyFilter,
    PLPManager
} from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { AggregationOption } from '@pylot-data/fwrdschema'
import cn from 'classnames'
import { ATTR_PRICE_FILTER } from 'hooks/usePlpManager'
import { useTranslation } from 'next-i18next'
import { FC, HTMLAttributes, useMemo } from 'react'
import styles from './SelectedFilter.module.scss'

interface Props extends Pick<HTMLAttributes<HTMLDivElement>, 'className'> {
    plpManager: PLPManager
    isNonTransactional: boolean
    currencyCode?: string
}

const SelectedFilter: FC<Props> = ({
    plpManager,
    isNonTransactional = false,
    className,
    currencyCode = 'USD'
}) => {
    const { t } = useTranslation(['plp', 'common'])
    const {
        plpState: {
            appliedFilterData,
            appliedSearchCritieria,
            filters,
            aggregations
        },
        setSearchCriteria,
        setFilter
    } = plpManager

    const sanitizedSelectedOptions = useMemo(() => {
        const sortedAggregationListWithFeature = getSortedAggregationListWithFeature(
            aggregations!,
            isNonTransactional
        )

        const sanitizedAggregation: AggregationOption[] = sortedAggregationListWithFeature.reduce(
            (prev: AggregationOption[], next: any) => {
                const currentOption =
                    next?.options?.map((opt: AggregationOption) => {
                        if (!opt?.label) return opt
                        const lbl = t(`plp-sidebar|${opt?.label}`)
                        opt.label = lbl.includes('plp-sidebar|')
                            ? opt?.label
                            : lbl
                        return opt
                    }) ?? []

                return [...prev, ...currentOption]
            },
            [] as AggregationOption[]
        )

        const currencySymbol = getCurrencySymbol(currencyCode)

        return appliedFilterData.map((data) => {
            const parentLabel =
                aggregations?.find(
                    (agg) => agg?.attribute_code === data?.requestVar
                )?.label ?? ''

            if (data?.requestVar === ATTR_PRICE_FILTER) {
                const valueArr = data.value_string.split('-')
                return {
                    ...data,
                    translateLabel: `Price: ${currencySymbol}${valueArr?.[0]} - ${currencySymbol}${valueArr?.[1]}`,
                    parentLabel: parentLabel
                        ? t(`plp-sidebar|${parentLabel}`)
                        : ''
                }
            }

            return {
                ...data,
                translateLabel:
                    sanitizedAggregation.find(
                        (aggregation) =>
                            aggregation.value === data?.value_string
                    )?.label ?? '',
                parentLabel: parentLabel ? t(`plp-sidebar|${parentLabel}`) : ''
            }
        })
    }, [aggregations, appliedFilterData, currencyCode, isNonTransactional, t])

    const handleRemoveFilter: ApplyFilter = (
        filterData,
        shouldApply,
        isSingleSelect
    ) => {
        const updatedData = fromApplyFilterDataToUpdatedFilterData({
            currentFilter: appliedFilterData,
            currentFilters: filters,
            currentSearchCriteria: appliedSearchCritieria,
            filterData,
            isSingleSelect,
            shouldApply
        })
        setSearchCriteria(updatedData.appliedSearchCritieria)
        setFilter({
            filters: updatedData.filters,
            appliedFilterData: updatedData.appliedFilterData
        })
    }

    if (sanitizedSelectedOptions.length === 0) {
        return null
    }

    return (
        <>
            <p className={styles.Subtitle}>{t('Now Shopping by')}</p>
            <div className={cn(styles.SelectedFilterWrapper, className)}>
                {sanitizedSelectedOptions.map((opt) => (
                    <button
                        key={opt.value_string}
                        aria-label={opt.translateLabel}
                    >
                        <span>
                            {`${opt.parentLabel}: `} {opt.translateLabel}
                        </span>
                        <CloseSVGIcon
                            className="cursor-pointer"
                            width={8}
                            height={8}
                            onClick={() => handleRemoveFilter(opt, false)}
                        />
                    </button>
                ))}
                <button
                    className={styles.ClearAllBtn}
                    onClick={() => handleRemoveFilter()}
                >
                    {t('Clear All')}
                </button>
            </div>
        </>
    )
}

export default SelectedFilter
