import { SearchCriteria } from '@corsairitshopify/corsair-filters-and-sort'
import {
    FiltersStateInput,
    PLPState
} from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import type {
    Aggregation,
    Maybe,
    ProductAttributeSortInput
} from '@pylot-data/pylotschema'
export enum ActionType {
    CLEAR_PLP_STATE = 'CLEAR_PLP_STATE',
    SET_CATEGORY_UID = 'SET_CATEGORY_UID',
    SET_FILTER = 'SET_FILTER',
    SET_AGGREGATIONS = 'SET_AGGREGATIONS',
    SORT = 'SORT',
    SET_FILTERS_VISIBLE = 'SET_FILTERS_VISIBLE',
    SET_SEARCH_CRITERIA = 'SET_SEARCH_CRITERIA'
}
export type Action =
    | {
          type: ActionType.CLEAR_PLP_STATE
          payload: Partial<PLPState>
      }
    | {
          type: ActionType.SET_CATEGORY_UID
          payload: string
      }
    | {
          type: ActionType.SET_FILTER
          payload: FiltersStateInput
      }
    | {
          type: ActionType.SET_AGGREGATIONS
          payload: Array<Maybe<Aggregation>>
      }
    | {
          type: ActionType.SORT
          payload: ProductAttributeSortInput
      }
    | {
          type: ActionType.SET_FILTERS_VISIBLE
          payload: boolean
      }
    | {
          type: ActionType.SET_SEARCH_CRITERIA
          payload: SearchCriteria[]
      }
