import { useTranslation } from 'next-i18next'
import cn from 'classnames'

import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

import s from './InlineTextImage.module.scss'

interface InlineTextImageProps {
    identifier?: string
    headline?: string
    text?: string
    textAlign?: 'left' | 'right' | 'center'
    textSize?: 'sm' | 'md' | 'lg'
    image?: CloudinaryImage[]
    layout?: '4-4' | '10-2'
}

const leftLayoutClass = {
    '4-4': 'md:w-4/12',
    '10-2': 'md:w-10/12'
}
const rightLayoutClass = {
    '4-4': 'md:w-4/12',
    '10-2': 'md:w-2/12'
}
const textAlignClass = {
    left: 'text-left',
    right: 'text-right',
    center: 'text-center'
}
const textSizeStyles = {
    sm: {
        fontSize: '1rem',
        lineHeight: '1.5rem'
    },
    md: {
        fontSize: '1.25rem',
        lineHeight: '1.5rem'
    },
    lg: {
        fontSize: '1.5rem',
        lineHeight: '1.75rem'
    }
}

const InlineTextImage: React.FC<{ content: InlineTextImageProps }> = ({
    content
}) => {
    const { t } = useTranslation()

    const {
        identifier,
        headline = '',
        text = '',
        textAlign = 'left',
        textSize = 'md',
        image = [],
        layout = '4-4'
    } = { ...content }

    return (
        <section id={identifier} className={s['inline-text-image']}>
            <div className={cn(s['inline-text-image--container'])}>
                <div className="flex flex-col md:flex-row items-center justify-center gap-x-5 gap-y-8">
                    <div
                        className={cn(
                            'flex flex-col gap-3 w-full',
                            leftLayoutClass[layout]
                        )}
                    >
                        {!!headline && (
                            <div
                                className={s['inline-text-image--headline']}
                                aria-label={headline}
                            >
                                {headline}
                            </div>
                        )}
                        {!!text && (
                            <div
                                className={cn(
                                    s['inline-text-image--text'],
                                    textAlignClass[textAlign]
                                )}
                                aria-label={text}
                                aria-live="polite"
                                style={textSizeStyles[textSize]}
                            >
                                {text}
                            </div>
                        )}
                    </div>
                    {!!image?.[0]?.secure_url && (
                        <div
                            className={cn(
                                'relative w-full',
                                rightLayoutClass[layout]
                            )}
                        >
                            <div className={s['inline-text-image--image']}>
                                <img
                                    className={s['slide--image']}
                                    src={convertUrlFormat(
                                        image?.[0]?.secure_url
                                    )}
                                    alt={correctImageAlt(
                                        t(
                                            `alt|${image?.[0]?.context?.custom?.alt}`
                                        )
                                    )}
                                    loading="lazy"
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </section>
    )
}

export default InlineTextImage
