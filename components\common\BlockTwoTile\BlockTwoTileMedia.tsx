import cn from 'classnames'
import s from './BlockTwoTile.module.scss'
import CorsairImage from '@corsairitshopify/corsair-image'
import { leftImage } from './BlockTwoTile'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import CorsairVideo from '../CorsairVideo/CorsairVideo'
import { useTranslation } from 'next-i18next'
export interface BlockTwoTileMediaProps {
    layoutPosition: string
    checkMedia: CloudinaryImage[]
    layoutType: string
    imageWidth?: string
    imageHeight?: string
    forceImageTakesFullHeight?: boolean
}

const c = /*tw*/ {
    imageContainer: 'relative flex items-center mx-auto',
    videoWithoutFrame: cn(
        s['slider-video'],
        `object-content h-full px-8 object-center`
    ),
    videoFrame: `w-full h-full my-10 lg:my-0 lg:mb-10  flex justify-start z-0 order-last lg:order-first`,
    videoFrameContainer: cn(s['frame-image-container'], `h-full block mx-auto`),
    imageVideo: cn(
        // s['image-or-video'],
        'image-or-video',
        `order-last lg:order-first md-max:pt-10 lg:pt-0 w-full lg:w-full`
    )
}

export const BlockTwoTileMedia = ({
    checkMedia,
    layoutPosition,
    layoutType,
    imageWidth,
    imageHeight,
    forceImageTakesFullHeight
}: BlockTwoTileMediaProps): JSX.Element => {
    const { t } = useTranslation(['common'])
    const VIDEO = checkMedia?.[0]?.resource_type || ''
    if (VIDEO === 'video') {
        return (
            <CorsairVideo
                secure_url={
                    checkMedia?.[0]?.secure_url
                        ? convertUrlFormat(checkMedia?.[0]?.secure_url)
                        : ''
                }
                className={cn(c.videoWithoutFrame)}
                fallbackImgUrl={
                    checkMedia?.[0]?.secure_url
                        ? convertUrlFormat(checkMedia?.[0]?.secure_url)
                        : ''
                }
            />
        )
    }

    return (
        <div className={cn(c.imageVideo, s['imageVideo'])}>
            <div
                className={cn(
                    s['ImageContainer'],
                    s[`${layoutType}blockTile`],
                    c.imageContainer
                )}
            >
                {checkMedia && (
                    <CorsairImage
                        keepOrigin
                        className={cn(
                            layoutPosition === leftImage
                                ? s[`${layoutType}_info-main-image-left`]
                                : s[`${layoutType}_info-main-image-right`]
                        )}
                        alt={correctImageAlt(
                            t(`alt|${checkMedia?.[0]?.context?.custom?.alt}`)
                        )}
                        src={
                            checkMedia?.[0]?.secure_url
                                ? convertUrlFormat(checkMedia?.[0]?.secure_url)
                                : ''
                        }
                        width={
                            imageWidth
                                ? imageWidth
                                : checkMedia?.[0]?.width
                                ? checkMedia?.[0]?.width
                                : 790
                        }
                        height={
                            imageHeight
                                ? imageHeight
                                : checkMedia?.[0]?.height
                                ? checkMedia?.[0]?.height
                                : 450
                        }
                        objectFit={
                            forceImageTakesFullHeight ? 'cover' : 'contain'
                        }
                    />
                )}
            </div>
        </div>
    )
}

export default BlockTwoTileMedia
