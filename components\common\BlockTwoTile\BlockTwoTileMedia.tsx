import cn from 'classnames'
import s from './BlockTwoTile.module.scss'
import CorsairImage from '@corsairitshopify/corsair-image'
import { leftImage } from './BlockTwoTile'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import CorsairVideo from '../CorsairVideo/CorsairVideo'
import { useTranslation } from 'next-i18next'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
export interface BlockTwoTileMediaProps {
    checkMedia: CloudinaryImage[]
    imageWidth?: string
    imageHeight?: string
    layoutPosition: string
    layoutType: string
    mediaFrame: CloudinaryImage[]
    frameEnabled: boolean
    toggleImageOverflow: boolean | undefined
    forceImageTakesFullHeight?: boolean
    videoLayout?: boolean
    justifyImage: string
    justifyImageMobile: string
    videoDescription?: string
}

const c = /*tw*/ {
    imageContainer: 'relative flex items-center',
    videoWithoutFrame: cn(
        s['slider-video'],
        `object-content h-full px-8 object-center`
    ),
    videoFrame: `w-full h-full my-10 lg:my-0 lg:mb-10  flex justify-start z-0 order-last lg:order-first`,
    videoFrameContainer: cn(s['frame-image-container'], `h-full block mx-auto`),
    imageVideo: cn(
        // s['image-or-video'],
        'image-or-video',
        `order-last lg:order-first md-max:pt-10 lg:pt-0 w-full lg:w-full`
    )
}

export const BlockTwoTileMedia = ({
    checkMedia,
    layoutPosition,
    layoutType,
    imageWidth,
    imageHeight,
    mediaFrame,
    frameEnabled,
    toggleImageOverflow,
    forceImageTakesFullHeight,
    videoLayout,
    justifyImage,
    justifyImageMobile,
    videoDescription
}: BlockTwoTileMediaProps) => {
    const { t } = useTranslation(['common'])
    const isMobile = useMediaQuery('(max-width: 767px)')
    const VIDEO = checkMedia?.[0]?.resource_type || ''
    const styles = {
        maxWidth: '1024px'
    }
    if (VIDEO === 'video') {
        return (
            <>
                {!frameEnabled && checkMedia ? (
                    <CorsairVideo
                        secure_url={
                            checkMedia?.[0]?.secure_url
                                ? convertUrlFormat(checkMedia?.[0]?.secure_url)
                                : ''
                        }
                        className={cn(
                            c.videoWithoutFrame,
                            videoLayout === false ? '' : 'w-full'
                        )}
                        fallbackImgUrl={
                            checkMedia?.[0]?.secure_url
                                ? convertUrlFormat(checkMedia?.[0]?.secure_url)
                                : ''
                        }
                        videoDescription={videoDescription}
                    />
                ) : (
                    <div className={cn(c.videoFrame)}>
                        <div className="w-full h-full relative " style={styles}>
                            <div
                                className={
                                    layoutPosition === leftImage
                                        ? `${s['frame-image-container-right']} block mx-auto`
                                        : c.videoFrameContainer
                                }
                            >
                                {mediaFrame?.[0]?.secure_url && (
                                    <CorsairImage
                                        keepOrigin
                                        src={convertUrlFormat(
                                            mediaFrame[0].secure_url
                                        )}
                                        alt={correctImageAlt(
                                            t(
                                                `alt|${mediaFrame[0]?.context?.custom?.alt}`
                                            )
                                        )}
                                        layout="responsive"
                                        width={100}
                                        height={77.9}
                                    />
                                )}
                                <CorsairVideo
                                    secure_url={
                                        checkMedia?.[0]?.secure_url
                                            ? convertUrlFormat(
                                                  checkMedia?.[0]?.secure_url
                                              )
                                            : ''
                                    }
                                    className={
                                        layoutPosition === leftImage
                                            ? s['image-frame-left']
                                            : s['image-frame']
                                    }
                                    fallbackImgUrl={
                                        checkMedia?.[0]?.secure_url
                                            ? convertUrlFormat(
                                                  checkMedia?.[0]?.secure_url
                                              )
                                            : ''
                                    }
                                    videoDescription={videoDescription}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </>
        )
    }
    const justifyImageClass = `justify-${
        isMobile ? justifyImageMobile : justifyImage
    }`
    const justifyMobileImageClass = isMobile
        ? justifyImageMobile === 'start'
            ? 'mr-auto'
            : justifyImageMobile === 'end'
            ? 'ml-auto'
            : 'mx-auto'
        : 'mx-auto'

    return (
        <>
            {!frameEnabled && (
                <div className={cn(c.imageVideo, s['imageVideo'])}>
                    <div
                        className={cn(
                            toggleImageOverflow && s['toggle-image-overflow'],
                            s['ImageContainer'],
                            s[`${layoutType}blockTile`],
                            c.imageContainer,
                            justifyImageClass,
                            justifyMobileImageClass
                        )}
                    >
                        {checkMedia && (
                            <CorsairImage
                                keepOrigin
                                className={cn(
                                    layoutPosition === leftImage
                                        ? s[
                                              `${layoutType}_info-main-image-left`
                                          ]
                                        : s[
                                              `${layoutType}_info-main-image-right`
                                          ]
                                )}
                                alt={correctImageAlt(
                                    t(
                                        `alt|${checkMedia?.[0]?.context?.custom?.alt}`
                                    )
                                )}
                                src={
                                    checkMedia?.[0]?.secure_url
                                        ? convertUrlFormat(
                                              checkMedia?.[0]?.secure_url
                                          )
                                        : ''
                                }
                                width={
                                    imageWidth
                                        ? imageWidth
                                        : checkMedia?.[0]?.width
                                        ? checkMedia?.[0]?.width
                                        : 790
                                }
                                height={
                                    imageHeight
                                        ? imageHeight
                                        : checkMedia?.[0]?.height
                                        ? checkMedia?.[0]?.height
                                        : 450
                                }
                                objectFit={
                                    forceImageTakesFullHeight
                                        ? 'cover'
                                        : 'contain'
                                }
                            />
                        )}
                    </div>
                </div>
            )}
            {frameEnabled && mediaFrame && (
                <div className={cn(c.videoFrame)}>
                    <div className="relative w-full h-full" style={styles}>
                        <div
                            className={
                                layoutPosition === leftImage
                                    ? `${s['frame-image-container-right']} block mx-auto`
                                    : c.videoFrameContainer
                            }
                        >
                            {mediaFrame?.[0]?.secure_url && (
                                <CorsairImage
                                    keepOrigin
                                    src={convertUrlFormat(
                                        mediaFrame[0].secure_url
                                    )}
                                    alt={correctImageAlt(
                                        t(
                                            `alt|${mediaFrame[0]?.context?.custom?.alt}`
                                        )
                                    )}
                                    layout="responsive"
                                    width={100}
                                    height={77.9}
                                />
                            )}
                            <div
                                className={
                                    layoutPosition === leftImage
                                        ? s['image-frame-left']
                                        : s['image-frame']
                                }
                            >
                                {checkMedia && (
                                    <CorsairImage
                                        keepOrigin
                                        className={cn(
                                            layoutPosition === leftImage
                                                ? s[
                                                      `${layoutType}_info-main-image-left`
                                                  ]
                                                : s[
                                                      `${layoutType}_info-main-image-right`
                                                  ]
                                        )}
                                        alt={correctImageAlt(
                                            t(
                                                `alt|${checkMedia?.[0]?.context?.custom?.alt}`
                                            )
                                        )}
                                        src={
                                            checkMedia?.[0]?.secure_url
                                                ? convertUrlFormat(
                                                      checkMedia?.[0]
                                                          ?.secure_url
                                                  )
                                                : ''
                                        }
                                        width={
                                            imageWidth
                                                ? imageWidth
                                                : checkMedia?.[0]?.width
                                                ? checkMedia?.[0]?.width
                                                : 790
                                        }
                                        height={
                                            imageHeight
                                                ? imageHeight
                                                : checkMedia?.[0]?.height
                                                ? checkMedia?.[0]?.height
                                                : 450
                                        }
                                        objectFit={
                                            forceImageTakesFullHeight
                                                ? 'cover'
                                                : 'contain'
                                        }
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    )
}

export default BlockTwoTileMedia
