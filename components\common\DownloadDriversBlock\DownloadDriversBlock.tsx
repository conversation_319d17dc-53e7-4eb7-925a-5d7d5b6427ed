import React from 'react'
import cn from 'classnames'
import dynamic from 'next/dynamic'

import DownloadDriverCard from './DownloadDriverCard'
import { DownloadDriversBlockProps } from './types'

import s from './DownloadDriversBlock.module.scss'

const DownloadDriverNewsletterSignupForm = dynamic(
    () => import('./DownloadDriverNewsletterSignupForm'),
    { ssr: false }
)

const DownloadDriversBlock: React.FC<{
    content: DownloadDriversBlockProps
}> = ({ content }) => {
    const {
        heading = '',
        headingColor = '#FFFFFF',
        headingType = 'h1',
        modalContent = '',
        modalEmailLabel = '',
        modalSkipLabel = '',
        modalHeading = '',
        drivers = [],
        hideOnMobile = true
    } = content

    const Heading =
        !!heading &&
        React.createElement(
            headingType,
            {
                className: cn(
                    s['heading'],
                    s[`heading-${headingType.toLowerCase()}`]
                ),
                style: {
                    color: headingColor
                }
            },
            heading
        )

    return (
        <div
            className={s['download-drivers-block']}
            data-hide-on-mobile={hideOnMobile}
            style={getContainerStyle(content)}
        >
            <div className={s['download-drivers-block--header']}>{Heading}</div>
            <div
                className={cn(
                    s['download-drivers-block--content'],
                    'scuf-container'
                )}
                role="list"
            >
                {drivers.map((driver, index) => (
                    <div key={index} role="listitem">
                        <DownloadDriverCard content={driver} />
                    </div>
                ))}
            </div>
            <DownloadDriverNewsletterSignupForm
                title={modalHeading}
                content={modalContent}
                emailLabel={modalEmailLabel}
                skipLabel={modalSkipLabel}
            />
        </div>
    )
}

export default DownloadDriversBlock

const paddingPresets = {
    none: {
        desktopPadding: '0rem',
        mobilePadding: '0rem'
    },
    small: {
        desktopPadding: '2rem',
        mobilePadding: '1rem'
    },
    medium: {
        desktopPadding: '3rem',
        mobilePadding: '2rem'
    },
    large: {
        desktopPadding: '4rem',
        mobilePadding: '3rem'
    }
}

const getContainerStyle = (
    content: DownloadDriversBlockProps
): React.CSSProperties => {
    const desktopUrl = content.desktopBackgroundImage?.[0]?.secure_url
    const mobileUrl =
        content.mobileBackgroundImage?.[0]?.secure_url || desktopUrl
    const desktopPadding =
        paddingPresets[content.paddingBlock || 'none'] || paddingPresets.none

    return {
        '--bg-color': content.backgroundColor || '#333132',
        '--desktop-bg-image': desktopUrl ? `url(${desktopUrl})` : undefined,
        '--mobile-bg-image': mobileUrl ? `url(${mobileUrl})` : undefined,
        '--desktop-pd-block': desktopPadding.desktopPadding,
        '--mobile-pd-block': desktopPadding.mobilePadding,
        '--item-bg-color': content.itemBackgroundColor || '#333132',
        '--item-text-color': content.itemTextColor || '#FFFFFF'
    } as React.CSSProperties
}
