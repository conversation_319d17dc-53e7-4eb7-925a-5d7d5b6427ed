import { Aggregation, Maybe } from '@pylot-data/pylotschema'

const TRANSACTIONAL_FIELDS = ['price_range', 'inventory']

export const getOnlyNonTransactionalOptions = (
    aggregationList: Maybe<Array<Maybe<Aggregation>>>
) =>
    aggregationList
        ? aggregationList.map((agg) => {
              if (agg?.attribute_code === 'features_and_availability') {
                  return {
                      ...agg,
                      options: agg?.options?.filter(
                          (option) =>
                              !TRANSACTIONAL_FIELDS.find((field) =>
                                  option?.value.includes(field)
                              )
                      )
                  }
              }
              return agg
          })
        : []
