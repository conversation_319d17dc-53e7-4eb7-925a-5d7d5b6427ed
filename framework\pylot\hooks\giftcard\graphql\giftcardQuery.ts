export const giftcardQuery = /* GraphQL */ `
    query giftcardQuery($searchCriteria: [SearchCriteriaInput!]!) {
        getGiftcard(searchCriteria: $searchCriteria) {
            id
            title
            description
            descriptionHtml
            mediaCount
            variantsCount
            media {
                id
                image {
                    altText
                    height
                    id
                    url
                    width
                }
            }
            variants {
                id
                sku
                availableForSale
                title
                image {
                    url
                    width
                    id
                    altText
                    height
                }
                price {
                    amount
                    currencyCode
                }
                quantityAvailable
                unitPrice {
                    amount
                    currencyCode
                }
                currentlyNotInStock
                compareAtPrice {
                    amount
                    currencyCode
                }
                selectedOptions {
                    name
                    value
                }
                variant_image_url {
                    value
                }
                variant_image_width {
                    value
                }
                variant_image_height {
                    value
                }
                variant_image_altText {
                    value
                }
            }
        }
    }
`
