import React, { ReactElement, useEffect, useState } from 'react'
import s from '@styles/common/product-gallery/ProductGallery.module.scss'
import cn from 'classnames'
import type {
    Maybe,
    MediaGalleryInterface,
    ProductImage,
    ProductInterface,
    ProductVideo
} from '@pylot-data/pylotschema'
import dynamic from 'next/dynamic'
import GalleryControls, {
    galleryControlsStatusType,
    updateGalleryControlsStatusFunctionType
} from './GalleryControls/GalleryControls'
import { cloudinaryDimensionsModelType } from '@pylot-data/hooks/contentful/use-content-json'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'

const StackedGallery = dynamic(() => import('./StackedGallery'), { ssr: false })
const ScrollGallery = dynamic(() => import('./ScrollGallery'))
const ThumbnailGallery = dynamic(() => import('./ThumbnailGallery'))

export const galleryControlsLabels = {
    images: 'Images',
    videos: 'Video',
    threeHundredSixty: 'View in 3D'
}

export const createInitialGalleryControls = (
    hasMediaGallery: boolean,
    hasEmbedVideo: boolean,
    hasCloudinaryModel: boolean
) => ({
    active: galleryControlsLabels.images,
    controls: [
        {
            label: galleryControlsLabels.images,
            icon:
                'https://assets.corsair.com/image/upload/f_auto,q_auto/pages/PDP%20Gallery%20Page%20Revamp%20Assets/Image_Icon_1.svg',
            active: true,
            enabled: hasMediaGallery,
            class: 'images-btn',
            hidden: false
        },
        {
            label: galleryControlsLabels.videos,
            icon:
                'https://assets.corsair.com/image/upload/f_auto,q_auto/pages/PDP%20Gallery%20Page%20Revamp%20Assets/Play_Icon-1.svg',
            active: false,
            enabled: hasEmbedVideo,
            class: 'videos-btn',
            hidden: !hasEmbedVideo
        },
        {
            label: galleryControlsLabels.threeHundredSixty,
            icon:
                'https://assets.corsair.com/image/upload/f_auto,q_auto/pages/PDP%20Gallery%20Page%20Revamp%20Assets/view_in_ar_24dp_5F6368_1-1.svg',
            active: false,
            enabled: hasCloudinaryModel,
            class: 'three-hundred-sixty-btn',
            hidden: !hasCloudinaryModel
        }
    ]
})

export type ProductGalleryType = {
    bundleProducts?: ProductInterface[]
    galleryControlDimensions?: { width: number; height: number }
    embedVideoUrl?: string
    cloudinaryDimensionsModel?: cloudinaryDimensionsModelType
    howToLink?: string
    layout?: 'fill' | 'fixed' | 'intrinsic' | 'responsive' | undefined
    breakpoints?: string
    productTabsVisible?: boolean
    mediaGalleryEntries: Maybe<MediaGalleryInterface>[]
    config?: { mobile?: GalleryConfigType; desktop?: GalleryConfigType }
    ratio?: string
    isAccessoryProduct?: boolean
}

export enum GalleryTypeEnum {
    SCROLL = 'SCROLL',
    STACKED = 'STACKED',
    THUMBNAIL = 'THUMBNAIL'
}

export type GalleryConfigType = {
    gallerytype: string
    zoom: boolean
    loop?: boolean
    fullscreen: boolean
    ratio?: string
    thumb?: boolean
    sliderProps?: {
        main?: {
            allowTouchMove?: boolean
            spaceBetween?: number
            maxSlides?: number
            navigation?: boolean
            pagination?: boolean | { clickable: boolean }
        }
        thumbs?: {
            spaceBetween?: number
            slidesPerView?: number
        }
    }
}

export type GalleryType = {
    bundleProducts?: ProductInterface[]
    galleryControlDimensions?: { width: number; height: number }
    galleryConfig: GalleryConfigType
    embedVideoUrl: string | undefined
    cloudinaryDimensionsModel: cloudinaryDimensionsModelType | undefined
    howToLink: string | undefined
    productTabsVisible: boolean | undefined
    mediaGalleryEntries: Maybe<ProductVideo | ProductImage>[]
    breakpoints?: string
    layout?: 'fill' | 'fixed' | 'intrinsic' | 'responsive' | undefined
    ratio?: string
    isMobile?: boolean
    showOnMobile?: boolean
}

const defaultGalleryConfig = {
    mobile: {
        gallerytype: GalleryTypeEnum.SCROLL,
        loop: true,
        zoom: true,
        fullscreen: false,
        thumb: false
    },
    desktop: {
        gallerytype: GalleryTypeEnum.STACKED,
        loop: true,
        zoom: false,
        fullscreen: true
    }
}

const Gallery = ({
    bundleProducts,
    galleryControlDimensions,
    galleryConfig,
    embedVideoUrl,
    cloudinaryDimensionsModel,
    howToLink,
    productTabsVisible,
    mediaGalleryEntries,
    breakpoints,
    layout,
    isMobile,
    galleryControlsStatus,
    updateGalleryControlsStatus,
    playEmbedVideo,
    setPlayEmbedVideo,
    showOnMobile
}: GalleryType & {
    galleryControlsStatus: galleryControlsStatusType
    updateGalleryControlsStatus: updateGalleryControlsStatusFunctionType
    playEmbedVideo: boolean
    setPlayEmbedVideo: (play: boolean) => void
}): ReactElement => {
    const createBundleProductImages = () => {
        if (!bundleProducts) {
            return mediaGalleryEntries
        }

        const defaultProductImage = '/images/default-product-image.png'
        const bundleProductImages = bundleProducts.map((product, index) => ({
            disabled: false,
            label: product.name,
            position: mediaGalleryEntries.length + index,
            url: product.image ? product.image : defaultProductImage,
            __typename: 'ProductImage'
        })) as ProductImage

        return mediaGalleryEntries.concat(bundleProductImages)
    }

    const showVideo = showOnMobile === isMobile
    return (
        <>
            {isMobile && (
                <GalleryControls
                    galleryControlsStatus={galleryControlsStatus}
                    updateGalleryControlsStatus={updateGalleryControlsStatus}
                />
            )}

            {galleryConfig.gallerytype === GalleryTypeEnum.SCROLL && (
                <div className={s['scroll-gallery']}>
                    <ScrollGallery
                        mediaGalleryEntries={createBundleProductImages()}
                        zoom={galleryConfig.zoom}
                        showFullscreen={galleryConfig.fullscreen}
                        thumb={galleryConfig.thumb}
                        sliderProps={galleryConfig.sliderProps}
                        loop={galleryConfig.loop}
                        embedVideoUrl={embedVideoUrl}
                        cloudinaryDimensionsModel={cloudinaryDimensionsModel}
                        galleryControlsStatus={galleryControlsStatus}
                        updateGalleryControlsStatus={
                            updateGalleryControlsStatus
                        }
                        playEmbedVideo={playEmbedVideo}
                        setPlayEmbedVideo={setPlayEmbedVideo}
                        howToLink={howToLink}
                        productTabsVisible={productTabsVisible}
                        breakpoints={breakpoints}
                        layout={layout}
                        ratio={galleryConfig.ratio}
                        showVideo={showVideo}
                    />
                </div>
            )}

            {galleryConfig.gallerytype === GalleryTypeEnum.STACKED && (
                <div className={s['stacked-gallery']}>
                    <StackedGallery
                        mediaGalleryEntries={createBundleProductImages()}
                        zoom={galleryConfig.zoom}
                        showFullscreen={galleryConfig.fullscreen}
                        breakpoints={breakpoints}
                        layout={layout}
                        galleryControlsStatus={galleryControlsStatus}
                        updateGalleryControlsStatus={
                            updateGalleryControlsStatus
                        }
                        playEmbedVideo={playEmbedVideo}
                        setPlayEmbedVideo={setPlayEmbedVideo}
                        embedVideoUrl={embedVideoUrl}
                    />
                </div>
            )}

            {galleryConfig.gallerytype === GalleryTypeEnum.THUMBNAIL && (
                <div className={s['thumbnail-gallery']}>
                    <ThumbnailGallery
                        mediaGalleryEntries={createBundleProductImages()}
                        zoom={galleryConfig.zoom}
                        showFullscreen={galleryConfig.fullscreen}
                        embedVideoUrl={embedVideoUrl}
                        cloudinaryDimensionsModel={cloudinaryDimensionsModel}
                        galleryControlsStatus={galleryControlsStatus}
                        updateGalleryControlsStatus={
                            updateGalleryControlsStatus
                        }
                        playEmbedVideo={playEmbedVideo}
                        setPlayEmbedVideo={setPlayEmbedVideo}
                        howToLink={howToLink}
                        productTabsVisible={productTabsVisible}
                        breakpoints={breakpoints}
                        layout={layout}
                        ratio={galleryConfig.ratio}
                        showVideo={showVideo}
                        sliderProps={galleryConfig.sliderProps}
                        thumb={galleryConfig.thumb}
                        loop={galleryConfig.loop}
                    />
                </div>
            )}
        </>
    )
}

export const ProductGallery = ({
    bundleProducts,
    galleryControlDimensions = { height: 22, width: 22 },
    embedVideoUrl,
    mediaGalleryEntries,
    cloudinaryDimensionsModel,
    howToLink,
    productTabsVisible,
    config = defaultGalleryConfig,
    breakpoints,
    layout,
    isAccessoryProduct
}: ProductGalleryType): ReactElement => {
    const [playEmbedVideo, setPlayEmbedVideo] = useState(false)
    const [galleryControlsStatus, setGalleryControlsStatus] = useState(
        createInitialGalleryControls(
            !!mediaGalleryEntries,
            !!embedVideoUrl,
            !!cloudinaryDimensionsModel
        )
    )

    const isMobile = useMediaQuery('(max-width: 1023px)')
    const [showOnMobile, setShowOnMobile] = useState(false)
    useEffect(() => {
        setShowOnMobile(isMobile)
    }, [isMobile])

    return (
        <div
            className={cn(s['pdp-gallery'], {
                [s['accessory-pdp-gallery']]: isAccessoryProduct
            })}
        >
            <div className={s['gallery-container']}>
                {config.mobile && (
                    <div className={cn(s['mobile-gallery'], 'mobile-gallery')}>
                        <Gallery
                            bundleProducts={bundleProducts}
                            galleryControlDimensions={galleryControlDimensions}
                            galleryConfig={config.mobile}
                            embedVideoUrl={embedVideoUrl}
                            mediaGalleryEntries={mediaGalleryEntries}
                            cloudinaryDimensionsModel={
                                cloudinaryDimensionsModel
                            }
                            howToLink={howToLink}
                            productTabsVisible={productTabsVisible}
                            breakpoints={breakpoints || ''}
                            layout={layout}
                            galleryControlsStatus={galleryControlsStatus}
                            updateGalleryControlsStatus={
                                setGalleryControlsStatus
                            }
                            playEmbedVideo={playEmbedVideo}
                            setPlayEmbedVideo={setPlayEmbedVideo}
                            isMobile
                            showOnMobile={showOnMobile}
                        />
                    </div>
                )}
                {config.desktop && (
                    <div
                        className={cn(s['desktop-gallery'], 'desktop-gallery', {
                            [s['desktop-accessory-gallery']]: isAccessoryProduct
                        })}
                    >
                        <Gallery
                            bundleProducts={bundleProducts}
                            galleryConfig={config.desktop!}
                            embedVideoUrl={embedVideoUrl}
                            mediaGalleryEntries={mediaGalleryEntries}
                            cloudinaryDimensionsModel={
                                cloudinaryDimensionsModel
                            }
                            howToLink={howToLink}
                            productTabsVisible={productTabsVisible}
                            breakpoints={breakpoints || ''}
                            layout={layout}
                            galleryControlsStatus={galleryControlsStatus}
                            updateGalleryControlsStatus={
                                setGalleryControlsStatus
                            }
                            playEmbedVideo={playEmbedVideo}
                            setPlayEmbedVideo={setPlayEmbedVideo}
                            isMobile={false}
                            showOnMobile={showOnMobile}
                        />
                    </div>
                )}
            </div>
        </div>
    )
}

export default ProductGallery
