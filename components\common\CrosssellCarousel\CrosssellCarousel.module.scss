.BaseCarouselWrapper {
    @apply overflow-hidden;
    // padding-inline: 20px;

    @screen md {
        // padding-inline: 80px;
    }

    // &.NoPseudoStart {
    //     &::before {
    //         @apply hidden;
    //     }
    // }

    // &.NoPseudoEnd {
    //     &::after {
    //         @apply hidden;
    //     }
    // }

    // &::before,
    // &::after {
    //     @apply h-full absolute top-0 block z-2;
    //     content: '';
    //     background: #0f0f0f;
    //     opacity: 0.3;
    //     width: 4.8%;

    //     @screen md-max {
    //         @apply hidden;
    //     }
    // }

    // &::before {
    //     @apply left-0;
    // }

    // &::after {
    //     @apply right-0;
    // }

    // &::before {
    //     --color-surface: #000000;
    //     --gradient-to-position: ;
    //     --gradient-from-position: ;
    //     --gradient-stops: var(--gradient-from), var(--gradient-to);
    //     --gradient-from: transparent var(--gradient-to-position);
    //     --gradient-to: var(--color-surface) var(--gradient-to-position);
    //     background-image: linear-gradient(to left, var(--gradient-stops));
    //     content: '';
    //     @apply absolute top-0 left-0 h-full w-1/12 z-2;
    // }

    // &::after {
    //     --color-surface: #000000;
    //     --gradient-to-position: ;
    //     --gradient-from-position: ;
    //     --gradient-stops: var(--gradient-from), var(--gradient-to);
    //     --gradient-from: transparent var(--gradient-to-position);
    //     --gradient-to: var(--color-surface) var(--gradient-to-position);
    //     background-image: linear-gradient(to right, var(--gradient-stops));
    //     content: '';
    //     @apply absolute top-0 right-0 h-full w-1/12 z-2;
    // }

    // @screen md-max {
    //     &::before,
    //     &::after {
    //         display: none;
    //     }
    // }

    .faded-left {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to left, var(--gradient-stops));
        transition: opacity 0.5s ease-in-out;
        @apply absolute top-0 left-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .faded-right {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to right, var(--gradient-stops));
        transition: opacity 0.5s ease-in-out;
        @apply absolute top-0 right-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .prev:disabled,
    .prev:disabled ~ .faded-left {
        @apply opacity-0;
    }
    .prev:disabled ~ .swiper-container {
        // margin-left: 20px;
        transform: translateX(20px);
        @screen md {
            // margin-left: 80px;
            transform: translateX(80px);
        }
    }
    .next:disabled,
    .next:disabled ~ .faded-right {
        @apply opacity-0;
    }
    .next:disabled ~ .swiper-container {
        // margin-right: 20px;
        transform: translateX(-20px);
        @screen md {
            // margin-right: 80px;
            transform: translateX(-80px);
        }
    }
    .prev:disabled + .next:disabled {
        & ~ .swiper-container {
            transform: translateX(0px) !important;
            margin-inline: 20px;
            @screen md {
                margin-inline: 80px;
            }
        }
    }

    @screen md-max {
        .faded-left,
        .faded-right {
            display: none;
        }
    }

    .slider-item-container {
        @apply flex flex-col;

        @screen xl-1280 {
            max-width: 428px;
            height: 429px;
        }

        @media screen and (max-width: 1500px) {
            // max-width: 320px;
            height: 320px;
        }

        @screen md-max {
            // max-width: 306px;
            height: 306px;
        }

        .slider-item-text-block {
            padding: 24px 36px;

            .slider-item-heading {
                @apply font-medium;
                font-size: 32px;
                line-height: 38.4px;
                margin-bottom: 12px;

                @screen md-max {
                    font-size: 22px;
                    line-height: 100%;
                    letter-spacing: 0%;
                    @apply font-primary font-medium;
                }
            }

            .slider-item-display-text {
                font-size: 16px;
                line-height: 20.26px;
                @apply font-bold;
            }

            .slider-item-cta {
                display: flex !important;
                align-items: center !important;
            }

            .slider-item-cta-text {
                @apply font-primary font-medium;
                font-size: 16px;
                line-height: 20.26px;
            }
        }

        .slider-item-image-container {
            flex: 1 1;
        }
    }
    @apply relative;

    .swiper-container {
        transition: transform 0.1s linear;

        @screen md {
            transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        }
        // &.last-item-reached {
        //     margin-right: 20px;
        //     transition: left 0.35s ease-in-out;
        //     @screen md {
        //         margin-right: 80px;
        //     }
        // }

        // &.first-item-reached {
        //     margin-left: 20px;
        //     transition: left 0.35s ease-in-out;
        //     @screen md {
        //         margin-left: 80px;
        //     }
        // }
    }

    .carousel-nav-button {
        transform: translateY(-50%);
        width: 52px;
        height: 48px;
        @apply z-10 top-1/2 absolute flex items-center justify-center overflow-hidden;

        :global(#Oval-2) {
            stroke: transparent;
        }

        &:disabled {
            display: none !important;
        }

        div {
            button {
                &:focus {
                    outline: none;
                }
            }
        }

        &.prev {
            left: 3%;
            @screen md-max {
                @apply hidden;
            }

            svg {
                transform: rotateZ(180deg);
            }
        }

        &.next {
            right: 3%;
            @screen md-max {
                @apply hidden;
            }
        }
    }
}

.NoPseudo {
    &::before,
    &::after {
        @apply hidden;
    }
}

.pagination-container {
    @screen md {
        display: none !important;
    }

    &.pagination-container {
        @apply flex items-center justify-around;
        margin-top: 40px;
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--primary);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }
}

.CrossellWrapper {
    // padding: 4rem 0px 7rem 0px;
    padding-top: 1.5rem;
    padding-bottom: 3rem;
    @apply bg-black;

    // @screen md-max {
    //     padding: 3rem 0px 3rem 18px;
    // }

    @screen md {
        padding-top: 3.25rem;
        padding-bottom: 3.5rem;
    }

    .Heading {
        // margin-bottom: 80px;
        // margin-inline: 80px;
        // font-size: 54px;
        // line-height: 65px;
        // @apply font-medium text-white;

        // @screen md-max {
        //     margin-inline: 18px;
        //     margin-bottom: 28px;
        //     font-size: 32px;
        //     line-height: 38.4px;
        // }
        padding-inline: 20px;
        margin-bottom: 28px;
        font-size: 32px;
        line-height: 38.4px;
        @apply font-medium text-white;
        @screen md {
            margin-bottom: 65px;
            padding-inline: 80px;
            font-size: 54px;
            line-height: 65px;
        }
    }

    // Max width variant
    &.max-width-variant {
        .Heading {
            @apply font-tomorrow p-0 text-2xl;
            line-height: 26px;
        }

        .ProductName {
            @apply font-sofiaSans text-base font-bold;
            line-height: 24px;
        }

        .ProductCategory {
            @apply font-sofiaSans text-base font-normal;
            line-height: 24px;
        }

        .PdpPrice {
            @apply font-sofiaSans text-base font-normal;
            line-height: 24px;
        }
    }
}

.CarouselItemWrapper {
    .ImageWrapper {
        width: 100%;
        aspect-ratio: 1/1;
        @apply relative overflow-hidden bg-secondaryOffBlack;
    }

    .ProductName {
        font-size: 24px;
        line-height: 30px;
        margin-top: 10px;
        @apply text-white font-sofiaSans;
    }

    .ProductCategory {
        font-size: 14px;
        line-height: 18px;
        margin-top: 2px;
        @apply text-white font-sofiaSans;
    }

    .PdpPrice {
        margin-top: 2px;
        [class*='regular-price'] {
            font-size: 16px;
            color: var(--primary);
            @apply font-semibold font-sofiaSans;
        }
        [class*='compare-price'] {
            text-decoration: line-through;
            color: var(--steel-gray20);
            @apply font-semibold font-sofiaSans;
        }
    }

    @apply flex flex-col;
}