import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import Link from 'next/link'
import s from './BrandSelector.module.scss'
import { BrandItemInterface } from './BrandSelector.types'

type BrandItemProps = {
    item: BrandItemInterface
    isMobile: boolean
}
const BrandItem: React.FC<BrandItemProps> = ({
    item,
    isMobile
}: BrandItemProps) => {
    return (
        <div className={s['brand-item']} key={item.identifier}>
            <Link href={item.url}>
                <a>
                    <img
                        alt={item.image[0].context?.custom?.alt || ''}
                        src={
                            isMobile
                                ? convertUrlFormat(
                                      item.mobileImage[0].secure_url
                                  )
                                : convertUrlFormat(item.image[0].secure_url)
                        }
                    />
                </a>
            </Link>
        </div>
    )
}

export default BrandItem
