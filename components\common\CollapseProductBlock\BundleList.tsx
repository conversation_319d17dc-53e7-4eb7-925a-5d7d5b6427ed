/* eslint-disable jsx-a11y/img-redundant-alt */
import { ProductInterface } from '@pylot-data/pylotschema'
import { useTranslation } from 'next-i18next'
import { useMemo } from 'react'
import type { VFC } from 'react'
import { ProductPrice } from '@components/common/ProductPrice/ProductPrice'
import { Badge } from '@components/common/ProductTile/Badge'
import { ICollapseProductBlockProps } from './CollapseProductBlock'
import s from './CollapseProductBlock.module.scss'
import { ButtonLabel } from '@pylot-data/hooks/product/use-product-ui'
import { Button } from '@corsairitshopify/pylot-ui'
import { Cart as CartIcon } from '@components/icons/Cart'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { convertImageFormat } from 'helpers/cloudinaryHelper'
import cn from 'classnames'
import { ClickFrom } from '@pylot-data/enums/ClickFromEnum.d'
import { useRouter } from 'next/router'
import { useUser } from '@corsairitshopify/pylot-auth-manager'
import { isTransactionalView } from 'helpers'
import { ModelTypeEnum } from '@config/base'
import { useProductUrlBuilder } from '@lib/hooks/useBuildProductUrl'

interface BundleListProps {
    bundles: ProductInterface[]
    maxVisible: number
    content: ICollapseProductBlockProps
    addProductHandler: (product: ProductInterface, clickFrom: string) => void
}

export const BundleList: VFC<BundleListProps> = ({
    bundles,
    maxVisible,
    content,
    addProductHandler
}) => {
    const productItemsLimitedLength = bundles.slice(0, maxVisible)
    const productUrlBuilder = useProductUrlBuilder({
        page: ModelTypeEnum.PRODUCT
    })
    const { t } = useTranslation('common')
    const { isAdding } = useAddToCart()
    const router = useRouter()
    const { locale } = router
    const { customer } = useUser()

    const isTransactional = useMemo(
        () => isTransactionalView('addToCart', locale, customer),
        [locale, customer]
    )

    const cartLabel = isAdding
        ? t('Adding...')
        : t(
              isTransactional
                  ? ButtonLabel.ADD_TO_CART
                  : ButtonLabel.LEARN_MORE_CAPITAL_CASE
          )

    return (
        <div className={s['collapse-bundle-block__wrapper']}>
            {productItemsLimitedLength.map((product: ProductInterface) => {
                const handleOnCTAClick = () => {
                    if (isTransactional) {
                        return addProductHandler(product, ClickFrom.OTHER)
                    }

                    return router.push(productUrlBuilder({ product }))
                }

                const productLink = productUrlBuilder({ product })

                if (!product) return null

                //fallback if image node not present for recently viewed old cache products
                const pickImageUrl =
                    product?.image?.url ||
                    product?.media_gallery?.[0]?.url ||
                    product?.small_image?.url

                // in case image has standard cloudinary format replace with the correct format for this page
                const imageUrl =
                    pickImageUrl &&
                    convertImageFormat(
                        pickImageUrl,
                        'c_pad,q_auto,h_500,w_500',
                        'webp'
                    )
                const showBadge =
                    (product?.badge || product?.custom_badge) &&
                    content?.showBadge
                return (
                    <div
                        key={product?.uid}
                        className={`${s['collapse-bundle-block__item']} flex md-max:block`}
                    >
                        <a
                            href={productLink}
                            target="_blank"
                            rel="noreferrer"
                            className="corsair-links"
                        >
                            <div
                                className={`${s['collapse-bundle-block__item-image']} relative`}
                            >
                                {showBadge && (
                                    <Badge
                                        className={
                                            s[
                                                'collapse-product-block__item-image-badge'
                                            ]
                                        }
                                        label={product?.badge || null}
                                        customBadge={product?.custom_badge}
                                    />
                                )}
                                <img
                                    src={
                                        imageUrl ||
                                        '/images/default-product-image.png?w=1920&q=75'
                                    }
                                    alt={product?.image?.label || ''}
                                />
                            </div>
                        </a>
                        <div className="flex flex-col justify-between">
                            <a
                                href={productLink}
                                target="_blank"
                                rel="noreferrer"
                            >
                                <div
                                    className={`${s['collapse-bundle-block__item-title']}`}
                                >
                                    {product?.name}
                                </div>
                                {content?.showSku ? (
                                    <div
                                        className={
                                            s[
                                                'collapse-bundle-block__item-title-sku'
                                            ]
                                        }
                                    >
                                        {product?.sku}
                                    </div>
                                ) : null}
                                {isTransactional ? (
                                    <ProductPrice
                                        className={`text-sm text-white ${s['collapse-bundle-block__price']}`}
                                        priceRange={product?.price_range}
                                    />
                                ) : null}
                            </a>
                            <div className={s['shop-now-wrapper']}>
                                <a
                                    href={productLink}
                                    className={s['shop-now-button']}
                                >
                                    {t('Shop now')}
                                </a>
                            </div>
                            <Button
                                aria-label={t(
                                    isTransactional
                                        ? ButtonLabel.ADD_TO_CART
                                        : ButtonLabel.LEARN_MORE_CAPITAL_CASE
                                )}
                                type="button"
                                className={cn(
                                    'corsair-links',
                                    s[
                                        'collapse-product-block__add-to-cart-button'
                                    ]
                                )}
                                onClick={handleOnCTAClick}
                                loading={false}
                            >
                                <span
                                    className={
                                        s[
                                            'collapse-product-block__icon-wrapper'
                                        ]
                                    }
                                >
                                    {isTransactional ? <CartIcon /> : null}
                                    {cartLabel}
                                </span>
                            </Button>
                        </div>
                    </div>
                )
            })}
        </div>
    )
}
