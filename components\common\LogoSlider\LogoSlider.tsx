import CorsairImage from '@corsairitshopify/corsair-image'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { VFC } from 'react'
import Slider from 'react-slick'
import { <PERSON><PERSON> } from '../Arrows'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import s from './LogoSlider.module.scss'

export interface LogoSliderInterface {
    content?: {
        backgroundColor?: string
        cloudinaryBackgroundImage?: CloudinaryImage[]
        heading?: string
        logos?: LogoType[]
        meta: {
            contentType: string
        }
        title: string
        pageBackgroundImage?: ImageType
        headingType: string
    }
}

export interface LogoType {
    colorImage: ImageType
    grayscaleImage: ImageType
    cloudinaryColorImage: CloudinaryImage[]
    cloudinaryGrayscaleImage: CloudinaryImage[]
}

const c = {
    container: `${s.container} mx-auto my-auto w-full`,
    heading: `${s.heading} text-white normal-case font-tomorrow`,
    logo: `${s.logo} absolute top-0 left-0 right-0 bottom-0 h-full`,
    logoSlider: `${s.logoSlider} bg-cover`,
    logoWrapper: `${s.logoWrapper} relative pb-full w-full border-solid`,
    slider: s.slider
}

const Logo: VFC<LogoType> = (logoImages): JSX.Element | null => {
    if (!logoImages) {
        return null
    }

    const { cloudinaryColorImage, cloudinaryGrayscaleImage } = logoImages

    return (
        <div className={c.logoWrapper}>
            <div className={c.logo}>
                {cloudinaryGrayscaleImage?.[0]?.secure_url && (
                    <CorsairImage
                        keepOrigin
                        height={cloudinaryGrayscaleImage?.[0]?.height || 360}
                        width={cloudinaryGrayscaleImage?.[0]?.width || 360}
                        src={convertUrlFormat(
                            cloudinaryGrayscaleImage?.[0]?.secure_url
                        )}
                    />
                )}
            </div>

            <div className={c.logo}>
                {cloudinaryColorImage?.[0]?.secure_url && (
                    <CorsairImage
                        keepOrigin
                        height={cloudinaryColorImage?.[0]?.height || 360}
                        width={cloudinaryColorImage?.[0]?.width || 360}
                        src={convertUrlFormat(
                            cloudinaryColorImage?.[0]?.secure_url
                        )}
                    />
                )}
            </div>
        </div>
    )
}

const LogoSlider: VFC<LogoSliderInterface> = ({
    content
}): JSX.Element | null => {
    if (!content) {
        return null
    }

    const {
        backgroundColor,
        cloudinaryBackgroundImage,
        heading,
        logos,
        title,
        headingType
    } = content

    if (!logos) {
        return null
    }

    const { length: logosLength } = logos

    const sliderSettings = {
        arrows: true,
        centerMode: true,
        centerPadding: '0px',
        dots: true,
        draggable: true,
        infinite: logosLength >= 4,
        nextArrow: <Arrows direction="right" s={s} />,
        prevArrow: <Arrows direction="left" s={s} />,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    centerMode: true,
                    centerPadding: '100px',
                    slidesToShow: 1
                }
            }
        ],
        slidesToShow: 4.5
    }

    const outputLogosList = logos?.map((element) => (
        <Logo key={title} {...element} />
    ))

    return (
        <section
            className={c.logoSlider}
            style={{
                backgroundImage: cloudinaryBackgroundImage?.[0]?.secure_url
                    ? `url(${convertUrlFormat(
                          cloudinaryBackgroundImage[0].secure_url
                      )})`
                    : '',
                backgroundColor: content.pageBackgroundImage
                    ? ''
                    : `#${backgroundColor}`
            }}
        >
            {heading && (
                <div className={c.container}>
                    {headingType === 'H1' ? (
                        <h1 className={c.heading}>{heading}</h1>
                    ) : (
                        <h2 className={c.heading}>{heading}</h2>
                    )}
                </div>
            )}
            {logos && <Slider {...sliderSettings}>{outputLogosList}</Slider>}
        </section>
    )
}

export default LogoSlider
