import { FC, HTMLAttributes } from 'react'

interface Props extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
    color?: string
}

const ChevronDown: FC<Props> = ({
    width = 15,
    height = 9,
    color = '#292E33',
    ...props
}) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 15 9"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.04289 0.60637C1.43342 0.215845 2.06658 0.215845 2.45711 0.60637L7.75 5.89926L13.0429 0.60637C13.4334 0.215845 14.0666 0.215845 14.4571 0.60637C14.8476 0.996894 14.8476 1.63006 14.4571 2.02058L8.45711 8.02058C8.06658 8.41111 7.43342 8.41111 7.04289 8.02058L1.04289 2.02058C0.652369 1.63006 0.652369 0.996894 1.04289 0.60637Z"
            fill={color}
        />
    </svg>
)

export default ChevronDown
