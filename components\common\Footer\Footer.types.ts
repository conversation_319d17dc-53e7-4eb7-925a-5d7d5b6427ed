import { CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface ColumnType {
    title: string
    items: CTAType[]
    type: string
}

export interface NewsletterType {
    title: string
    description: CTAType[]
}

export interface SocialLinkType {
    cloudinaryImage: CloudinaryImage[]
    title: string
    url: string
    newTab: boolean
}

export interface FooterData {
    columns: ColumnType[]
    copyrightLinks: CTAType[]
    copyrightText: string
    indentifier: string
    newsletter: NewsletterType
    showCookieSettings: boolean
    sociallinks: SocialLinkType[]
    title: string
}
