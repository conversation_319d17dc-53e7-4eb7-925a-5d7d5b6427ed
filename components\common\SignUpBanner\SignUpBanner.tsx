import { ChangeEvent, FC, useState } from 'react'
import Newsletter from '../Newsletter/Newsletter'
import s from './SignUpBanner.module.scss'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { useRouter } from 'next/router'
import { FieldError } from 'react-hook-form'
export interface SignUpBannerContent {
    headline: string
    title: string
    description: string
    emailPlaceholder: string
    buttonLabel: string
    agreePolicyText: string
    redirectLink: string
    desktopBackground: CloudinaryImage[]
    mobileBackground: CloudinaryImage[]
}

interface Props {
    content: SignUpBannerContent
}

const SignUpBanner: FC<Props> = ({ content }) => {
    const {
        headline,
        title,
        description,
        emailPlaceholder,
        buttonLabel,
        agreePolicyText,
        redirectLink,
        desktopBackground,
        mobileBackground
    } = content
    const [isAgreeTerm, setIsAgreeTerm] = useState(false)
    const [error, setError] = useState<FieldError | undefined>(undefined)
    const [formSubmitSuccess, setFormSubmitSuccess] = useState(false)
    const isMobile = useMediaQuery('(max-width: 767px)')
    const handleUpdateAgreeTerm = (e: ChangeEvent<HTMLInputElement>) => {
        setIsAgreeTerm(e.target.checked)
    }
    const router = useRouter()
    const handleFormSubmitSuccess = () => {
        setFormSubmitSuccess(true)
        router.push(redirectLink)
    }

    return (
        <div
            className={s.container}
            style={{
                backgroundImage: `url(${
                    isMobile
                        ? mobileBackground?.[0]?.secure_url
                        : desktopBackground?.[0]?.secure_url
                })`
            }}
        >
            <div className={s.contentWrapper}>
                <div className={s.main}>
                    <h5 className={s.headline}>{headline}</h5>
                    <span className={s.title}>{title}</span>
                    <p className={s.description}>{description}</p>
                </div>

                <div className={s.signUpForm}>
                    <Newsletter
                        placeholder={emailPlaceholder}
                        actionLabel={buttonLabel}
                        disabledSubmit={!isAgreeTerm}
                        cbWhenFormSubmitSuccess={handleFormSubmitSuccess}
                        onError={(value: FieldError | undefined) => {
                            setError(value)
                        }}
                        validateMode="onSubmit"
                    />
                    {formSubmitSuccess ? null : (
                        <div
                            className={s.CheckBoxWrapper}
                            style={{
                                marginTop: error ? 26 : 0
                            }}
                        >
                            <input
                                id="checkbox-signup-banner"
                                aria-required="true"
                                type="checkbox"
                                onChange={handleUpdateAgreeTerm}
                                className="sr-only"
                            />
                            <label
                                htmlFor="checkbox-signup-banner"
                                className={s.policyCheckbox}
                            >
                                <span className="sr-only">
                                    {agreePolicyText}
                                </span>
                            </label>
                            <p>{agreePolicyText}</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default SignUpBanner
