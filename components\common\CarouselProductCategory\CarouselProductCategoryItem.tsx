import { useBaseUrl } from '@config/hooks/useBaseUrl'
import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { CSSProperties, FC } from 'react'
import { ProductsSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselProductCategory.module.scss'
import Link from 'next/link'

export type CarouselProductCategoryItemProps = {
    product: ProductsSlideType
}

const PRIMARY_COLOR = 'var(--primary)'

const CarouselProductCategoryItem: FC<CarouselProductCategoryItemProps> = ({
    product
}) => {
    const { t } = useTranslation(['common'])

    const { region, language } = useBaseUrl(
        typeof window !== 'undefined' ? window.location.href : ''
    )

    const generateUrl = (identifier = '') => {
        return identifier.replace(/\s+/g, '_').replace(/\./g, '')
    }
    return (
        <div
            className={cn(s['slider-item-container'])}
            style={
                {
                    backgroundColor:
                        product?.backgroundColor ||
                        'var(--secondary-off-black)',
                    '--img-bg-color':
                        product.imageBackgroundColor ||
                        'var(--tertiary-light-gray)'
                } as CSSProperties
            }
        >
            <div
                className={cn(
                    s['slider-item-image-container'],
                    {
                        [s['lg-img']]: product.horizonPadding == 'none'
                    },
                    'relative w-full h-full'
                )}
            >
                <Link
                    href={`${generateUrl(
                        product.identifier
                    )}/#page-title-heading`}
                >
                    <a>
                        <CorsairImage
                            keepOrigin
                            src={convertUrlFormat(
                                product.cloudinaryMainImage?.[0]?.secure_url
                            )}
                            alt={t(
                                `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                            )}
                            layout="fill"
                            objectFit="contain"
                        />
                    </a>
                </Link>
            </div>
            <div
                className={cn(s['slider-item-text-block'], {
                    [s['noHorizonPadding']]: product.horizonPadding == 'none'
                })}
            >
                <h5
                    className={s['slider-item-heading']}
                    style={{
                        color: product.headingColor ?? 'var(--white)'
                    }}
                >
                    {product.heading}
                </h5>

                {product?.description && (
                    <p
                        className={s['slider-item-description']}
                        style={{
                            color: product?.textColor || 'var(--white)'
                        }}
                        dangerouslySetInnerHTML={{
                            __html: product.description
                        }}
                    />
                )}

                {product?.productPrice && (
                    <p
                        style={{
                            color: product?.textColor || 'var(--white)'
                        }}
                        className={s['slider-item-sub-desciption']}
                    >
                        {product.productPrice}
                    </p>
                )}
            </div>
        </div>
    )
}

export default CarouselProductCategoryItem
