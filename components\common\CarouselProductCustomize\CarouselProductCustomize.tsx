/* eslint-disable i18next/no-literal-string */

import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import { generateRandomString } from 'helpers/stringHelper'
import { useTranslation } from 'next-i18next'
import { ReactNode, useMemo, useRef, useState } from 'react'
import { A11y, Navigation, Pagination } from 'swiper'
import { Swiper, SwiperProps, SwiperSlide } from 'swiper/react'
import { CtaButton } from '../Carousel/Carousel'
import {
    CarouselWrapperType,
    ProductCustomizeType
} from '../CarouselWrapper/CarouselWrapper.types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { JsonToCss } from '../HeroBanner/Util/JsonToCss'
import { IMeta } from '../types'
import s from './CarouselProductCustomize.module.scss'
import CarouselProductCustomizeItem from './CarouselProductCustomizeItem'

export type CslData = ProductCustomizeType[]

type CarouselProductCustomizeProps = {
    content: CarouselWrapperType
    renderSlideItem?: (prod: ProductCustomizeType) => ReactNode
    overrideCarouselConfig?: SwiperProps
    classNames?: string[]
}

type DesktopMobileImage = {
    desktopProductTitle: string
    desktopProductDescription: string
    mobileProductTitle: string
    desktop: ImageComponent
    mobile: ImageComponent
    ctaButton?: CtaButton
}

type ImageComponent = {
    title: string
    image: ImageType
    url: string
    cloudinaryImage?: CloudinaryImage[]
}

export type CSLObject = {
    productImages: DesktopMobileImage[]
    identifier: string
    heading: string
    cloudinaryLogo: CloudinaryImage[]
    cloudinaryMainImage: CloudinaryImage[]
}

export type CslContainerTiles = {
    identifier: string
    productSlide: CSLObject[]
    copyBlock?: { subHeading?: string; heading?: string; text?: string }
    meta: IMeta<'carouselppContainer'>
    displayType: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryMobileBackgroundImage?: CloudinaryImage[]
    backgroundColor?: string
    desktopPaddingTop?: string
    desktopPaddingBottom?: string
    mobilePaddingTop?: string
    mobilePaddingBottom?: string
}

const ARROW_COLOR = 'var(--secondary-off-black)'

const CarouselProductCustomize = ({
    content: carouselContainer,
    renderSlideItem,
    overrideCarouselConfig,
    classNames
}: CarouselProductCustomizeProps): JSX.Element | null => {
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const isMobile = useMediaQuery('(max-width: 767px)')
    const [isStart, setIsStart] = useState(true)
    const [isEnd, setIsEnd] = useState(false)
    const { t } = useTranslation(['common'])
    const formattedName = useMemo(() => {
        return generateRandomString(10)
    }, [])
    const paddingsClassheading = `paddingClass-${formattedName}`

    const carouselConfig: SwiperProps = useMemo(() => {
        return {
            modules: [Navigation, Pagination, A11y],
            mousewheel: true,
            speed: 500,
            spaceBetween: 10,
            slidesPerGroup: 1,
            slidesPerView: 1.2,
            // slidesOffsetBefore: 20,
            // slidesOffsetAfter: 20,
            navigation: {
                prevEl: `.${formattedName}-prev`,
                nextEl: `.${formattedName}-next`,
                disabledClass: '.disabled:opacity-50'
            },
            onBreakpoint: (swiper: any) => {
                animatePagination(swiper)
            },
            onSlideChange: (swiper: any) => {
                animatePagination(swiper)
                handleSlideChange(swiper)
            },
            // onReachBeginning(swiper) {
            //     handleSlideChange(swiper)
            // },
            // onReachEnd(swiper) {
            //     handleSlideChange(swiper)
            // },
            // rewind: true,
            pagination: {
                el: `.${formattedName}-pagination`,
                clickable: true,
                renderBullet: (idx: number, className: string) => {
                    return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                }
            },
            breakpoints: {
                // 500: {
                //     slidesPerView: 1.3,
                //     slidesPerGroup: 1,
                //     // slidesOffsetBefore: 80,
                //     // slidesOffsetAfter: 80,
                //     spaceBetween: 20
                // },
                768: {
                    slidesPerView: 2.2,
                    slidesPerGroup: 1,
                    // slidesOffsetBefore: 80,
                    // slidesOffsetAfter: 80,
                    spaceBetween: 20
                },
                1024: {
                    slidesPerView: 3.2,
                    slidesPerGroup: 1,
                    // slidesOffsetBefore: 80,
                    // slidesOffsetAfter: 80,
                    spaceBetween: 20
                },
                1440: {
                    slidesPerView: 4.2,
                    slidesPerGroup: 1,
                    // slidesOffsetBefore: 80,
                    // slidesOffsetAfter: 80,
                    spaceBetween: 20
                }
            },
            onSwiper: (swiper: any) => {
                setIsEnd(swiper.isEnd)
            },
            lazy: true,
            ...overrideCarouselConfig
        }
    }, [overrideCarouselConfig, formattedName])

    if (!carouselContainer) return null

    const productSlides: CslData = carouselContainer.productSlide as ProductCustomizeType[]

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: any) => {
        setIsStart(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
        animatePagination(swiper)
    }

    const stylesDesktop = {
        ...(carouselContainer?.desktopPaddingTop && {
            'padding-top': `${carouselContainer?.desktopPaddingTop}rem;`
        }),
        ...(carouselContainer?.desktopPaddingBottom && {
            'padding-bottom': `${carouselContainer?.desktopPaddingBottom}rem;`
        }),
        ...(carouselContainer?.cloudinaryBackgroundImage && {
            background: `no-repeat center center / cover url(${carouselContainer?.cloudinaryBackgroundImage?.[0].secure_url});`,
            'background-size': 'cover'
        }),
        ...(carouselContainer?.backgroundColor && {
            background: `${carouselContainer?.backgroundColor};`
        })
    }

    const stylesMobile = {
        ...(carouselContainer?.mobilePaddingTop && {
            'padding-top': `${carouselContainer?.mobilePaddingTop}rem;`
        }),
        ...(carouselContainer?.mobilePaddingBottom && {
            'padding-bottom': `${carouselContainer?.mobilePaddingBottom}rem;`
        }),
        ...(carouselContainer?.cloudinaryMobileBackgroundImage && {
            background: `no-repeat center center / cover url(${carouselContainer?.cloudinaryMobileBackgroundImage?.[0].secure_url});`,
            'background-size': 'cover'
        }),
        ...(carouselContainer?.backgroundColor && {
            background: `${carouselContainer?.backgroundColor};`
        })
    }

    return (
        <div
            id={carouselContainer?.identifier}
            className={cn(
                `pt-8 pb-4 md:pt-16 md:pb-8 verticalPaddings-${paddingsClassheading}`,
                classNames?.join(' ')
            )}
        >
            <style jsx>{`
                @media screen and (min-width: 768px){
                    .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                stylesDesktop
            )}
                }
                @media screen and (max-width: 767px) {
                    .verticalPaddings-${paddingsClassheading} ${JsonToCss(
                stylesMobile
            )}
                }
            `}</style>

            <div
                className={cn(
                    s['product-customize-top-container'],
                    'product-customize-top-container'
                )}
            >
                {carouselContainer?.heading && (
                    <h3
                        className={cn(s['header'], 'header')}
                        style={{ color: carouselContainer?.headingColor }}
                    >
                        {carouselContainer?.heading}
                    </h3>
                )}
            </div>

            <div
                className={cn(
                    s['product-customize-swiper-container'],
                    'product-customize-swiper-container',
                    'overflow-hidden'
                )}
            >
                {/* {!isStart && <div className={cn(s['faded-left'])} />}
                    {!isEnd && <div className={cn(s['faded-right'])} />} */}
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['prev'],
                        `${formattedName}-prev`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                        // { ['hide-button']: isStart }
                    )}
                    aria-label={t('previous')}
                >
                    <ArrowRightIcon />
                </button>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['next'],
                        'scuf-button-primary',
                        'scuf-show-right-arrow',
                        `${formattedName}-next`
                        // { ['hide-button']: isEnd }
                    )}
                    aria-label={t('next')}
                >
                    <ArrowRightIcon />
                </button>

                <div className={cn(s['faded-left'])} />
                <div className={cn(s['faded-right'])} />

                <Swiper
                    className={cn(
                        s['swiper-container'],
                        'swiper-container'
                        // isEnd && s['last-item-reached'],
                        // (!isEnd || isStart) && s['first-item-reached']
                    )}
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    {...carouselConfig}
                >
                    {productSlides.map((prod, key) => (
                        <SwiperSlide key={key}>
                            {renderSlideItem ? (
                                renderSlideItem(prod)
                            ) : (
                                <CarouselProductCustomizeItem product={prod} />
                            )}
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            {isMobile && (
                <div
                    className={cn(
                        s['pagination-container'],
                        'pagination-container',
                        s['pagination-container-mobile'],
                        'pagination-container-mobile'
                    )}
                    ref={containerRef}
                >
                    <div className="relative inline-block md:hidden">
                        <div
                            ref={paginationRef}
                            className={`${formattedName}-pagination flex gap-4`}
                        />
                        <div className="dots-line" />
                        <div
                            ref={activeDotRef}
                            className="active-animated-dot"
                        />
                    </div>
                </div>
            )}
        </div>
    )
}

export default CarouselProductCustomize
