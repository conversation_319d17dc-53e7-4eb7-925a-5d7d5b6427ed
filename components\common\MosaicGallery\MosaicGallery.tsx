import cn from 'classnames'
import Link from 'next/link'
import { FC } from 'react'
import { ChevronRight } from 'react-feather'
import BaseCarousel from '../CrosssellCarousel/BaseCarousel'
import styles from './MosaicGallery.module.scss'
import {
    MosaicGalleryBlock,
    MosaicGalleryContent,
    MosaicGalleryProps
} from './MosaicGallery.types'

const PRIMARY_COLOR = 'var(--primary)'

const Item: FC<MosaicGalleryBlock> = (props) => {
    const { image, description = '', title, button } = props

    return (
        <div className={styles.MosaicGalleryItem}>
            <div className={styles.ImageWrapper}>
                <img
                    src={image?.[0]?.secure_url}
                    alt={image?.[0]?.context?.custom?.alt || ''}
                    loading="lazy"
                />
            </div>
            <div className={styles.Content}>
                <h5 aria-label={title}>{title}</h5>
                {description && <p aria-label={description}>{description}</p>}
                {button && (
                    <Link href={button?.url}>
                        <a>
                            <span> {button?.displayText} </span>
                            <ChevronRight
                                color={PRIMARY_COLOR}
                                className={styles.Icon}
                                size={15}
                            />
                        </a>
                    </Link>
                )}
            </div>
        </div>
    )
}

const getCssVariables = (props: MosaicGalleryContent) => {
    const {
        backgroundColor,
        backgroundImageDesktop,
        backgroundImageMobile,
        titleColor
    } = props

    const bgImageDesktop = backgroundImageDesktop?.[0]?.secure_url
    const bgImageMobile =
        backgroundImageMobile?.[0]?.secure_url || bgImageDesktop

    return {
        '--mosaic-title-color': titleColor || 'var(--primary)',
        '--mosaic-bg-color': backgroundColor || 'var(--tertiary-rick-black)',
        '--mosaic-bg-image-desktop': bgImageDesktop
            ? `url(${bgImageDesktop})`
            : undefined,
        '--mosaic-bg-image-mobile': bgImageMobile
            ? `url(${bgImageMobile})`
            : undefined
    } as React.CSSProperties
}

const MosaicGallery: FC<MosaicGalleryProps> = ({ contents }) => {
    const { blocks, title, showRearShadow } = contents
    const displayCarousel = blocks.length > 3

    const cssVariables = getCssVariables(contents)

    return (
        <section
            className={cn(styles.MosaicGalleryWrapper)}
            style={cssVariables as React.CSSProperties}
        >
            <div className="">
                {!!title && <h3 className={styles.SectionTitle}>{title}</h3>}
                <BaseCarousel
                    className={cn(styles.MosaicCarousel, {
                        [styles.HidePseudo]: !displayCarousel
                    })}
                    swiperProps={{
                        breakpoints: {
                            768: {
                                slidesPerView: 2.2,
                                slidesPerGroup: 1,
                                spaceBetween: 20
                            },
                            1024: {
                                slidesPerView: 3,
                                slidesPerGroup: 1,
                                spaceBetween: 20
                            },
                            1440: {
                                slidesPerView: 4,
                                slidesPerGroup: 1,
                                spaceBetween: 20
                            }
                        }
                    }}
                    list={blocks.map((block) => ({
                        ...block,
                        id: block.title
                    }))}
                    showRearShadow={showRearShadow}
                    renderItem={(data) => <Item {...data} />}
                />
            </div>
        </section>
    )
}

export default MosaicGallery
