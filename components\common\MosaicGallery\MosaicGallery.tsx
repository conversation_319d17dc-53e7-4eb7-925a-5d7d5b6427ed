import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import Link from 'next/link'
import { FC } from 'react'
import { ChevronRight } from 'react-feather'
import BaseCarousel from '../CrosssellCarousel/BaseCarousel'
import styles from './MosaicGallery.module.scss'
import {
    ImgElementStyle,
    MosaicGalleryBlock,
    MosaicGalleryContent,
    MosaicGalleryProps
} from './MosaicGallery.types'

const PRIMARY_COLOR = 'var(--primary)'

const Item: FC<MosaicGalleryBlock> = (props) => {
    const {
        image,
        description = '',
        title,
        button,
        keepOrigin,
        objectFit
    } = props

    return (
        <div className={styles.MosaicGalleryItem}>
            <div className={styles.ImageWrapper}>
                <Image
                    layout="fill"
                    src={image?.[0]?.secure_url}
                    keepOrigin={keepOrigin}
                    objectFit={objectFit as ImgElementStyle['objectFit']}
                />
            </div>
            <div className={styles.Content}>
                <h5>{title}</h5>
                {description && <p>{description}</p>}
                {button && (
                    <Link href={button?.url}>
                        <a>
                            <span> {button?.displayText} </span>
                            <ChevronRight
                                color={PRIMARY_COLOR}
                                className={styles.Icon}
                                size={15}
                            />
                        </a>
                    </Link>
                )}
            </div>
        </div>
    )
}

const getCssVariables = (props: MosaicGalleryContent) => {
    const {
        backgroundColor,
        backgroundImageDesktop,
        backgroundImageMobile,
        titleColor
    } = props

    return {
        '--mosaic-bg-color': backgroundColor || '',
        '--mosaic-bg-image-desktop': backgroundImageDesktop
            ? `url(${backgroundImageDesktop?.[0]?.secure_url})`
            : 'none',
        '--mosaic-bg-image-mobile': backgroundImageMobile
            ? `url(${backgroundImageMobile?.[0]?.secure_url})`
            : 'none',
        '--mosaic-title-color': titleColor || ''
    }
}

const MosaicGallery: FC<MosaicGalleryProps> = ({ contents }) => {
    const { blocks, title } = contents
    const displayCarousel = blocks.length > 3

    const cssVariables = getCssVariables(contents)

    return (
        <section
            className={cn(styles.MosaicGalleryWrapper)}
            style={cssVariables as React.CSSProperties}
        >
            <div className="scuf-container">
                <h4 className={styles.SectionTitle}>{title}</h4>
                <BaseCarousel
                    className={cn(styles.MosaicCarousel, {
                        [styles.HidePseudo]: !displayCarousel
                    })}
                    swiperProps={{
                        slidesPerGroup: 1,
                        slidesPerView: 1.2,
                        spaceBetween: 20,
                        breakpoints: {
                            768: {
                                slidesPerView: 3,
                                slidesPerGroup: 1
                            }
                        }
                    }}
                    list={blocks.map((block) => ({
                        ...block,
                        id: block.title
                    }))}
                    renderItem={(data) => <Item {...data} />}
                />
            </div>
        </section>
    )
}

export default MosaicGallery
