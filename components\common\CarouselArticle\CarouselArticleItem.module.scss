.slider-item-container {
    @apply flex flex-col h-full cursor-pointer;

    .slider-item-text-block {
        @apply flex flex-col;
        row-gap: 12px;
        padding-top: 20px;

        .slider-item-heading {
            @apply font-sofiaSans font-medium;
            font-size: 1.25rem;
            line-height: 1.5rem;
        }
    }

    .slider-item-image-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; // 16:9 aspect ratio
        overflow: hidden;
    }

    .article-link {
        display: block;
        text-decoration: none;
    }

    &.show-animation {
        transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        &:hover {
            transform: translateY(-5px);

            .slider-item-image-container img {
                transform: scale(1.05);
            }
        }

        .slider-item-image-container {
            img {
                transition: transform 0.45s cubic-bezier(0.2, 0, 0.2, 1);
                transform-origin: center center;
            }
        }
    }
}
