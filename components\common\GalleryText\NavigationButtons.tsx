import cn from 'classnames'
import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import s from './GalleryText.module.scss'
import { useTranslation } from 'next-i18next'

interface NavigationButtonsProps {
    isStart: boolean
    isEnd: boolean
    formattedName: string
    position: 'default' | 'top'
}
function NavigationButtons(props: NavigationButtonsProps) {
    const { isStart, isEnd, formattedName, position } = props

    const { t } = useTranslation(['common'])

    return (
        <>
            <button
                className={cn(
                    s['carousel-nav-button'],
                    s['prev'],
                    `${formattedName}-prev`,
                    position == 'default'
                        ? 'scuf-button-primary'
                        : 'scuf-button-dark',
                    'scuf-show-right-arrow',
                    {
                        [s['disabled']]: isStart && position == 'top',
                        ['hide-button']: isStart && position == 'default'
                    }
                )}
                aria-label={t('previous')}
            >
                <ArrowRightIcon color="var(--white)" />
            </button>
            <button
                className={cn(
                    s['carousel-nav-button'],
                    s['next'],
                    'scuf-show-right-arrow',
                    `${formattedName}-next`,
                    position == 'default'
                        ? 'scuf-button-primary'
                        : 'scuf-button-dark',
                    'scuf-show-right-arrow',
                    {
                        [s['disabled']]: isEnd && position == 'top',
                        ['hide-button']: isEnd && position == 'default'
                    }
                )}
                aria-label={t('next')}
            >
                <ArrowRightIcon color="var(--white)" />
            </button>
        </>
    )
}

export default NavigationButtons
