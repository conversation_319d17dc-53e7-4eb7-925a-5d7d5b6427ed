import { GetStaticPropsContext } from 'next'
import Head from 'next/head'
import React, { useMemo, useRef, useState } from 'react'
import { Layout } from '@components/common'
import { SEO } from '@corsairitshopify/corsair-seo'
import {
    CartItemInput,
    ConfigurableVariant,
    Maybe
} from '@pylot-data/pylotschema'
import serverSideTranslations from '@pylot-data/serverSideTranslations'
import { useTranslation } from 'next-i18next'
import getGiftcard from '@pylot-data/api/operations/get-giftcard'
import s from '@pagestyles/GiftcardPDP.module.scss'
import Image from '@corsairitshopify/corsair-image'
import { getIsValidEmailAddress } from '@corsairitshopify/pylot-utils'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { ToastType, useUI } from '@corsairitshopify/pylot-ui/context'
import { useCartItemHelper } from 'helpers/cartItemHelper'
import { ClickFrom } from '@pylot-data/enums/ClickFromEnum.d'
import { GiftcardDesignSelector } from '@components/GiftcardScreen/GiftcardDesignSelector'
import { GiftcardDenominationSelector } from '@components/GiftcardScreen/GiftcardDenominationSelector'
import { GiftcardRecipientSelector } from '@components/GiftcardScreen/GiftcardRecipientSelector'
import {
    ContentJson,
    ContentJsonItem,
    useContentJson
} from '@pylot-data/hooks/contentful/use-content-json'
import { ContentPage } from '@components/common/ContentPage/ContentPage'
import getContentJson from '@pylot-data/api/operations/get-content-json'
import { PreviewData } from '@components/common/types'
import { BreadcrumbsPage } from '@components/common/BreadcrumbsPage/BreadcrumbsPage'

const GIFT_CARD_IDENTIFIER = 'Gift Card'
const GIFT_CARD_CONTENT_TYPE = 'giftcard'

export async function getStaticProps({
    locale,
    preview,
    previewData
}: GetStaticPropsContext) {
    const previewDate = previewData as PreviewData
    const product = await getGiftcard(locale || 'en')
    const { data } = await getContentJson<ContentPage>({
        queryVariables: {
            identifier: [GIFT_CARD_IDENTIFIER],
            contentType: GIFT_CARD_CONTENT_TYPE
        },
        previewDate: previewDate?.time,
        isPreview: preview,
        locale
    })

    return {
        props: {
            ...(await serverSideTranslations(locale!, ['common'])),
            product,
            pageContent: data
        },

        revalidate: 900
    }
}

const getUniqueOptions = (
    data: Maybe<ConfigurableVariant>[] | undefined,
    optionType: string
): string[] => {
    if (!data) return []
    const values = data.flatMap((variant) =>
        variant
            ?.selectedOptions!.filter((o) => o!.name === optionType)
            .map((o) => o!.value!)
    )
    return [...new Set(values)] as string[]
}

const getUniqueDesignVariants = (
    variants: Maybe<ConfigurableVariant>[] | undefined,
    designTexts: string[]
): ConfigurableVariant[] => {
    if (!variants) return []
    const list: ConfigurableVariant[] = []
    designTexts.forEach((design) => {
        const found = variants.find((v) =>
            v?.selectedOptions?.some((o) => o?.value === design)
        )
        if (found) list.push(found as ConfigurableVariant)
    })
    return list
}

interface GiftCardContentfulData {
    title: string
    topContentEntries?: ContentJsonItem<ContentPage>
    designTitle: string
    amountTitle: string
    emailTitle: string
    emailPlaceholder: string
    description?: string
}
export default function GiftcardPage({ product, pageContent }: any) {
    const { t } = useTranslation(['common'])
    const variants = product?.variants || []
    const denominationOptions = useMemo(
        () => getUniqueOptions(variants, 'Denominations'),
        [variants]
    )
    const { data, error } = useContentJson<GiftCardContentfulData>(
        {
            identifier: [GIFT_CARD_IDENTIFIER],
            contentType: GIFT_CARD_CONTENT_TYPE,
            options: {
                level: 5
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            initialData: { data: pageContent }
        },
        {}
    )
    const contentfulData = data ? data[0]?.parsedEntries : null

    const designTexts = useMemo(() => getUniqueOptions(variants, 'Design'), [
        variants
    ])
    const designOptions = useMemo(
        () => getUniqueDesignVariants(variants, designTexts),
        [variants, designTexts]
    )
    const [
        selectedDesign,
        setSelectedDesign
    ] = useState<ConfigurableVariant | null>(designOptions[0] || null)
    const [selectedDenomination, setSelectedDenomination] = useState(
        denominationOptions[0] || ''
    )
    const [selectedEmail, setSelectedEmail] = useState('')
    const [isLoading, setIsLoading] = useState(false)

    const formRef = useRef<HTMLFormElement>(null)
    const updateCartItemsLocalStorage = useCartItemHelper()
    const { openSidebar, openCartToast } = useUI()
    const { addToCart } = useAddToCart()
    const TIMEOUT_AFTER_OPENING_SIDEBAR = 300

    const addGiftcard = async () => {
        if (!variants?.length || !selectedDesign) return
        setIsLoading(true)

        const designName = selectedDesign.selectedOptions?.find(
            (o) => o?.name === 'Design'
        )?.value

        const variant = variants.find(
            (v: ConfigurableVariant) =>
                v.selectedOptions?.some((o) => o?.value === designName) &&
                v.selectedOptions?.some(
                    (o) => o?.value === selectedDenomination
                )
        ) as ConfigurableVariant | undefined

        if (!variant) {
            setIsLoading(false)
            return
        }

        updateCartItemsLocalStorage(variant, ClickFrom.PDP)

        const uid = (variant.id as string)?.split('/').pop() || ''
        const payload: CartItemInput[] = [
            {
                sku: variant.sku as string,
                uid,
                max_quantity: 1,
                quantity: 1,
                recipient_email: selectedEmail,
                design_option: designName
            }
        ]

        // TODO: Check if add sku work or not ?
        const res = await addToCart(payload, [
            {
                ...product,
                sku: variant.sku as string,
                price_range: { minimum_price: 0 },
                name: product.title,
                thumbnail: {
                    url: variant.image?.url
                },
                image: {
                    url: variant.image?.url
                },
                small_image: {
                    url: variant.image?.url
                },
                qty_limit: 1
            }
        ])
        const errMsg =
            res?.errors?.[0]?.message ?? res?.user_errors?.[0]?.message

        if (errMsg) {
            setTimeout(
                () => openCartToast(errMsg, ToastType.Warning),
                TIMEOUT_AFTER_OPENING_SIDEBAR
            )
        } else {
            openSidebar()
        }
        setIsLoading(false)
    }

    if (!variants?.length) return <></>

    return (
        <div className="relative">
            <Head>
                <title>{t('Gift Cards')}</title>
            </Head>
            <SEO descriptionOverride={t('gift-card-meta||gift-card-meta')} />
            {contentfulData?.title && (
                <div className={s['breadcrumbs']}>
                    <BreadcrumbsPage
                        separator=" / "
                        rootText={t('breadcrumbs|Home')}
                        slug={[contentfulData?.title]}
                    />
                </div>
            )}

            {contentfulData?.topContentEntries && (
                <ContentPage pageContent={contentfulData?.topContentEntries} />
            )}
            <div className={s.container} id="gift-card-main">
                <div className={s['gallery-wrapper']}>
                    <div className={s.gallery}>
                        {selectedDesign?.image?.url && (
                            <Image
                                src={
                                    selectedDesign?.variant_image_url?.value ||
                                    selectedDesign?.image?.url
                                }
                                keepOrigin
                                layout="fill"
                                objectFit="cover"
                                width={750}
                                height={750}
                            />
                        )}
                    </div>
                </div>

                <div className={s.main}>
                    <GiftcardDesignSelector
                        options={designOptions}
                        value={selectedDesign}
                        onChange={setSelectedDesign}
                        designTitle={contentfulData?.designTitle}
                    />

                    <GiftcardDenominationSelector
                        options={denominationOptions}
                        value={selectedDenomination}
                        onChange={setSelectedDenomination}
                        amountTitle={contentfulData?.amountTitle}
                    />

                    <GiftcardRecipientSelector
                        formRef={formRef}
                        email={selectedEmail}
                        setEmail={setSelectedEmail}
                        isLoading={isLoading}
                        onAddGiftcard={addGiftcard}
                        getIsValidEmailAddress={getIsValidEmailAddress}
                        emailTitle={contentfulData?.emailTitle}
                        emailPlaceholder={contentfulData?.emailPlaceholder}
                    />

                    {contentfulData?.description && (
                        <div
                            className={s.description}
                            dangerouslySetInnerHTML={{
                                __html: contentfulData.description
                            }}
                        />
                    )}
                </div>
            </div>
        </div>
    )
}

GiftcardPage.Layout = Layout
