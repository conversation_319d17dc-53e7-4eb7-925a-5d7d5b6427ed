import { FC } from 'react'
import cn from 'classnames'
import s from '@pagestyles/GiftcardPDP.module.scss'
import { Button } from '@corsairitshopify/pylot-ui'
import { ButtonLabel } from '@pylot-data/hooks/product/use-product-ui'
import { SubscribeInputVariables } from '@corsairitshopify/pylot-footer-newsletter'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'next-i18next'

interface Props {
    formRef: React.RefObject<HTMLFormElement>
    email: string
    setEmail: (email: string) => void
    isLoading: boolean
    onAddGiftcard: () => Promise<void>
    getIsValidEmailAddress: (email: string) => boolean | string
    emailTitle?: string
    emailPlaceholder?: string
}

export const GiftcardRecipientSelector: FC<Props> = ({
    formRef,
    email,
    setEmail,
    isLoading,
    onAddGiftcard,
    getIsValidEmailAddress,
    emailTitle,
    emailPlaceholder
}) => {
    const { t } = useTranslation(['common'])

    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitSuccessful }
    } = useForm<SubscribeInputVariables>({
        mode: 'onChange',
        defaultValues: { email }
    })

    const onSubmit = async () => {
        await onAddGiftcard()
    }

    const registerEmail = register('email', {
        validate: getIsValidEmailAddress
    })

    return (
        <>
            <div className={s['email-container']}>
                {emailTitle && <p className={s['title']}>{emailTitle}</p>}
                <form ref={formRef} onSubmit={handleSubmit(onSubmit)}>
                    <div
                        className={cn(
                            s.inputWrapper,
                            errors?.email && 'border-alert-danger',
                            'flex'
                        )}
                    >
                        <div className={s['email-container-input-wrapper']}>
                            <input
                                className={cn(s.emailInput, {
                                    [s['error-input']]: Boolean(errors.email),
                                    [s['success-input']]: isSubmitSuccessful
                                })}
                                type="text"
                                id="lucky-gammer"
                                placeholder={emailPlaceholder}
                                {...registerEmail}
                                onChange={(e) => {
                                    registerEmail.onChange(e)
                                    setEmail(e.target.value)
                                }}
                                disabled={false}
                                aria-invalid={Boolean(errors.email)}
                                aria-describedby="newsletter-label-message"
                            />
                        </div>
                        {errors.email && (
                            <small
                                className={cn(
                                    s['response-message'],
                                    s['response-message-error']
                                )}
                            >
                                {email.length > 0
                                    ? t(
                                          'Please enter a valid email address (Ex: <EMAIL>).'
                                      )
                                    : t('This is a required field.')}
                            </small>
                        )}
                    </div>
                    <div className={cn(s['price-add-to-cart'], s['pdp-price'])}>
                        <Button
                            type="submit"
                            className={s['add-to-cart-button']}
                        >
                            <span>{ButtonLabel.ADD_TO_CART}</span>
                        </Button>
                    </div>
                </form>
            </div>
        </>
    )
}
