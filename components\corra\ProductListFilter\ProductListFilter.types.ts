interface BaseContentJson {
    identifier: string
}

export interface ContentfulCloudinaryImageField {
    bytes?: number
    created_at?: string
    duration?: unknown
    format?: string
    height?: number
    metadata?: unknown
    original_secure_url?: string
    original_url?: string
    public_id?: string
    raw_transformation?: string
    resource_type?: string
    secure_url?: string
    tags?: unknown[]
    type?: string
    url: string
    version?: number
    width: number
}

export interface FilterExtraInforItem {
    description: string
    title: string
    media: ContentfulCloudinaryImageField[]
}

export interface FilterExtraInforBlock {
    items: FilterExtraInforItem[]
    title: string
    drawerTitle: string
}

export interface AdditionalInformationForFilterEntries
    extends Pick<BaseContentJson, 'identifier'> {
    title: string
    items: FilterExtraInforBlock[]
}
