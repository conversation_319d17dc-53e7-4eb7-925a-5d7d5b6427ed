import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { FC } from 'react'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import s from './HydroXBlock.module.scss'
import HydroXBlockItem, { HydroXCards } from './HydroXBlockItem'
export interface HydroXBlockButton {
    text: string
    url: string
    target: string
}

export interface HydroXBlockResponse {
    heading: string
    subTitle: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    card: HydroXCards[]
    shopButton: HydroXBlockButton
    startKit: HydroXBlockButton
    designLoop: HydroXBlockButton
    headingType: string
}

interface HydroXBlockProps {
    content: HydroXBlockResponse
}

const twClasses = /*tw*/ {
    mainHeading: 'flex justify-center text-5xl font-secondary',
    subTitle: 'flex justify-center font-semibold mt-6 text-center font-primary',
    containerClasses: 'flex justify-center flex-col md:flex-row mt-16',
    cardsColOne:
        'Cta-Cards flex flex-col justify-between pl-0 mt-8 md:mt-0 md:pl-8',
    startKit:
        'flex justify-center items-center font-semibold bg-black cursor-pointer',
    designLoop:
        'flex justify-center items-center font-semibold text-lg mt-8 md:mt-0 cursor-pointer relative',
    shopButton:
        'text-center flex justify-center items-center hover:text-lime-900/[.06] cursor-pointer',
    ctaBttonDiv:
        'text-black justify-center items-center font-semibold text-center bg-yellow-300 font-secondary',
    ctaButtonText: 'font-tomorrow inline-flex justify-center items-center'
}

export const HydroXBlock: FC<HydroXBlockProps> = ({ content }) => {
    const { t } = useTranslation(['common'])
    if (!content) {
        return null
    }
    const {
        heading,
        subTitle,
        card,
        cloudinaryBackgroundImage,
        headingType
    } = content
    const shopButton = content?.shopButton
    const designLoop = content?.designLoop
    const startKit = content?.startKit
    const cardCount: number = Math.ceil(card.length / 2)

    return (
        <div
            className={cn(
                s['Hydro-X-Block-Container'],
                'flex flex-col box-border relative'
            )}
        >
            {cloudinaryBackgroundImage?.[0]?.secure_url && (
                <div
                    className={cn(
                        s['Hydro-X-Bg-Image'],
                        'w-full absolute h-full left-0'
                    )}
                >
                    <CorsairImage
                        keepOrigin
                        layout="fill"
                        className="object-cover"
                        src={convertUrlFormat(
                            `${cloudinaryBackgroundImage[0]?.secure_url}`
                        )}
                        alt={correctImageAlt(
                            t(
                                `alt|${cloudinaryBackgroundImage[0]?.context?.custom?.alt}`
                            )
                        )}
                    />
                </div>
            )}
            <div className={cn(s['Hydro-X-Block'], 'gap-6 py-5')}>
                {headingType === 'H1' ? (
                    <h1
                        className={cn(
                            s['Hydro-X-Heading'],
                            twClasses.mainHeading
                        )}
                    >
                        {heading}
                    </h1>
                ) : (
                    <h2
                        className={cn(
                            s['Hydro-X-Heading'],
                            twClasses.mainHeading
                        )}
                    >
                        {heading}
                    </h2>
                )}
                {subTitle && (
                    <div
                        className={cn(
                            s['Hydro-X-Subtitle'],
                            twClasses.subTitle
                        )}
                    >
                        {subTitle}
                    </div>
                )}
                <div
                    className={cn(
                        s['Grid-container'],
                        twClasses.containerClasses
                    )}
                >
                    <div
                        className={cn(
                            s['Hydro-X-Col-1'],
                            `grid grid-cols-2 md:grid-cols-${cardCount}`
                        )}
                    >
                        {cardCount > 1 &&
                            card.map(
                                ({
                                    title,
                                    url,
                                    image,
                                    cloudinaryImage,
                                    newTab,
                                    heading,
                                    description
                                }) => (
                                    <HydroXBlockItem
                                        Key={title}
                                        title={title}
                                        url={url}
                                        image={cloudinaryImage}
                                        newTab={newTab}
                                        heading={heading}
                                        description={description}
                                    />
                                )
                            )}
                    </div>
                    <div
                        className={cn(
                            s['Hydro-X-Col-2'],
                            twClasses.cardsColOne
                        )}
                    >
                        {startKit && (
                            <div
                                className={cn(
                                    s['StartKit'],
                                    twClasses.startKit
                                )}
                            >
                                <Link href={startKit?.url || ''}>
                                    <a
                                        target={
                                            startKit?.target === 'Self'
                                                ? '_self'
                                                : '_blank'
                                        }
                                    >
                                        <span
                                            className={cn(
                                                s['hydroXblock-fonts'],
                                                'text-white font-secondary'
                                            )}
                                        >
                                            {startKit?.text}
                                        </span>
                                    </a>
                                </Link>
                            </div>
                        )}
                        {designLoop && (
                            <div
                                className={cn(
                                    s['designLoop'],
                                    twClasses.designLoop
                                )}
                            >
                                <Link href={designLoop?.url || ''}>
                                    <a
                                        target={
                                            designLoop?.target === 'Self'
                                                ? '_self'
                                                : '_blank'
                                        }
                                    >
                                        <span
                                            className={cn(
                                                s['Cta-Design'],
                                                'block text-white font-secondary text-center p-5'
                                            )}
                                        >
                                            {designLoop?.text}
                                        </span>
                                    </a>
                                </Link>
                            </div>
                        )}
                    </div>
                </div>

                {shopButton && (
                    <div
                        className={cn(
                            s['Cta-shopButton'],
                            twClasses.shopButton,
                            'mt-11'
                        )}
                    >
                        <Link href={shopButton?.url || ''}>
                            <a
                                target={
                                    shopButton?.target === 'Self'
                                        ? '_self'
                                        : '_blank'
                                }
                            >
                                <div
                                    className={cn(
                                        s['Cta-ButtonDiv'],
                                        twClasses.ctaBttonDiv,
                                        'inline-flex'
                                    )}
                                >
                                    <span
                                        className={cn(
                                            s['CtaButtonText'],
                                            twClasses.ctaButtonText
                                        )}
                                    >
                                        {shopButton?.text}
                                    </span>
                                </div>
                            </a>
                        </Link>
                    </div>
                )}
            </div>
        </div>
    )
}

export default HydroXBlock
