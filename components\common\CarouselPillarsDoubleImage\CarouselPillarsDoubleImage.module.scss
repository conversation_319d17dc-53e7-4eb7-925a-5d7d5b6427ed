.double-image {
    @screen md-max {
        padding-top: 0 !important;
        background: #f5f5f5 !important;
    }

    @screen md {
        height: auto;
        min-height: 668px;
    }

    .slider-item-cta {
        @apply font-primary font-medium;
        margin: auto;
        margin-top: 38px;
        padding: 0.923rem 1.6rem 0.723rem 1.6rem;
        line-height: 27.85px;
        gap: 12px;
        font-size: 18px;

        @screen lg {
            font-size: 16px;
        }

        @screen 2xl {
            font-size: 22px;
        }

        &:hover {
            .suffix-icon {
                [stroke="var(--secondary-off-black)"] {
                    stroke: var(--secondary-off-black);
                }

                [fill="var(--secondary-off-black)"] {
                    fill: var(--secondary-off-black);
                }
            }
        }

        .suffix-icon {
            [stroke="var(--secondary-off-black)"] {
                stroke: #f5f5f5;
            }

            [fill="var(--secondary-off-black)"] {
                fill: #f5f5f5;
            }
        }
    }

    .slider-item {
        &-image-container {
            @apply flex-shrink-0;
        }

        &-second-image-container {
            @apply z-0 absolute left-1/2 transform -translate-x-1/2;
            top: min(140px, 37.33vw);
            width: min(335px, 89.33vw);
            height: min(335px, 89.33vw);

            @screen md {
                top: 30%;
                width: min(230px, 27.57vw);
                height: min(230px, 27.57vw);
            }

            @screen lg {
                width: min(358px, 18.64vw);
                height: min(358px, 18.64vw);
                top: 32%;
            }

            @screen xl {
                top: 28%;
            }

            @screen 2xl {
                top: 22%;
            }
        }

        &-text-block {
            margin-top: min(73px, 19.46vw);

            @media screen and (min-width: 1024px) and (max-width: 1279px) {
                padding: 20px 15px;
            }
        }

        &-heading {
            @apply text-center;
        }

        &-description {
            @apply text-center overflow-hidden;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 5;
            line-clamp: 5;
            display: -webkit-box;
            height: 120px;
        }
    }
}