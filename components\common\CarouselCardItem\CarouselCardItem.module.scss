.MosaicGalleryItem {
    // height: 710px;
    @apply relative;

    .ImageWrapper {
        @apply relative;

        img {
            @apply w-full;
        }
    }

    .Content {
        z-index: 2;
        width: 100%;
        padding: 25px 20px;
        @apply absolute left-0 bottom-0 box-border;

        h5 {
            margin-bottom: 10px;
            font-style: normal;
            letter-spacing: 0;
            font-size: 1.5rem;
            line-height: 1.75rem;
            color: var(--white);
            @apply font-tomorrow font-medium uppercase;
        }

        p {
            color: var(--white);
            font-style: normal;
            font-size: 1rem;
            line-height: 1.5rem;
            white-space: pre-wrap;
            @apply font-sofiaSans font-normal;
        }

        a {
            gap: 5px;
            transition: color 0.3s ease;
            -webkit-transition: color 0.3s ease;
            -moz-transition: color 0.3s ease;
            -ms-transition: color 0.3s ease;
            margin-top: 10px;
            @apply font-medium font-tomorrow uppercase flex items-center;

            span {
                color: var(--primary);
                font-size: 1rem;
                line-height: 1rem;
            }

            .Icon {
                font-size: 15px;
            }
        }
    }
}
