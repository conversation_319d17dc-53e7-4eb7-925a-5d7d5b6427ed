.plp-root {
    padding: 0 20px;
    padding-bottom: 20px;

    @screen md {
        max-width: 1366px;
        margin-inline: auto;
    }

    [class*='filter-body'],
    [class*='filter-body'] ul li,
    [class*='filter-wrapper'],
    [class*='selected-items-clear-all'] {
        @apply bg-white;
        color: black !important;
    }

    [class*='filter-close-btn'] {
        fill: black;
    }

    [class*='label-checkbox']::after {
        border-color: black;
    }

    .ListWrapper {
        margin-bottom: 30px;
        @screen md {
            padding-left: 2%;
            @apply flex-1;
        }

        .ToolbarDesktop {
            margin-bottom: 20px;
            @apply flex justify-between items-center;

            .ProductCountTxt {
                font-size: 14px;
                line-height: 16.8px;
                padding-block: 10px;
                color: var(--secondary-off-black);
                width: max-content;
                @apply font-sofiaSans font-light;
            }

            .wrapper-dropdown {
                @apply hidden;

                @screen md {
                    gap: 7px;
                    @apply flex justify-between items-center;
                }

                .sort-label {
                    font-size: 14px;
                    line-height: 17px;
                    color: var(--secondary-off-black);
                    @apply font-sofiaSans font-thin;
                }

                span[role='button'][tabindex='0'] {
                    > button {
                        &:focus-visible {
                            outline: 4px solid #000;
                            outline-offset: 4px;
                        }
                    }
                }

                [class*='dropdown-options'] {
                    @apply bg-white;

                    ul {
                        li {
                            padding: 6px 10px;

                            &:first-of-type {
                                border-top-left-radius: 8px;
                                border-top-right-radius: 8px;
                            }

                            &:last-of-type {
                                border-bottom-left-radius: 8px;
                                border-bottom-right-radius: 8px;
                            }

                            button {
                                line-height: 24px;
                                font-size: 16px !important;
                                color: var(--secondary-off-black) !important;
                                font-weight: 400 !important;
                                text-align: left !important;
                                @apply font-sofiaSans;
                                &:focus-visible {
                                    outline: 2px solid
                                        var(--dark-bg-btn-primary) !important;
                                }
                            }

                            &:hover {
                                background-color: var(--light-grey);
                                button {
                                    color: var(--white) !important;
                                }
                            }
                        }

                        [class*='item-active'] {
                            background-color: var(--dark-bg-btn-primary);

                            button {
                                color: var(--white) !important;
                                &:focus-visible {
                                    outline: 2px solid
                                        var(--dark-bg-btn-primary) !important;
                                }
                            }
                        }
                    }
                }

                [class*='sort-label-selected'] {
                    height: 36px;
                    padding-inline: 10px;
                    width: 185px;
                    font-size: 14px;
                    line-height: 24px;
                    border-radius: 5px;
                    color: var(--secondary-off-black);
                    @apply font-normal font-sofiaSans justify-between bg-white;

                    &::after {
                        border: solid var(--secondary-off-black);
                        border-width: 0 2px 2px 0;
                        width: 8px;
                        height: 8px;
                        border-radius: 2px;
                        transform: rotate(45deg) translateX(-3px);
                        -webkit-transform: rotate(45deg) translateX(-3px);
                    }
                }

                span > button {
                    width: 100%;

                    span {
                        border-color: var(--colspan-light-bg) !important;
                    }
                }
            }
        }
    }

    .LeftFilter {
        padding-right: 2%;
        @apply w-1/4 flex flex-col;

        @screen md-max {
            @apply w-full;
        }

        .FilterInfo {
            @screen md-max {
                @apply hidden;
            }

            .FilterSubTitle {
                padding: 8px 0;
                font-size: 18px;
                line-height: 1.2;
                color: var(--secondary-off-black);
                @apply font-sofiaSans uppercase font-semibold;
            }

            .FilterDescription {
                color: var(--secondary-off-black);
                font-style: normal;
                line-height: 16px;
                font-size: 12px;
                margin: 2px 0 10px;
                @apply font-sofiaSans font-normal;
            }
        }
    }

    .ToolBarHeader {
        padding-block: 8px;
        border-bottom: 1px solid #dbdbdb;
        @apply flex items-center px-0;

        @screen md {
            @apply hidden;
        }

        .BtnWrapper {
            @apply flex-1 flex;
        }

        .FilterToggleBtn {
            gap: 8px;
            font-size: 18px;
            line-height: 22px;
            @apply flex items-center font-sofiaSans
                font-semibold cursor-pointer flex-1 justify-center uppercase;
        }

        .SortContainer {
            @apply flex flex-1 justify-between;

            .MobileSort {
                gap: 8px;
                border-left: 1px solid #dbdbdb !important;
                line-height: 22px;
                letter-spacing: 0.25px;
                @apply flex items-center font-sofiaSans justify-center w-full font-semibold uppercase;
            }
        }
    }
}

.search-header {
    font-size: 1.6rem;
    letter-spacing: 2.28px;
    line-height: 35px;
    margin-block: 20px;

    @screen md {
        @apply inline-block overflow-ellipsis overflow-hidden;
        max-height: 200px;
        margin-block: 25px;
    }
}

.empty-search > div {
    color: black;
}

.btn-label {
    height: 48px;
    font-size: 14px;
    font-weight: 400;
    background-color: #4c4c4d !important;
    color: black !important;
}

.search-show-more {
    font-size: 16px;
    letter-spacing: 1.6px;
    line-height: 20px;
    color: #ece81a;

    &::after {
        border-right: 2px solid #ece81a;
        border-top: 2px solid #ece81a;
        content: '';
        height: 9px;
        position: absolute;
        right: -13px;
        top: 42%;
        transform: translateY(-50%) rotate(45deg);
        width: 9px;
    }
}

.search-header-error {
    padding-bottom: 80px;
    padding-top: 73px;
}

.search-dropdown {
    @media (min-width: 769px) {
        margin-bottom: 24px;
    }
}

.toolbar-header {
    padding: 24px 27px;
    z-index: 12;

    @screen md {
        padding: 24px 2px 24px 2.5rem;
    }
}

.heading {
    @apply text-left uppercase tracking-normal font-tomorrow font-medium;
    hyphens: auto;
    font-size: 2.25rem;
    line-height: 3.5rem;
}

.amount-wrapper span {
    @apply text-right;
    width: 23% !important;
    color: black !important;
    border-color: black !important;
    @screen md {
        color: #909090 !important;
        border-color: #909090 !important;
    }
}

.amount-wrapper button {
    max-width: 100% !important;
}
.wrapper-sort-filter div {
    @screen md-max {
        @apply w-full;
    }
}
//classes to modify style only on memory category page
.wrapper-sort-filter button {
    font-family: Tomorrow, sans-serif;
    @screen md-max {
        color: black !important;
        border-color: black !important;
    }
}
.wrapper-sort-filter span {
    font-family: Tomorrow, sans-serif;
    @screen md-max {
        color: black !important;
        border-color: black !important;
    }
}
