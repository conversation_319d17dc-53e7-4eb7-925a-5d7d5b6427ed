import { MobileFilterQueryParams } from '@components/corra/SortFilterMobile'
import { SearchCriteria } from '@corsairitshopify/corsair-filters-and-sort'
import {
    FilterInputData,
    FilterListType,
    PLPManager,
    PLPState
} from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { Aggregation, Maybe } from '@pylot-data/fwrdschema'
import { ATTR_PRICE_FILTER } from 'hooks/usePlpManager'
import { getApplyCriterionHandler } from './getApplyCriterionHandler'
import { getApplyFilterHandler } from './getApplyFilterHandler'
import { getOnlyNonTransactionalOptions } from './getOnlyNonTransactionalOptions'

export const updatePlpManagerState = (
    plpManager: PLPManager,
    newState: Partial<PLPState>
): void => {
    const { clearPLPState, plpState } = plpManager
    const updatedState = { ...plpState, ...newState }
    clearPLPState(updatedState)
}

export const fillArrayWithLength = (length: number): Array<number> =>
    length === 0 ? [] : Array.from(Array(length).keys())

interface TransFormApplyFilterToUpdatedFilterParams {
    filterData?: FilterInputData
    shouldApply?: boolean
    isSingleSelect?: boolean
    currentSearchCriteria: SearchCriteria[]
    currentFilter: FilterInputData[]
    currentFilters?: FilterListType
}

export const fromApplyFilterDataToUpdatedFilterData = (
    params: TransFormApplyFilterToUpdatedFilterParams
): MobileFilterQueryParams => {
    const {
        currentFilter,
        currentFilters,
        currentSearchCriteria,
        filterData,
        shouldApply,
        isSingleSelect
    } = params

    const searchCriteria = getApplyCriterionHandler(
        filterData,
        shouldApply,
        currentSearchCriteria
    )
    const filter = getApplyFilterHandler(
        filterData,
        shouldApply,
        isSingleSelect,
        currentFilter,
        currentFilters
    )

    return {
        appliedSearchCritieria: searchCriteria ?? [],
        appliedFilterData: filter.appliedFilterData,
        filters: filter.filters as any
    }
}

export const getSortedAggregationListWithFeature = (
    aggregationList: Maybe<Aggregation>[],
    isNonTransactional: boolean
): Maybe<Aggregation>[] => {
    if (!aggregationList) return []

    const filteredAggregationList = isNonTransactional
        ? getOnlyNonTransactionalOptions(aggregationList).filter(
              (agg) => agg?.options && agg.options.length
          )
        : aggregationList

    const sortedAggregationListWithFeature = filteredAggregationList.reduce(
        (arr: Maybe<Aggregation>[], agg) =>
            agg?.attribute_code === 'features_and_availability'
                ? [agg, ...arr]
                : [...arr, agg],
        []
    )

    return sortedAggregationListWithFeature
}

export const getMaxPriceRangeValue = (
    agg: Maybe<Maybe<Aggregation>[]>
): number => {
    if (!agg) {
        return 0
    }
    const filterRange = agg?.find(
        (el) => el?.attribute_code === ATTR_PRICE_FILTER
    )
    if (!filterRange) {
        return 0
    }

    const maxValueStr = filterRange?.options?.[0]?.value ?? '0'
    const maxValueFloat = parseFloat(maxValueStr)

    if (isNaN(maxValueFloat)) {
        return 0
    }

    return maxValueFloat
}
