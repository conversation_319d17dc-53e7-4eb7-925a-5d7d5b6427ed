import { useTranslation } from 'next-i18next'
import React, { useMemo, useRef } from 'react'
import { A11y, Navigation, Pagination, FreeMode } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'
import cn from 'classnames'

import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import { generateRandomString } from 'helpers/stringHelper'

import CarouselProductGalleryItem from './CarouselProductGalleryItem'
import { CtaButton } from '../Carousel/Carousel'
import {
    CarouselWrapperType,
    ProductGalleryType,
    ProductsSlideType
} from '../CarouselWrapper/CarouselWrapper.types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { IMeta } from '../types'

import s from './CarouselProductGallery.module.scss'

export type CslData = ProductsSlideType[]

type CarouselProductTilesProps = {
    content: CarouselWrapperType
}

type DesktopMobileImage = {
    desktopProductTitle: string
    desktopProductDescription: string
    mobileProductTitle: string
    desktop: ImageComponent
    mobile: ImageComponent
    ctaButton?: CtaButton
}

type ImageComponent = {
    title: string
    image: ImageType
    url: string
    cloudinaryImage?: CloudinaryImage[]
}

export type CSLObject = {
    productImages: DesktopMobileImage[]
    identifier: string
    heading: string
    cloudinaryLogo: CloudinaryImage[]
    cloudinaryMainImage: CloudinaryImage[]
}

export type CslContainerTiles = {
    identifier: string
    productSlide: CSLObject[]
    copyBlock?: { subHeading?: string; heading?: string; text?: string }
    meta: IMeta<'carouselppContainer'>
    displayType: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryMobileBackgroundImage?: CloudinaryImage[]
    backgroundColor?: string
    desktopPaddingTop?: string
    desktopPaddingBottom?: string
    mobilePaddingTop?: string
    mobilePaddingBottom?: string
}

const CarouselProductGallery = ({
    content: carouselContainer
}: CarouselProductTilesProps): JSX.Element | null => {
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const isMobile = useMediaQuery('(max-width: 767px)')
    const { t } = useTranslation(['common'])

    const formattedName = useMemo(() => {
        return generateRandomString(10)
    }, [])

    if (!carouselContainer) return null

    const productSlides = carouselContainer?.productSlide as ProductGalleryType[]

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: any) => {
        animatePagination(swiper)
    }

    return (
        <>
            <div
                className={cn(
                    s['product-gallery-swiper-container'],
                    'overflow-hidden'
                )}
            >
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['prev'],
                        `${formattedName}-prev`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                    )}
                    aria-label={t('previous')}
                >
                    <ArrowRightIcon />
                </button>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['next'],
                        'scuf-button-primary',
                        'scuf-show-right-arrow',
                        `${formattedName}-next`
                    )}
                    aria-label={t('next')}
                >
                    <ArrowRightIcon />
                </button>

                {carouselContainer?.showRearShadow && (
                    <>
                        <div className={cn(s['faded-left'])} />
                        <div className={cn(s['faded-right'])} />
                    </>
                )}

                <Swiper
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    className={cn(s['swiper-container'], 'swiper-container')}
                    modules={[Navigation, Pagination, A11y, FreeMode]}
                    speed={500}
                    spaceBetween={20}
                    slidesPerGroup={1}
                    // slidesPerView={calculateSlidesPerView(1)}
                    slidesPerView={1.2}
                    centerInsufficientSlides
                    navigation={{
                        prevEl: `.${formattedName}-prev`,
                        nextEl: `.${formattedName}-next`,
                        disabledClass: '.disabled:opacity-50'
                    }}
                    onBreakpoint={(swiper) => {
                        animatePagination(swiper)
                    }}
                    onSlideChange={(swiper) => {
                        animatePagination(swiper)
                        handleSlideChange(swiper)
                    }}
                    onReachEnd={handleSlideChange}
                    onReachBeginning={handleSlideChange}
                    pagination={{
                        el: `.${formattedName}-pagination`,
                        clickable: true,
                        renderBullet: (idx, className) => {
                            return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                        }
                    }}
                    breakpoints={{
                        500: {
                            slidesPerView: 2.2,
                            slidesPerGroup: 1
                        },
                        768: {
                            slidesPerView: 3.4,
                            slidesPerGroup: 1
                        },
                        1024: {
                            slidesPerView: 3.6,
                            slidesPerGroup: 1,
                            spaceBetween: 20
                        },
                        1440: {
                            slidesPerView: 4.4,
                            slidesPerGroup: 1,
                            spaceBetween: 20
                        }
                    }}
                >
                    {productSlides.map((prod, key) => (
                        <SwiperSlide key={key}>
                            <CarouselProductGalleryItem product={prod} />
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            {isMobile && (
                <div
                    className={cn(
                        s['pagination-container'],
                        'pagination-container',
                        s['pagination-container-mobile'],
                        'pagination-container-mobile'
                    )}
                    ref={containerRef}
                >
                    <div className="relative inline-block md:hidden">
                        <div
                            ref={paginationRef}
                            className={`${formattedName}-pagination flex gap-4`}
                        />
                        <div className="dots-line" />
                        <div
                            ref={activeDotRef}
                            className="active-animated-dot"
                        />
                    </div>
                </div>
            )}
        </>
    )
}

export default CarouselProductGallery
