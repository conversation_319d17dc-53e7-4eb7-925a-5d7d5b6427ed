import { FilterWrapperProps } from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import cn from 'classnames'
import { useEffect } from 'react'
import s from './SidebarFilter.module.scss'

const c = {
    layeredNavigations: `${s['layered-navigations']} text-white w-full hidden fixed h-screen right-0 overflow-hidden box-border z-50 border-none opacity-0`,
    desktopStyles: 'md:relative md:top-0 md:z-0 md:h-full',
    filterOverlay: `${s['filter-overlay']} z-50 fixed inset-0 opacity-50 md:hidden`,
    filterMob: `${s['filter-mob']} md:hidden uppercase border-solid border-b border-accents-3 p-4 pl-10 font-sairaHeader tracking-wide`,
    filterMobClose: `${s['filter-mob-close']} absolute right-4 top-4 cursor-pointer md:hidden`,
    filterBody: `${s['filter-body']} justify-between h-screen md:h-auto`,
    clearAllButton: `${s['selected-items-clear-all']}`,
    filterCloseIcon: `${s['filter-close-btn']} rounded-full`
}

export const SidebarFilter = (props: FilterWrapperProps): JSX.Element => {
    const {
        linkedPages,
        filtersVisible,
        isAccordion,
        filtersList,
        className,
        isFilterByCompatibility = false,
        filtersApplied,
        appliedFilterList,
        isMobilePopup
    } = props

    useEffect(() => {
        const $header = document.getElementById('header')
        const clientHeight = $header ? $header.clientHeight : null
        const element: HTMLElement | null = document.querySelector(
            '#mobileToolbar .topStyle'
        )
        if (element) {
            element.style.top = `${clientHeight}px`
        }
    })

    return (
        <div className={isMobilePopup ? 'mobileFilter' : 'desktopfilter'}>
            <aside>
                <div
                    className={`${cn(
                        c.layeredNavigations,
                        c.desktopStyles,
                        filtersVisible && s['open'],
                        className
                    )} topStyle`}
                >
                    <div className={cn(s['filters-list'])}>
                        <div
                            className={cn(
                                c.filterBody,
                                isAccordion
                                    ? s['accordion-filter-body']
                                    : s['non-accordion-filter-body']
                            )}
                        >
                            {linkedPages && !isFilterByCompatibility && (
                                <div className={s['linked-pages-container']}>
                                    {linkedPages}
                                </div>
                            )}

                            <ul>
                                {filtersApplied > 0 && (
                                    <li className="md-max:hidden">
                                        {appliedFilterList}
                                    </li>
                                )}
                                {filtersList}
                            </ul>
                        </div>
                    </div>
                </div>
            </aside>
        </div>
    )
}
