import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import Link from 'next/link'
import { CSSProperties, FC } from 'react'
import styles from './FeatureColumn.module.scss'
import {
    FeatureColumnItemProps,
    FeatureColumnProps
} from './FeatureColumn.types'

const Item: FC<FeatureColumnItemProps> = ({
    data,
    itemType,
    itemSpacingRightDesktop
}) => {
    const additionalStyles = {
        '--padding-right': `${itemSpacingRightDesktop}px`
    } as CSSProperties

    return (
        <div
            className={cn(
                styles.FeatureColumnItemWrapper,
                styles[`${itemType}Item`]
            )}
            style={additionalStyles}
        >
            <div className={styles.ImageWrapper}>
                <Image
                    src={data.icon[0].secure_url}
                    layout="fill"
                    objectFit="cover"
                    keepOrigin
                />
            </div>
            <h5 className={cn('body')}>{data.title}</h5>
            {data?.description && (
                <p className={cn('body')}>{data?.description}</p>
            )}
        </div>
    )
}

const FeatureColumn: FC<FeatureColumnProps> = ({ content }) => (
    <section id={content.title} className={cn(styles.FeatureColumnWrapper)}>
        <div className="scuf-container">
            <h3 className={cn('title--3', styles.Title)}>{content.title}</h3>
            <div
                className={cn(
                    styles.ItemWrapper,
                    styles[`Item${content.itemType}Wrapper`]
                )}
            >
                {content.items.map((item) => (
                    <Item data={item} key={item.title} {...content} />
                ))}
            </div>
            {content?.buttonLink && content?.buttonContent && (
                <div className={styles.ButtonWrapper}>
                    <Link href={content?.buttonLink}>
                        <button
                            className={cn(
                                styles.ActionButton,
                                'button',
                                'bg-white',
                                'scuf-button-secondary'
                            )}
                        >
                            {content?.buttonContent}
                            {content?.buttonIcon?.[0] && (
                                <div className={styles.IconWrapper}>
                                    <Image
                                        src={
                                            content?.buttonIcon?.[0]?.secure_url
                                        }
                                        layout="fill"
                                        objectFit="cover"
                                    />
                                </div>
                            )}
                        </button>
                    </Link>
                </div>
            )}
        </div>
    </section>
)

export default FeatureColumn
