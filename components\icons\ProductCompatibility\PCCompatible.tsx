import { FC, HTMLAttributes } from 'react'

interface Props extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
}

const PCCompatible: FC<Props> = ({ width = 31, height = 31, ...props }) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 31 31"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <rect
            x="0.75"
            y="0.75"
            width="29.5"
            height="29.5"
            rx="1.5"
            fill="black"
            stroke="black"
        />
        <path
            d="M5.37 21.967V10.175H13.258L15.034 11.951V16.495L13.258 18.271H8.362V21.967H5.37ZM8.362 15.999H11.578L12.138 15.439V13.007L11.578 12.447H8.362V15.999ZM17.8573 21.967L16.0813 20.191V11.951L17.8573 10.175H23.9213L25.7133 11.951V14.335H22.8013V13.007L22.2253 12.447H19.6493L19.0733 13.007V19.135L19.6493 19.695H22.3213L22.9133 19.135V17.791H25.8093V20.191L24.0333 21.967H17.8573Z"
            fill="white"
        />
    </svg>
)

export default PCCompatible
