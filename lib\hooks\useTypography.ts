import { Typography } from '@components/common/Banner/Banner'
import { useMediaQuery } from './useMediaQuery'
import { createElement } from 'react'
import { CSSProperties } from 'react'
import cn from 'classnames'

export interface TypographyType {
    text: string
    color: string
    fontSize: string
    fontWeight: number
    type: 'Paragraph' | 'H1' | 'H2' | 'H3' | 'H4' | 'H5' | 'H6'
    element: JSX.Element
}

const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']

const mappedFontWeight = {
    Thin: 300,
    Normal: 400,
    Medium: 500,
    'Semi-bold': 600,
    'Extra Bold': 700,
    Black: 800
}

const getMappedFontWeight = (
    fw: 'Thin' | 'Normal' | 'Medium' | 'Semi-bold' | 'Extra Bold' | 'Black'
) => mappedFontWeight[fw] || 500

export const useTypography = (
    typography?: Typography,
    customClassName?: string
): TypographyType => {
    const {
        text,
        color,
        fontSizeDesktop,
        fontSizeMobile,
        fontWeightDesktop,
        fontWeightMobile,
        type
    } = typography || {
        text: '',
        color: '',
        fontSizeDesktop: '0',
        fontSizeMobile: '0',
        fontWeightDesktop: 'Normal',
        fontWeightMobile: 'Normal',
        type: 'Paragraph'
    }
    const fontSize = useMediaQuery('(min-width: 1024px)')
        ? `${fontSizeDesktop}px`
        : `${fontSizeMobile}px`
    const fontWeight = useMediaQuery('(min-width: 1024px)')
        ? getMappedFontWeight(fontWeightDesktop)
        : getMappedFontWeight(fontWeightMobile)

    if (!typography) return {} as TypographyType

    const element = createElement(
        headingTags.includes(type.toLocaleLowerCase())
            ? type.toLocaleLowerCase()
            : 'p',
        {
            className: cn(customClassName),
            style: {
                
                fontSize: fontSize,
                fontWeight: fontWeight
            } as CSSProperties,
            dangerouslySetInnerHTML: { __html: text }
        }
    )

    return {
        text,
        color,
        fontSize,
        fontWeight,
        type,
        element: element as JSX.Element
    }
}
