import {
    createElement,
    CSSProperties,
    useEffect,
    useRef,
    useState
} from 'react'
import cn from 'classnames'

import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

import s from './Banner.module.scss'
import { CTAButtonType } from '@pylot-data/hooks/contentful/use-content-json'
import CloudinaryVideoFilePlayer from '../Carousel/CloudinaryVideoFilePlayer'
import {
    convertUrlFormat,
    generateVideoThumbnail
} from 'helpers/cloudinaryHelper'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { CTAButton } from '../CTA'
import { useOnScreen } from '@lib/hooks/useOnScreen'

type ContentPosition =
    | 'left'
    | 'top-left'
    | 'bottom-left'
    | 'right'
    | 'top-right'
    | 'bottom-right'
    | 'center'
    | 'top-center'
    | 'bottom-center'

export interface BannerCTAResponse {
    identifier: string
    heading?: Typography
    subHeading?: Typography
    desktopContentPosition: ContentPosition
    mobileContentPosition: ContentPosition
    desktopContentWidth: string
    mobileContentWidth: string
    background: Background
    ctAs: CTAButtonType[]
}

export interface Background {
    desktopBackground?: CloudinaryImage[]
    mobileBackground?: CloudinaryImage[]
    desktopBackgroundHeight?: string
    mobileBackgroundHeight?: string
    backgroundColor?: BackgroundColor
}

export interface Typography {
    text: string
    color: string
    fontSizeDesktop: string
    fontSizeMobile: string
    fontWeightDesktop: FontWeight
    fontWeightMobile: FontWeight
    type: 'Paragraph' | 'H1' | 'H2' | 'H3' | 'H4' | 'H5' | 'H6'
}

export interface BannerCTAProps {
    content: BannerCTAResponse
}

export interface BackgroundColor {
    id: string
    name: string
    value: string
}

export type FontWeight =
    | 'Thin'
    | 'Normal'
    | 'Medium'
    | 'Semi-bold'
    | 'Extra Bold'
    | 'Black'

const BannerCTA: React.FC<BannerCTAProps> = ({ content }) => {
    const isMobile = useMediaQuery('(max-width: 767px')
    const [backgroundType, setBackgroundType] = useState<{
        isImage: boolean
        isVideo: boolean
        url: string | undefined
    }>()
    const animateRef = useRef(null)
    const { isOnScreen } = useOnScreen(animateRef)
    useEffect(() => {
        setBackgroundType(getBackgroundType(content, isMobile))
    }, [content, isMobile])
    const {
        desktopContentPosition = 'bottom-left',
        mobileContentPosition = 'bottom-left'
    } = content

    const headingTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']

    const mappedFontWeight = {
        Thin: 300,
        Normal: 400,
        Medium: 500,
        'Semi-bold': 600,
        'Extra Bold': 700,
        Black: 800
    }

    const {
        type: headingType,
        color: headingColor,
        text: headingText,
        fontSizeDesktop: headingFontSizeDesktop,
        fontSizeMobile: headingFontSizeMobile,
        fontWeightDesktop: headingFontWeightDesktop,
        fontWeightMobile: headingFontWeightMobile
    } = content.heading || { type: '', headingColor: '' }
    const {
        type: subHeadingType,
        color: subHeadingColor,
        text: subHeadingText,
        fontSizeDesktop: subHeadingFontSizeDesktop,
        fontSizeMobile: subHeadingFontSizeMobile,
        fontWeightDesktop: subHeadingFontWeightDesktop,
        fontWeightMobile: subHeadingFontWeightMobile
    } = content.subHeading || {
        type: '',
        headingColor: ''
    }

    const getBackgroundType = (
        content: BannerCTAResponse,
        isMobile: boolean
    ) => {
        const mobileMedia = content.background.mobileBackground?.[0]
        const desktopMedia = content.background.desktopBackground?.[0]
        const media = isMobile ? mobileMedia || desktopMedia : desktopMedia

        return {
            isImage: media?.resource_type === 'image',
            isVideo: media?.resource_type === 'video',
            url: media?.secure_url
        }
    }

    const getMappedFontWeight = (
        fw: 'Thin' | 'Normal' | 'Medium' | 'Semi-bold' | 'Extra Bold' | 'Black'
    ) => mappedFontWeight[fw] || 500

    const Heading = createElement(
        headingTags.includes(headingType.toLocaleLowerCase())
            ? headingType.toLocaleLowerCase()
            : 'p',
        {
            className: cn(
                s['hero-banner--heading'],
                s[`heading-${headingType.toLowerCase()}`]
            ),
            style: {
                color: headingColor,
                '--heading-font-size-desktop': `${headingFontSizeDesktop}px`,
                '--heading-font-size-mobile': `${headingFontSizeMobile}px`,
                '--heading-font-weight-desktop': getMappedFontWeight(
                    headingFontWeightDesktop || 'Normal'
                ),
                '--heading-font-weight-mobile': getMappedFontWeight(
                    headingFontWeightMobile || 'Normal'
                )
            } as CSSProperties,
            dangerouslySetInnerHTML: { __html: headingText }
        }
    )

    const SubHeading = createElement(
        headingTags.includes(subHeadingType.toLocaleLowerCase())
            ? subHeadingType.toLocaleLowerCase()
            : 'p',
        {
            className: cn(
                s['hero-banner--sub-heading'],
                s[`heading-${subHeadingType.toLowerCase()}`]
            ),
            style: {
                color: subHeadingColor,
                '--sub-heading-font-size-desktop': `${subHeadingFontSizeDesktop}px`,
                '--sub-heading-font-size-mobile': `${subHeadingFontSizeMobile}px`,
                '--sub-heading-font-weight-desktop': getMappedFontWeight(
                    subHeadingFontWeightDesktop || 'Normal'
                ),
                '--sub-heading-font-weight-mobile': getMappedFontWeight(
                    subHeadingFontWeightMobile || 'Normal'
                )
            } as CSSProperties,
            dangerouslySetInnerHTML: { __html: subHeadingText }
        }
    )

    const CTA = () => {
        return (
            <div className={s['hero-banner--cta']}>
                {content?.ctAs?.map((cta) => (
                    <CTAButton
                        key={cta.url}
                        {...cta}
                        className={{
                            root: s['hero-banner--cta-button']
                        }}
                    />
                ))}
            </div>
        )
    }

    return (
        <div className={s['hero-banner']} ref={animateRef}>
            {backgroundType?.isVideo && (
                <div
                    className={`${s['hero-banner--section']} w-full h-full relative`}
                    style={getBackgroundStyle(content, true)}
                >
                    <CloudinaryVideoFilePlayer
                        videoUrl={convertUrlFormat(backgroundType.url || '')}
                        alt={
                            isMobile
                                ? content.background.mobileBackground?.[0]
                                      ?.context?.custom?.alt || ''
                                : content.background.mobileBackground?.[0]
                                      ?.context?.custom?.alt ||
                                  content.heading?.text
                        }
                        fallbackImgUrl={generateVideoThumbnail(
                            backgroundType?.url || ''
                        )}
                        className="w-full h-full object-center object-cover absolute"
                    />
                    <div
                        data-desktop-position={desktopContentPosition}
                        data-mobile-position={mobileContentPosition}
                        className={cn(`${s['hero-banner--content']} absolute`, {
                            [s['onScreen']]: isOnScreen
                        })}
                        style={getContentStyle(content)}
                    >
                        {headingText ? Heading : ''}
                        {subHeadingText ? SubHeading : ''}
                        {content?.ctAs ? <CTA /> : ''}
                    </div>
                </div>
            )}
            {backgroundType?.isImage && (
                <section
                    className={s['hero-banner--section']}
                    style={getBackgroundStyle(content)}
                >
                    <div
                        data-desktop-position={desktopContentPosition}
                        data-mobile-position={mobileContentPosition}
                        className={cn(s['hero-banner--content'], {
                            [s['onScreen']]: isOnScreen
                        })}
                        style={getContentStyle(content)}
                    >
                        {headingText ? Heading : ''}
                        {subHeadingText ? SubHeading : ''}
                        {content?.ctAs ? <CTA /> : ''}
                    </div>
                </section>
            )}
            {content.background.backgroundColor?.id &&
                !backgroundType?.isImage && (
                    <section
                        className={s['hero-banner--section']}
                        style={getBackgroundStyle(content)}
                    >
                        <div
                            data-desktop-position={desktopContentPosition}
                            data-mobile-position={mobileContentPosition}
                            className={cn(s['hero-banner--content'], {
                                [s['onScreen']]: isOnScreen
                            })}
                            style={getContentStyle(content)}
                        >
                            {headingText ? Heading : ''}
                            {subHeadingText ? SubHeading : ''}
                            {content?.ctAs ? <CTA /> : ''}
                        </div>
                    </section>
                )}
        </div>
    )
}

const getBackgroundStyle = (
    content: BannerCTAResponse,
    isVideo?: boolean
): React.CSSProperties => {
    const mobileUrl = content.background?.mobileBackground?.[0]?.secure_url
    const desktopUrl = content.background?.desktopBackground?.[0]?.secure_url
    const desktopHeight = content.background?.desktopBackgroundHeight
    const mobileHeight = content.background?.mobileBackgroundHeight

    const styling = {
        '--desktop-bg-height': `${desktopHeight}px`,
        '--mobile-bg-height': `${mobileHeight}px`,
        '--bg-color':
            content.background.backgroundColor?.value || 'var(--white)'
    } as React.CSSProperties

    return isVideo
        ? styling
        : ({
              ...styling,
              '--desktop-bg-image': `url(${desktopUrl})`,
              '--mobile-bg-image': `url(${mobileUrl || desktopUrl})`
          } as React.CSSProperties)
}

const getContentWidth = (w: string) => {
    return Number.isFinite(w) ? `${w}px` : w
}

const getContentStyle = (content: BannerCTAResponse): React.CSSProperties => {
    return {
        '--desktop-content-width':
            getContentWidth(content.desktopContentWidth) ?? 'auto',
        '--mobile-content-width':
            getContentWidth(content.mobileContentWidth) ?? 'auto'
    } as React.CSSProperties
}

export default BannerCTA
