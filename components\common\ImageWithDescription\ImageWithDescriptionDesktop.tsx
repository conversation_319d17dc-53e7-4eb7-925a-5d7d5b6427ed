import s from './ImageWithDescription.module.scss'
import { ImageWithDescriptionProps } from '../types'
import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
const ImageWithDescriptionDesktop = ({
    content
}: ImageWithDescriptionProps): JSX.Element | null => {
    const { t } = useTranslation(['common'])
    return (
        <div className="w-full px-5 hidden md:block">
            <div className={s['image-outer-container']}>
                <div className={s['image-container']}>
                    {content?.cloudinaryMobileMedia && (
                        <CorsairImage
                            keepOrigin
                            src={
                                content?.cloudinaryDesktopMedia[0]?.secure_url
                                    ? convertUrlFormat(
                                          content?.cloudinaryDesktopMedia[0]
                                              ?.secure_url
                                      )
                                    : ''
                            }
                            alt={correctImageAlt(
                                t(
                                    `alt|${content?.cloudinaryDesktopMedia?.[0]?.context?.custom.alt}`
                                )
                            )}
                            layout="fixed"
                            width={1019}
                            height={374}
                        />
                    )}
                </div>
                <div className={s['text-container']}>
                    <div className={s['left-inner']}>
                        <div
                            className={`${s['inner-title']} ${s['label']}`}
                            style={{
                                color: content?.titleFontColor,
                                borderBottomColor: content?.lineBelowColor
                            }}
                        >
                            {content?.title1stImage}
                        </div>
                        <p
                            dangerouslySetInnerHTML={{
                                __html: content?.copy || ''
                            }}
                            className={s['body-copy']}
                        />
                        <div className={s['space-between-copy-disclaimer']} />
                        <span
                            className={`${s['right-below-text']} ${s['disclaimer-copy']}`}
                        >
                            {content?.disclaimerCopy}
                        </span>
                    </div>
                    <div className={s['right-inner']}>
                        <div
                            className={`${s['inner-title']} ${s['label']}`}
                            style={{
                                color: content?.titleFontColor2ndImage,
                                borderBottomColor:
                                    content?.lineBelowColor2ndImage
                            }}
                        >
                            {content?.title2ndImage}
                        </div>
                        <p
                            dangerouslySetInnerHTML={{
                                __html: content?.copy2ndImage || ''
                            }}
                            className={s['body-copy']}
                        />
                        <div className={s['space-between-copy-disclaimer']} />
                        <span
                            className={`${s['right-below-text']} ${s['disclaimer-copy']}`}
                        >
                            {content?.disclaimerCopy2ndImage}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ImageWithDescriptionDesktop
