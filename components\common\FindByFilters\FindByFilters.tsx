/* eslint-disable i18next/no-literal-string */
import React, {
    Dispatch,
    useMemo,
    VFC,
    useState,
    useEffect,
    useRef
} from 'react'
import s from './FindByFilters.module.scss'
import { Search } from '@components/icons'
import { Dropdown } from '@corsairitshopify/pylot-dropdown'
import cn from 'classnames'
import Image from '@corsairitshopify/corsair-image'
import { useTranslation } from 'next-i18next'
import { SearchCriteriaFilterActions } from '@corsairitshopify/corsair-filters-and-sort'
import { SearchCriteria } from '@corsairitshopify/pylot-filters-and-sort'
import { Maybe } from '@pylot-data/pylotschema'
import { dataQueryModelMore } from '@pylot-data/hooks/use-paginated-query'

const c = {
    container: `${s.filtersContainer} mx-auto my-auto w-full flex place-content-between md-max:px-6 mb-8`,
    dropdownContainer: `${s['sort-label']} w-full relative z-1 flex items-center justify-center focus:outline-none`,
    dropdownItemSelect: `${s['sort-label-selected']} uppercase text-left w-full`,
    imageContainer: `relative mt-3 ${s.imageContainer}`,
    containerSelected: `md:w-6/12 relative ${s.borderSelected} before:absolute before:w-full before:v-full flex-row flex`,
    containerSelection: `md:w-6/12 relative ${s.borderSelection} flex-row flex`,
    titleSelection: `relative font-tomorrow uppercase ${s.titleSelection}`,
    descriptionSelection: `relative mt-1 ${s.descriptionSelection}`,
    underline: `relative ${s.underline}`
}

const placeholderBySystem = '/images/memory-finder-placeholder-s.png'
const placeholderByCompatibility = '/images/memory-finder-placeholder-c.png'

export enum FindByFiltersDropdownEnum {
    MOTHERBOARDS = 'Motherboards',
    SYSTEMS = 'Systems',
    CPU_DATA = 'CPUData',
    MANUFACTURERS = 'Manufacturers',
    MODEL = 'Model',
    CPU = 'CPU',
    MODELS = 'Models'
}
export interface FindByFiltersProps {
    setInitialSearchCriteria: Dispatch<
        React.SetStateAction<SearchCriteria | undefined>
    >
    setFindByDropdownFilters: Dispatch<
        React.SetStateAction<FindByDropdownFiltersType>
    >
    findByDropdownFilters: FindByDropdownFiltersType
    setFindMemoryByOption: Dispatch<
        React.SetStateAction<FIND_MEMORY_BY_OPTION | undefined>
    >
    findMemoryByOption: FIND_MEMORY_BY_OPTION | undefined
    setFilterSearchCriteria: Dispatch<
        React.SetStateAction<SearchCriteria[] | undefined>
    >
    setNewSearchCriteria: Dispatch<
        React.SetStateAction<SearchCriteria | undefined>
    >
    resetFilters: () => void
}

export type FIND_MEMORY_BY_OPTION =
    | FindByFiltersDropdownEnum.SYSTEMS
    | FindByFiltersDropdownEnum.MOTHERBOARDS

export type DropDownFilterType = {
    title: string
    options?: Maybe<string[]>
}

export type FindByDropdownFiltersType = {
    systems: DropDownFilterType
    manufacturers: DropDownFilterType
    models: DropDownFilterType
    CPUModels: DropDownFilterType
}

const FindByFilters: VFC<FindByFiltersProps> = ({
    setInitialSearchCriteria,
    setFindByDropdownFilters,
    findByDropdownFilters,
    setFindMemoryByOption,
    findMemoryByOption,
    setFilterSearchCriteria,
    setNewSearchCriteria,
    resetFilters
}): JSX.Element => {
    const { t } = useTranslation(['common', 'plp'])

    const motherboardInitialSearchCriteria = {
        attribute_code: 'type',
        filter_action: SearchCriteriaFilterActions.EQ,
        filter_value: FindByFiltersDropdownEnum.MOTHERBOARDS
    }

    const systemsInitialSearchCriteria = {
        attribute_code: 'type',
        filter_action: SearchCriteriaFilterActions.EQ,
        filter_value: FindByFiltersDropdownEnum.SYSTEMS
    }

    const dropdownLabel = (isOpen: boolean, label: string) => (
        <button
            className={cn(c.dropdownContainer, {
                [s['sort-label-active']]: !isOpen
            })}
        >
            <span className={c.dropdownItemSelect}>{label}</span>
        </button>
    )

    const onStopPropagationClick = (event: any) => {
        event.stopPropagation()
    }

    const [change, setChange] = useState(false)

    // Systems hook
    const [indexSystemsActive, setIndexSystemsActive] = useState(0)
    const [systemsTitle, setSystemsTitle] = useState('')
    const [dataFilterSystems, setDataFilterSystems] = useState<
        string[] | undefined
    >()
    const [openSystems, setOpenSystems] = useState(false)
    const [searchSystems, setSearchSystems] = useState('Systems')
    const inputSearchSystems = useRef<HTMLInputElement>(null)

    useEffect(() => {
        if (
            findByDropdownFilters?.systems?.options &&
            findByDropdownFilters?.systems?.options.length > 0
        ) {
            setDataFilterSystems(findByDropdownFilters?.systems?.options)
        }
    }, [findByDropdownFilters?.systems?.options])

    const onStopPropagationSpaceBarKeySystems = (event: any) => {
        const systemsElementList = document.querySelectorAll('li[tabindex="0"]')
        if (event.keyCode === 32) {
            event.stopPropagation()
        }
        if (
            event.keyCode === 40 &&
            indexSystemsActive < systemsElementList.length - 1
        ) {
            for (let i = 0; i < systemsElementList.length; i++) {
                switch (i) {
                    case indexSystemsActive + 1:
                        systemsElementList[i].classList.add(s['active-result'])
                        systemsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        systemsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexSystemsActive(indexSystemsActive + 1)
        }
        if (event.keyCode === 38 && indexSystemsActive > 0) {
            for (let i = 0; i < systemsElementList.length; i++) {
                switch (i) {
                    case indexSystemsActive - 1:
                        systemsElementList[i].classList.add(s['active-result'])
                        systemsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        systemsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexSystemsActive(indexSystemsActive - 1)
        }
        if (event.keyCode === 13) {
            const element: HTMLElement = systemsElementList[
                indexSystemsActive
            ] as HTMLElement
            element.click()
        }
    }

    const onDownListSystems = (index: any) => {
        const systemsElementList = document.querySelectorAll('li[tabindex="0"]')
        setIndexSystemsActive(index)
        for (let i = 0; i < systemsElementList.length; i++) {
            switch (i) {
                case index:
                    systemsElementList[i].classList.add(s['active-result'])
                    break
                default:
                    systemsElementList[i].classList.remove(s['active-result'])
                    break
            }
        }
    }

    // Manufacturers Systems hook
    const [indexManuSystemsActive, setIndexManuSystemsActive] = useState(0)
    const [manuSystemsTitle, setManuSystemsTitle] = useState('')
    const [dataFilterManuSystems, setDataFilterManuSystems] = useState<
        string[] | undefined
    >()
    const [openManuSystems, setOpenManuSystems] = useState(false)
    const [searchManuSystems, setSearchManuSystems] = useState('Manufacturers')
    const inputSearchManuSystems = useRef<HTMLInputElement>(null)

    useEffect(() => {
        if (
            findByDropdownFilters?.manufacturers?.options &&
            findByDropdownFilters?.manufacturers?.options.length > 0
        ) {
            setDataFilterManuSystems(
                findByDropdownFilters?.manufacturers?.options
            )
        }
    }, [findByDropdownFilters?.manufacturers?.options])

    const onStopPropagationSpaceBarKeyManuSystems = (event: any) => {
        const manuSystemsElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        if (event.keyCode === 32) {
            event.stopPropagation()
        }
        if (
            event.keyCode === 40 &&
            indexManuSystemsActive < manuSystemsElementList.length - 1
        ) {
            for (let i = 0; i < manuSystemsElementList.length; i++) {
                switch (i) {
                    case indexManuSystemsActive + 1:
                        manuSystemsElementList[i].classList.add(
                            s['active-result']
                        )
                        manuSystemsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        manuSystemsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexManuSystemsActive(indexManuSystemsActive + 1)
        }
        if (event.keyCode === 38 && indexManuSystemsActive > 0) {
            for (let i = 0; i < manuSystemsElementList.length; i++) {
                switch (i) {
                    case indexManuSystemsActive - 1:
                        manuSystemsElementList[i].classList.add(
                            s['active-result']
                        )
                        manuSystemsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        manuSystemsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexManuSystemsActive(indexManuSystemsActive - 1)
        }
        if (event.keyCode === 13) {
            const element: HTMLElement = manuSystemsElementList[
                indexManuSystemsActive
            ] as HTMLElement
            element.click()
        }
    }

    const onDownListManuSystems = (index: any) => {
        const manuSystemsElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        setIndexManuSystemsActive(index)
        for (let i = 0; i < manuSystemsElementList.length; i++) {
            switch (i) {
                case index:
                    manuSystemsElementList[i].classList.add(s['active-result'])
                    break
                default:
                    manuSystemsElementList[i].classList.remove(
                        s['active-result']
                    )
                    break
            }
        }
    }

    // Model Systems hook
    const [indexModelsSystemActive, setIndexModelsSystemActive] = useState(0)
    const [modelsSystemTitle, setModelsSystemTitle] = useState('')
    const [dataFilterModelsSystem, setDataFilterModelsSystem] = useState<
        string[] | undefined
    >()
    const [openModelsSystem, setOpenModelsSystem] = useState(false)
    const [searchModelsSystem, setSearchModelsSystem] = useState('Models')
    const inputSearchModelsSystem = useRef<HTMLInputElement>(null)
    const [modelSystemLoading, setModelSystemLoading] = useState(false)

    useEffect(() => {
        if (
            findByDropdownFilters?.models?.options &&
            findByDropdownFilters?.models?.options.length > 0
        ) {
            setDataFilterModelsSystem(findByDropdownFilters?.models?.options)
        }
    }, [findByDropdownFilters?.models?.options, change])

    const onStopPropagationSpaceBarKeyModelsSystem = (event: any) => {
        const modelsSystemElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        if (event.keyCode === 32) {
            event.stopPropagation()
        }
        if (
            event.keyCode === 40 &&
            indexModelsSystemActive < modelsSystemElementList.length - 1
        ) {
            for (let i = 0; i < modelsSystemElementList.length; i++) {
                switch (i) {
                    case indexModelsSystemActive + 1:
                        modelsSystemElementList[i].classList.add(
                            s['active-result']
                        )
                        modelsSystemElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        modelsSystemElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexModelsSystemActive(indexModelsSystemActive + 1)
        }
        if (event.keyCode === 38 && indexModelsSystemActive > 0) {
            for (let i = 0; i < modelsSystemElementList.length; i++) {
                switch (i) {
                    case indexModelsSystemActive - 1:
                        modelsSystemElementList[i].classList.add(
                            s['active-result']
                        )
                        modelsSystemElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        modelsSystemElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexModelsSystemActive(indexModelsSystemActive - 1)
        }
        if (event.keyCode === 13) {
            const element: HTMLElement = modelsSystemElementList[
                indexModelsSystemActive
            ] as HTMLElement
            element.click()
        }
    }

    const onDownListModelsSystem = (index: any) => {
        const modelsSystemElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        setIndexModelsSystemActive(index)
        for (let i = 0; i < modelsSystemElementList.length; i++) {
            switch (i) {
                case index:
                    modelsSystemElementList[i].classList.add(s['active-result'])
                    break
                default:
                    modelsSystemElementList[i].classList.remove(
                        s['active-result']
                    )
                    break
            }
        }
    }

    // Manufacturers hook
    const [indexManufacturersActive, setIndexManufacturersActive] = useState(0)
    const [manufacturersTitle, setManufacturersTitle] = useState('')
    const [dataFilterManufacturers, setDataFilterManufacturers] = useState<
        string[] | undefined
    >()
    const [openManufacturers, setOpenManufacturers] = useState(false)
    const [searchManufacturers, setSearchManufacturers] = useState(
        'Manufacturers'
    )
    const inputSearchManufacturers = useRef<HTMLInputElement>(null)

    useEffect(() => {
        if (
            findByDropdownFilters?.manufacturers?.options &&
            findByDropdownFilters?.manufacturers?.options.length > 0
        ) {
            setDataFilterManufacturers(
                findByDropdownFilters?.manufacturers?.options
            )
        }
    }, [findByDropdownFilters?.manufacturers?.options])

    const onStopPropagationSpaceBarKeyManufacturers = (event: any) => {
        const manufacturersElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        if (event.keyCode === 32) {
            event.stopPropagation()
        }
        if (
            event.keyCode === 40 &&
            indexManufacturersActive < manufacturersElementList.length - 1
        ) {
            for (let i = 0; i < manufacturersElementList.length; i++) {
                switch (i) {
                    case indexManufacturersActive + 1:
                        manufacturersElementList[i].classList.add(
                            s['active-result']
                        )
                        manufacturersElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        manufacturersElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexManufacturersActive(indexManufacturersActive + 1)
        }
        if (event.keyCode === 38 && indexManufacturersActive > 0) {
            for (let i = 0; i < manufacturersElementList.length; i++) {
                switch (i) {
                    case indexManufacturersActive - 1:
                        manufacturersElementList[i].classList.add(
                            s['active-result']
                        )
                        manufacturersElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        manufacturersElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexManufacturersActive(indexManufacturersActive - 1)
        }
        if (event.keyCode === 13) {
            const element: HTMLElement = manufacturersElementList[
                indexManufacturersActive
            ] as HTMLElement
            element.click()
        }
    }

    const onDownListManufacturers = (index: any) => {
        const manufacturersElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        setIndexManufacturersActive(index)
        for (let i = 0; i < manufacturersElementList.length; i++) {
            switch (i) {
                case index:
                    manufacturersElementList[i].classList.add(
                        s['active-result']
                    )
                    break
                default:
                    manufacturersElementList[i].classList.remove(
                        s['active-result']
                    )
                    break
            }
        }
    }

    // Models hook
    const [indexModelsActive, setIndexModelsActive] = useState(0)
    const [modelsTitle, setModelsTitle] = useState('')
    const [dataFilterModels, setDataFilterModels] = useState<
        string[] | undefined
    >()
    const [openModels, setOpenModels] = useState(false)
    const [searchModels, setSearchModels] = useState('Models')
    const inputSearchModels = useRef<HTMLInputElement>(null)
    const [modelLoading, setModelLoading] = useState(false)

    useEffect(() => {
        if (
            findByDropdownFilters?.models?.options &&
            findByDropdownFilters?.models?.options.length > 0
        ) {
            setDataFilterModels(findByDropdownFilters?.models?.options)
        }
    }, [findByDropdownFilters?.models?.options, change])

    const onStopPropagationSpaceBarKeyModels = (event: any) => {
        const modelsElementList = document.querySelectorAll('li[tabindex="0"]')
        if (event.keyCode === 32) {
            event.stopPropagation()
        }
        if (
            event.keyCode === 40 &&
            indexModelsActive < modelsElementList.length - 1
        ) {
            for (let i = 0; i < modelsElementList.length; i++) {
                switch (i) {
                    case indexModelsActive + 1:
                        modelsElementList[i].classList.add(s['active-result'])
                        modelsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        modelsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexModelsActive(indexModelsActive + 1)
        }
        if (event.keyCode === 38 && indexModelsActive > 0) {
            for (let i = 0; i < modelsElementList.length; i++) {
                switch (i) {
                    case indexModelsActive - 1:
                        modelsElementList[i].classList.add(s['active-result'])
                        modelsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        modelsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexModelsActive(indexModelsActive - 1)
        }
        if (event.keyCode === 13) {
            const element: HTMLElement = modelsElementList[
                indexModelsActive
            ] as HTMLElement
            element.click()
        }
    }

    const onDownListModels = (index: any) => {
        const modelsElementList = document.querySelectorAll('li[tabindex="0"]')
        setIndexModelsActive(index)
        for (let i = 0; i < modelsElementList.length; i++) {
            switch (i) {
                case index:
                    modelsElementList[i].classList.add(s['active-result'])
                    break
                default:
                    modelsElementList[i].classList.remove(s['active-result'])
                    break
            }
        }
    }

    useEffect(() => {
        if (modelLoading) {
            let dataFilter
            setDataFilterModels([])
            dataQueryModelMore.then((res: any) => {
                const data = res?.data
                const products = data?.products
                const memoryFilters = products?.memoryFilters
                const models = memoryFilters?.models || []
                if (models.length === 0 || !products) {
                    setSearchModels(`No results match ${searchModels}`)
                    dataFilter = []
                    setModelLoading(false)
                } else {
                    setSearchModels('')
                    dataFilter = models
                    setModelLoading(false)
                }
                setModelLoading(false)
                if (inputSearchModels?.current?.value.length !== 0) {
                    setDataFilterModels(dataFilter)
                }
            })
        }
    }, [dataQueryModelMore])

    useEffect(() => {
        if (modelSystemLoading) {
            let dataFilter
            setDataFilterModelsSystem([])
            dataQueryModelMore.then((res: any) => {
                const data = res?.data
                const products = data?.products
                const memoryFilters = products?.memoryFilters
                const models = memoryFilters?.models || []
                if (models.length === 0 || !products) {
                    setSearchModelsSystem(
                        `No results match ${searchModelsSystem}`
                    )
                    dataFilter = []
                    setModelSystemLoading(false)
                } else {
                    setSearchModelsSystem('')
                    dataFilter = models
                    setModelSystemLoading(false)
                }
                setModelSystemLoading(false)
                if (inputSearchModelsSystem?.current?.value.length !== 0) {
                    setDataFilterModelsSystem(dataFilter)
                }
            })
        }
    }, [dataQueryModelMore])

    // CPU Models hook
    const [indexCPUModelsActive, setIndexCPUModelsActive] = useState(0)
    const [cPUModelsTitle, setCPUModelsTitle] = useState('')
    const [dataFilterCPUModels, setDataFilterCPUModels] = useState<
        string[] | undefined
    >()
    const [openCPUModels, setOpenCPUModels] = useState(false)
    const [searchCPUModels, setSearchCPUModels] = useState('CPU')
    const inputSearchCPUModels = useRef<HTMLInputElement>(null)

    useEffect(() => {
        if (
            findByDropdownFilters?.CPUModels?.options &&
            findByDropdownFilters?.CPUModels?.options.length > 0
        ) {
            setDataFilterCPUModels(findByDropdownFilters?.CPUModels?.options)
        }
    }, [findByDropdownFilters?.CPUModels?.options])

    const onStopPropagationSpaceBarKeyCPUModels = (event: any) => {
        const cPUModelsElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        if (event.keyCode === 32) {
            event.stopPropagation()
        }
        if (
            event.keyCode === 40 &&
            indexCPUModelsActive < cPUModelsElementList.length - 1
        ) {
            for (let i = 0; i < cPUModelsElementList.length; i++) {
                switch (i) {
                    case indexCPUModelsActive + 1:
                        cPUModelsElementList[i].classList.add(
                            s['active-result']
                        )
                        cPUModelsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        cPUModelsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexCPUModelsActive(indexCPUModelsActive + 1)
        }
        if (event.keyCode === 38 && indexCPUModelsActive > 0) {
            for (let i = 0; i < cPUModelsElementList.length; i++) {
                switch (i) {
                    case indexCPUModelsActive - 1:
                        cPUModelsElementList[i].classList.add(
                            s['active-result']
                        )
                        cPUModelsElementList[i].scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        })
                        break
                    default:
                        cPUModelsElementList[i].classList.remove(
                            s['active-result']
                        )
                        break
                }
            }
            setIndexCPUModelsActive(indexCPUModelsActive - 1)
        }
        if (event.keyCode === 13) {
            const element: HTMLElement = cPUModelsElementList[
                indexCPUModelsActive
            ] as HTMLElement
            element.click()
        }
    }

    const onDownListCPUModels = (index: any) => {
        const cPUModelsElementList = document.querySelectorAll(
            'li[tabindex="0"]'
        )
        setIndexCPUModelsActive(index)
        for (let i = 0; i < cPUModelsElementList.length; i++) {
            switch (i) {
                case index:
                    cPUModelsElementList[i].classList.add(s['active-result'])
                    break
                default:
                    cPUModelsElementList[i].classList.remove(s['active-result'])
                    break
            }
        }
    }

    const callApiMoreModel = async (searchValue: any) => {
        const newSearchCriteria = {
            attribute_code: 'Motherboards',
            filter_action: SearchCriteriaFilterActions.EQ,
            filter_value: `${searchValue}**`
        }
        setNewSearchCriteria(newSearchCriteria)
    }

    const callApiMoreSystemModel = async (searchValue: any) => {
        const newSearchCriteria = {
            attribute_code: 'Models',
            filter_action: SearchCriteriaFilterActions.EQ,
            filter_value: `${searchValue}**`
        }
        setNewSearchCriteria(newSearchCriteria)
    }

    // find By Systems Dropdowns -------------------------------------------

    const findBySytemsDropdowns = useMemo(() => {
        if (
            findMemoryByOption &&
            findMemoryByOption === FindByFiltersDropdownEnum.SYSTEMS
        ) {
            const { systems, manufacturers, models } = findByDropdownFilters

            // Systems handle
            const focusInputSearchSystems = () => {
                setOpenSystems(true)
                setTimeout(() => {
                    if (inputSearchSystems.current) {
                        inputSearchSystems.current.focus()
                    }
                    const systemsElementList = document.querySelectorAll(
                        'li[tabindex="0"]'
                    )
                    for (let i = 0; i < systemsElementList.length; i++) {
                        switch (i) {
                            case indexSystemsActive:
                                systemsElementList[i].classList.add(
                                    s['active-result']
                                )
                                systemsElementList[i].scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'nearest',
                                    inline: 'nearest'
                                })
                                break
                            default:
                                systemsElementList[i].classList.remove(
                                    s['active-result']
                                )
                                break
                        }
                    }
                }, 500)
            }

            const filterSystems = (e: any) => {
                const dataFilter = systems?.options?.filter((item) => {
                    return (
                        item
                            .toLowerCase()
                            .search(e.target.value.toLowerCase()) !== -1
                    )
                })
                if (dataFilter && dataFilter?.length > 0) {
                    setSearchSystems('')
                } else {
                    setSearchSystems('No results match ' + e.target.value)
                }
                setDataFilterSystems(dataFilter)
            }

            if (!openSystems) {
                setSearchSystems('Systems')
                if (systems.options) {
                    setDataFilterSystems(systems?.options)
                }
                setOpenSystems(false)
                if (systemsTitle === '') {
                    setIndexSystemsActive(0)
                } else {
                    if (systems?.options) {
                        for (let i = 0; i < systems?.options?.length; i++) {
                            if (
                                systemsTitle.toLowerCase() ===
                                systems?.options[i].toLowerCase()
                            ) {
                                setIndexSystemsActive(i)
                            }
                        }
                    }
                }
            }

            // Manufacturers systems filter
            const focusInputSearchManuSystems = () => {
                setOpenManuSystems(true)
                setTimeout(() => {
                    if (inputSearchManuSystems.current) {
                        inputSearchManuSystems.current.focus()
                    }
                    const manuSystemsElementList = document.querySelectorAll(
                        'li[tabindex="0"]'
                    )
                    for (let i = 0; i < manuSystemsElementList.length; i++) {
                        switch (i) {
                            case indexManuSystemsActive:
                                manuSystemsElementList[i].classList.add(
                                    s['active-result']
                                )
                                manuSystemsElementList[i].scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'nearest',
                                    inline: 'nearest'
                                })
                                break
                            default:
                                manuSystemsElementList[i].classList.remove(
                                    s['active-result']
                                )
                                break
                        }
                    }
                }, 500)
            }

            const filterManuSystems = (e: any) => {
                const dataFilter = manufacturers?.options?.filter((item) => {
                    return (
                        item
                            .toLowerCase()
                            .search(e.target.value.toLowerCase()) !== -1
                    )
                })
                if (dataFilter && dataFilter?.length > 0) {
                    setSearchManuSystems('')
                } else {
                    setSearchManuSystems('No results match ' + e.target.value)
                }
                setDataFilterManuSystems(dataFilter)
            }

            if (!openManuSystems) {
                setSearchManuSystems('Manufacturers')
                if (manufacturers.options) {
                    setDataFilterManuSystems(manufacturers?.options)
                }
                setOpenManuSystems(false)
                if (manuSystemsTitle === '') {
                    setIndexManuSystemsActive(0)
                } else {
                    if (manufacturers?.options) {
                        for (
                            let i = 0;
                            i < manufacturers?.options?.length;
                            i++
                        ) {
                            if (
                                manuSystemsTitle.toLowerCase() ===
                                manufacturers?.options[i].toLowerCase()
                            ) {
                                setIndexManuSystemsActive(i)
                            }
                        }
                    }
                }
            }

            // Models system handle
            const focusInputSearchModelsSystem = () => {
                setOpenModelsSystem(true)
                setTimeout(() => {
                    if (inputSearchModelsSystem.current) {
                        inputSearchModelsSystem.current.focus()
                    }
                    const modelsSystemElementList = document.querySelectorAll(
                        'li[tabindex="0"]'
                    )
                    for (let i = 0; i < modelsSystemElementList.length; i++) {
                        switch (i) {
                            case indexModelsSystemActive:
                                modelsSystemElementList[i].classList.add(
                                    s['active-result']
                                )
                                modelsSystemElementList[i].scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'nearest',
                                    inline: 'nearest'
                                })
                                break
                            default:
                                modelsSystemElementList[i].classList.remove(
                                    s['active-result']
                                )
                                break
                        }
                    }
                }, 500)
            }

            const filterModelsSystem = async (e: any) => {
                if (e.target.value.length === 0) {
                    dataQueryModelMore == null
                }
                const dataFilter = models?.options?.filter((item) => {
                    return (
                        item
                            .toLowerCase()
                            .search(e.target.value.toLowerCase()) !== -1
                    )
                })

                if (dataFilter && dataFilter?.length > 0) {
                    setSearchModelsSystem('')
                    setDataFilterModelsSystem(dataFilter)

                    if (e.target.value.length === 0) {
                        await callApiMoreSystemModel(e.target.value).then(
                            () => {
                                setSearchModelsSystem(e.target.value)
                                setDataFilterModelsSystem(dataFilter)
                            }
                        )
                    }
                } else if (
                    dataFilter?.length === 0 &&
                    models?.options?.length === 200
                ) {
                    setModelSystemLoading(true)
                    setDataFilterModelsSystem([])
                    await callApiMoreSystemModel(e.target.value).then(() => {
                        setSearchModelsSystem(e.target.value)
                    })
                } else {
                    setSearchModelsSystem(`No results match ${e.target.value}`)
                }
                setChange(true)
            }

            if (!openModelsSystem) {
                setSearchModelsSystem('Models')
                if (models.options) {
                    setDataFilterModelsSystem(models?.options)
                }
                setOpenModelsSystem(false)
                if (modelsSystemTitle === '') {
                    setIndexModelsSystemActive(0)
                } else {
                    if (models?.options) {
                        for (let i = 0; i < models?.options?.length; i++) {
                            if (
                                modelsSystemTitle.toLowerCase() ===
                                models?.options[i].toLowerCase()
                            ) {
                                setIndexModelsSystemActive(i)
                            }
                        }
                    }
                }
            }

            return (
                <>
                    <Dropdown
                        className={s['sort']}
                        openedDisplay={dropdownLabel(false, systems.title!)}
                        closedDisplay={dropdownLabel(true, systems.title!)}
                        wrapperClassName={cn(s['sort-container'])}
                        willOpen={focusInputSearchSystems}
                        willClose={() => setOpenSystems(false)}
                    >
                        <div className={s['search_input_content']}>
                            <input
                                type="text"
                                className={s['search_input']}
                                onChange={filterSystems}
                                onClick={onStopPropagationClick}
                                autoComplete="off"
                                ref={inputSearchSystems}
                                onKeyDown={onStopPropagationSpaceBarKeySystems}
                            />
                            <span>
                                <Search />
                            </span>
                        </div>
                        {dataFilterSystems && (
                            <ul className={s['ul-list']}>
                                <li className={s['disabled-result']}>
                                    {searchSystems}
                                </li>
                                {dataFilterSystems?.map((systemElem, index) => (
                                    <li
                                        key={systemElem + '_index_' + index}
                                        className={cn(
                                            s['sort-item'],
                                            s[
                                                systemElem == systems.title!
                                                    ? 'active-result'
                                                    : ''
                                            ],
                                            'block'
                                        )}
                                    >
                                        <button
                                            tabIndex={0}
                                            onMouseOver={() =>
                                                onDownListSystems(index)
                                            }
                                            onFocus={() =>
                                                onDownListSystems(index)
                                            }
                                            onClick={() => {
                                                const newSearchCriteria = {
                                                    attribute_code:
                                                        FindByFiltersDropdownEnum.SYSTEMS,
                                                    filter_action:
                                                        SearchCriteriaFilterActions.EQ,
                                                    filter_value: systemElem
                                                }
                                                setFindByDropdownFilters(
                                                    (prevState) => ({
                                                        ...prevState,
                                                        systems: {
                                                            ...prevState.systems,
                                                            title: systemElem
                                                        }
                                                    })
                                                )
                                                setNewSearchCriteria(
                                                    newSearchCriteria
                                                )
                                                setSystemsTitle(systemElem)
                                                setManuSystemsTitle('')
                                                setModelsSystemTitle('')
                                            }}
                                        >
                                            <span
                                                className={
                                                    s['sort-item-anchor']
                                                }
                                            >
                                                {systemElem}
                                            </span>
                                        </button>
                                    </li>
                                ))}
                            </ul>
                        )}
                    </Dropdown>
                    <Dropdown
                        className={s['sort']}
                        openedDisplay={dropdownLabel(
                            false,
                            manufacturers.title!
                        )}
                        closedDisplay={dropdownLabel(
                            true,
                            manufacturers.title!
                        )}
                        wrapperClassName={cn(s['sort-container'])}
                        willOpen={focusInputSearchManuSystems}
                        willClose={() => setOpenManuSystems(false)}
                    >
                        <div className={s['search_input_content']}>
                            <input
                                type="text"
                                className={s['search_input']}
                                onChange={filterManuSystems}
                                onClick={onStopPropagationClick}
                                autoComplete="off"
                                ref={inputSearchManuSystems}
                                onKeyDown={
                                    onStopPropagationSpaceBarKeyManuSystems
                                }
                            />
                            <span>
                                <Search />
                            </span>
                        </div>
                        {dataFilterManuSystems && (
                            <ul className={s['ul-list']}>
                                <li className={s['disabled-result']}>
                                    {searchManuSystems}
                                </li>
                                {dataFilterManuSystems?.map(
                                    (systemElem, index) => (
                                        <li
                                            key={systemElem + '_index_' + index}
                                            className={cn(
                                                s['sort-item'],
                                                s[
                                                    systemElem ==
                                                    manufacturers.title!
                                                        ? 'active-result'
                                                        : ''
                                                ],
                                                'block'
                                            )}
                                        >
                                            <button
                                                tabIndex={0}
                                                onMouseOver={() =>
                                                    onDownListManuSystems(index)
                                                }
                                                onFocus={() =>
                                                    onDownListManuSystems(index)
                                                }
                                                onClick={() => {
                                                    const newSearchCriteria = {
                                                        attribute_code:
                                                            FindByFiltersDropdownEnum.MANUFACTURERS,
                                                        filter_action:
                                                            SearchCriteriaFilterActions.EQ,
                                                        filter_value: systemElem
                                                    }
                                                    setFindByDropdownFilters(
                                                        (prevState) => ({
                                                            ...prevState,
                                                            manufacturers: {
                                                                ...prevState.manufacturers,
                                                                title: systemElem
                                                            }
                                                        })
                                                    )
                                                    setNewSearchCriteria(
                                                        newSearchCriteria
                                                    )
                                                    setManuSystemsTitle(
                                                        systemElem
                                                    )
                                                    setModelsSystemTitle('')
                                                }}
                                            >
                                                <span
                                                    className={
                                                        s['sort-item-anchor']
                                                    }
                                                >
                                                    {systemElem}
                                                </span>
                                            </button>
                                        </li>
                                    )
                                )}
                            </ul>
                        )}
                    </Dropdown>
                    <Dropdown
                        className={s['sort']}
                        openedDisplay={dropdownLabel(false, models.title!)}
                        closedDisplay={dropdownLabel(true, models.title!)}
                        wrapperClassName={cn(s['sort-container'])}
                        willOpen={focusInputSearchModelsSystem}
                        willClose={() => setOpenModelsSystem(false)}
                    >
                        <div className={s['search_input_content']}>
                            <input
                                type="text"
                                className={s['search_input']}
                                onChange={filterModelsSystem}
                                onClick={onStopPropagationClick}
                                autoComplete="off"
                                ref={inputSearchModelsSystem}
                                onKeyDown={
                                    onStopPropagationSpaceBarKeyModelsSystem
                                }
                            />
                            <span>
                                <Search />
                            </span>
                        </div>
                        {dataFilterModelsSystem && (
                            <ul className={s['ul-list']}>
                                {modelSystemLoading ? (
                                    <li
                                        className={
                                            s['search-loading-container']
                                        }
                                    >
                                        <span className={s['search-loading']} />
                                    </li>
                                ) : (
                                    <li className={s['disabled-result']}>
                                        {searchModelsSystem}
                                    </li>
                                )}
                                {dataFilterModelsSystem?.map(
                                    (systemElem, index) => (
                                        <li
                                            key={systemElem + '_index_' + index}
                                            className={cn(
                                                s['sort-item'],
                                                s[
                                                    systemElem == models.title!
                                                        ? 'active-result'
                                                        : ''
                                                ],
                                                'block'
                                            )}
                                        >
                                            <button
                                                tabIndex={0}
                                                onMouseOver={() =>
                                                    onDownListModelsSystem(
                                                        index
                                                    )
                                                }
                                                onFocus={() =>
                                                    onDownListModelsSystem(
                                                        index
                                                    )
                                                }
                                                onClick={() => {
                                                    const newSearchCriteria = {
                                                        attribute_code:
                                                            FindByFiltersDropdownEnum.MODELS,
                                                        filter_action:
                                                            SearchCriteriaFilterActions.EQ,
                                                        filter_value: systemElem
                                                    }
                                                    setFindByDropdownFilters(
                                                        (prevState) => ({
                                                            ...prevState,
                                                            models: {
                                                                ...prevState.models,
                                                                title: systemElem
                                                            }
                                                        })
                                                    )
                                                    setNewSearchCriteria(
                                                        newSearchCriteria
                                                    )
                                                    setModelsSystemTitle(
                                                        systemElem
                                                    )
                                                }}
                                            >
                                                <span
                                                    className={
                                                        s['sort-item-anchor']
                                                    }
                                                >
                                                    {systemElem}
                                                </span>
                                            </button>
                                        </li>
                                    )
                                )}
                            </ul>
                        )}
                    </Dropdown>
                </>
            )
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        findByDropdownFilters,
        findMemoryByOption,
        dataFilterSystems,
        indexSystemsActive,
        openSystems,
        dataFilterManuSystems,
        indexManuSystemsActive,
        openManuSystems,
        dataFilterModelsSystem,
        indexModelsSystemActive,
        openModelsSystem
    ])

    // find By Motherboards Dropdowns -------------------------------------------

    const findByMotherboardsDropdowns = useMemo(() => {
        if (
            findMemoryByOption &&
            findMemoryByOption === FindByFiltersDropdownEnum.MOTHERBOARDS
        ) {
            const { manufacturers, models, CPUModels } = findByDropdownFilters

            // Manufacturers handle
            const focusInputSearchManufacturers = () => {
                setOpenManufacturers(true)
                setTimeout(() => {
                    if (inputSearchManufacturers.current) {
                        inputSearchManufacturers.current.focus()
                    }
                    const manufacturersElementList = document.querySelectorAll(
                        'li[tabindex="0"]'
                    )
                    for (let i = 0; i < manufacturersElementList.length; i++) {
                        switch (i) {
                            case indexManufacturersActive:
                                manufacturersElementList[i].classList.add(
                                    s['active-result']
                                )
                                manufacturersElementList[i].scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'nearest',
                                    inline: 'nearest'
                                })
                                break
                            default:
                                manufacturersElementList[i].classList.remove(
                                    s['active-result']
                                )
                                break
                        }
                    }
                }, 500)
            }

            const filterManufacturers = (e: any) => {
                const dataFilter = manufacturers?.options?.filter((item) => {
                    return (
                        item
                            .toLowerCase()
                            .search(e.target.value.toLowerCase()) !== -1
                    )
                })
                if (dataFilter && dataFilter?.length > 0) {
                    setSearchManufacturers('')
                } else {
                    setSearchManufacturers('No results match ' + e.target.value)
                }
                setDataFilterManufacturers(dataFilter)
            }

            if (!openManufacturers) {
                setSearchManufacturers('Manufacturers')
                if (manufacturers.options) {
                    setDataFilterManufacturers(manufacturers?.options)
                }
                if (manufacturersTitle === '') {
                    setIndexManufacturersActive(0)
                } else {
                    if (manufacturers?.options) {
                        for (
                            let i = 0;
                            i < manufacturers?.options?.length;
                            i++
                        ) {
                            if (
                                manufacturersTitle.toLowerCase() ===
                                manufacturers?.options[i].toLowerCase()
                            ) {
                                setIndexManufacturersActive(i)
                            }
                        }
                    }
                }
            }

            // Models handle
            const focusInputSearchModels = () => {
                setOpenModels(true)
                setTimeout(() => {
                    if (inputSearchModels.current) {
                        inputSearchModels.current.focus()
                    }
                    const modelsElementList = document.querySelectorAll(
                        'li[tabindex="0"]'
                    )
                    for (let i = 0; i < modelsElementList.length; i++) {
                        switch (i) {
                            case indexModelsActive:
                                modelsElementList[i].classList.add(
                                    s['active-result']
                                )
                                modelsElementList[i].scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'nearest',
                                    inline: 'nearest'
                                })
                                break
                            default:
                                modelsElementList[i].classList.remove(
                                    s['active-result']
                                )
                                break
                        }
                    }
                }, 500)
            }

            const filterModels = async (e: any) => {
                if (e.target.value.length === 0) {
                    dataQueryModelMore == null
                }
                const dataFilter = models?.options?.filter((item) => {
                    return (
                        item
                            .toLowerCase()
                            .search(e.target.value.toLowerCase()) !== -1
                    )
                })

                if (dataFilter && dataFilter?.length > 0) {
                    setSearchModels('')
                    setDataFilterModels(dataFilter)

                    if (e.target.value.length === 0) {
                        await callApiMoreModel(e.target.value).then(() => {
                            setSearchModels(e.target.value)
                            setDataFilterModels(dataFilter)
                        })
                    }
                } else if (
                    dataFilter?.length === 0 &&
                    models?.options?.length === 200
                ) {
                    setModelLoading(true)
                    setDataFilterModels([])
                    await callApiMoreModel(e.target.value).then(() => {
                        setSearchModels(e.target.value)
                    })
                } else {
                    setSearchModels(`No results match ${e.target.value}`)
                }
                setChange(true)
            }

            if (!openModels) {
                setSearchModels('Models')
                if (models.options) {
                    setDataFilterModels(models?.options)
                }
                setOpenModels(false)
                if (modelsTitle === '') {
                    setIndexModelsActive(0)
                } else {
                    if (models?.options) {
                        for (let i = 0; i < models?.options?.length; i++) {
                            if (
                                modelsTitle.toLowerCase() ===
                                models?.options[i].toLowerCase()
                            ) {
                                setIndexModelsActive(i)
                            }
                        }
                    }
                }
            }

            // CPUModels handle
            const focusInputSearchCPUModels = () => {
                setOpenCPUModels(true)
                setTimeout(() => {
                    if (inputSearchCPUModels.current) {
                        inputSearchCPUModels.current.focus()
                    }
                    const cPUModelsElementList = document.querySelectorAll(
                        'li[tabindex="0"]'
                    )
                    for (let i = 0; i < cPUModelsElementList.length; i++) {
                        switch (i) {
                            case indexCPUModelsActive:
                                cPUModelsElementList[i].classList.add(
                                    s['active-result']
                                )
                                cPUModelsElementList[i].scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'nearest',
                                    inline: 'nearest'
                                })
                                break
                            default:
                                cPUModelsElementList[i].classList.remove(
                                    s['active-result']
                                )
                                break
                        }
                    }
                }, 500)
            }

            const filterCPUModels = (e: any) => {
                const dataFilter = CPUModels?.options?.filter((item) => {
                    return (
                        item
                            .toLowerCase()
                            .search(e.target.value.toLowerCase()) !== -1
                    )
                })
                if (dataFilter && dataFilter?.length > 0) {
                    setSearchCPUModels('')
                } else {
                    setSearchCPUModels('No results match ' + e.target.value)
                }
                setDataFilterCPUModels(dataFilter)
            }

            if (!openCPUModels) {
                setSearchCPUModels('CPU')
                if (CPUModels.options) {
                    setDataFilterCPUModels(CPUModels?.options)
                }
                setOpenCPUModels(false)
                if (cPUModelsTitle === '') {
                    setIndexCPUModelsActive(0)
                } else {
                    if (CPUModels?.options) {
                        for (let i = 0; i < CPUModels?.options?.length; i++) {
                            if (
                                cPUModelsTitle.toLowerCase() ===
                                CPUModels?.options[i].toLowerCase()
                            ) {
                                setIndexCPUModelsActive(i)
                            }
                        }
                    }
                }
            }

            return (
                <>
                    <Dropdown
                        className={s['sort']}
                        openedDisplay={dropdownLabel(
                            false,
                            manufacturers.title!
                        )}
                        closedDisplay={dropdownLabel(
                            true,
                            manufacturers.title!
                        )}
                        wrapperClassName={cn(s['sort-container'])}
                        willOpen={focusInputSearchManufacturers}
                        willClose={() => setOpenManufacturers(false)}
                    >
                        <div className={s['search_input_content']}>
                            <input
                                type="text"
                                className={s['search_input']}
                                onChange={filterManufacturers}
                                onClick={onStopPropagationClick}
                                autoComplete="off"
                                ref={inputSearchManufacturers}
                                onKeyDown={
                                    onStopPropagationSpaceBarKeyManufacturers
                                }
                            />
                            <span>
                                <Search />
                            </span>
                        </div>
                        {dataFilterManufacturers && (
                            <ul className={s['ul-list']}>
                                <li className={s['disabled-result']}>
                                    {searchManufacturers}
                                </li>
                                {dataFilterManufacturers?.map(
                                    (motherboardElem, index) => (
                                        <li
                                            key={
                                                motherboardElem +
                                                '_index_' +
                                                index
                                            }
                                            className={cn(
                                                s['sort-item'],
                                                s[
                                                    motherboardElem ==
                                                    manufacturers.title!
                                                        ? 'active-result'
                                                        : ''
                                                ],
                                                'block'
                                            )}
                                        >
                                            <button
                                                tabIndex={0}
                                                onMouseOver={() =>
                                                    onDownListManufacturers(
                                                        index
                                                    )
                                                }
                                                onFocus={() =>
                                                    onDownListManufacturers(
                                                        index
                                                    )
                                                }
                                                onClick={() => {
                                                    const newSearchCriteria = {
                                                        attribute_code:
                                                            FindByFiltersDropdownEnum.MANUFACTURERS,
                                                        filter_action:
                                                            SearchCriteriaFilterActions.EQ,
                                                        filter_value: motherboardElem
                                                    }
                                                    setFindByDropdownFilters(
                                                        (prevState) => ({
                                                            ...prevState,
                                                            manufacturers: {
                                                                ...prevState.manufacturers,
                                                                title: motherboardElem
                                                            }
                                                        })
                                                    )
                                                    setNewSearchCriteria(
                                                        newSearchCriteria
                                                    )
                                                    setManufacturersTitle(
                                                        motherboardElem
                                                    )
                                                    setModelsTitle('')
                                                    setCPUModelsTitle('')
                                                }}
                                            >
                                                <span
                                                    className={
                                                        s['sort-item-anchor']
                                                    }
                                                >
                                                    {motherboardElem}
                                                </span>
                                            </button>
                                        </li>
                                    )
                                )}
                            </ul>
                        )}
                    </Dropdown>
                    <Dropdown
                        className={s['sort']}
                        openedDisplay={dropdownLabel(false, models.title!)}
                        closedDisplay={dropdownLabel(true, models.title!)}
                        wrapperClassName={cn(s['sort-container'])}
                        willOpen={focusInputSearchModels}
                        willClose={() => setOpenModels(false)}
                    >
                        <div className={s['search_input_content']}>
                            <input
                                type="text"
                                className={s['search_input']}
                                onChange={filterModels}
                                onClick={onStopPropagationClick}
                                autoComplete="off"
                                ref={inputSearchModels}
                                onKeyDown={onStopPropagationSpaceBarKeyModels}
                            />
                            <span>
                                <Search />
                            </span>
                        </div>
                        {dataFilterModels && (
                            <ul className={s['ul-list']}>
                                {modelLoading ? (
                                    <li
                                        className={
                                            s['search-loading-container']
                                        }
                                    >
                                        <span className={s['search-loading']} />
                                    </li>
                                ) : (
                                    <li className={s['disabled-result']}>
                                        {searchModels}
                                    </li>
                                )}
                                {dataFilterModels?.map(
                                    (motherboardElem, index) => (
                                        <li
                                            key={
                                                motherboardElem +
                                                '_index_' +
                                                index
                                            }
                                            className={cn(
                                                s['sort-item'],
                                                s[
                                                    motherboardElem ==
                                                    models.title!
                                                        ? 'active-result'
                                                        : ''
                                                ],
                                                'block'
                                            )}
                                        >
                                            <button
                                                tabIndex={0}
                                                onMouseOver={() =>
                                                    onDownListModels(index)
                                                }
                                                onFocus={() =>
                                                    onDownListModels(index)
                                                }
                                                onClick={() => {
                                                    const newSearchCriteria = {
                                                        attribute_code:
                                                            FindByFiltersDropdownEnum.MOTHERBOARDS,
                                                        filter_action:
                                                            SearchCriteriaFilterActions.EQ,
                                                        filter_value: motherboardElem
                                                    }
                                                    setFindByDropdownFilters(
                                                        (prevState) => ({
                                                            ...prevState,
                                                            models: {
                                                                ...prevState.models,
                                                                title: motherboardElem
                                                            }
                                                        })
                                                    )
                                                    setNewSearchCriteria(
                                                        newSearchCriteria
                                                    )
                                                    setModelsTitle(
                                                        motherboardElem
                                                    )
                                                    setCPUModelsTitle('')
                                                }}
                                            >
                                                <span
                                                    className={
                                                        s['sort-item-anchor']
                                                    }
                                                >
                                                    {motherboardElem}
                                                </span>
                                            </button>
                                        </li>
                                    )
                                )}
                            </ul>
                        )}
                    </Dropdown>
                    {CPUModels.options && CPUModels.options.length > 0 && (
                        <Dropdown
                            className={s['sort']}
                            openedDisplay={dropdownLabel(
                                false,
                                CPUModels.title!
                            )}
                            closedDisplay={dropdownLabel(
                                true,
                                CPUModels.title!
                            )}
                            wrapperClassName={cn(s['sort-container'])}
                            willOpen={focusInputSearchCPUModels}
                            willClose={() => setOpenCPUModels(false)}
                        >
                            <div className={s['search_input_content']}>
                                <input
                                    type="text"
                                    className={s['search_input']}
                                    onChange={filterCPUModels}
                                    onClick={onStopPropagationClick}
                                    autoComplete="off"
                                    ref={inputSearchCPUModels}
                                    onKeyDown={
                                        onStopPropagationSpaceBarKeyCPUModels
                                    }
                                />
                                <span>
                                    <Search />
                                </span>
                            </div>
                            {dataFilterCPUModels && (
                                <ul className={s['ul-list']}>
                                    <li className={s['disabled-result']}>
                                        {searchCPUModels}
                                    </li>
                                    {dataFilterCPUModels?.map(
                                        (motherboardElem, index) => (
                                            <li
                                                key={
                                                    motherboardElem +
                                                    '_index_' +
                                                    index
                                                }
                                                className={cn(
                                                    s['sort-item'],
                                                    s[
                                                        motherboardElem ==
                                                        CPUModels.title!
                                                            ? 'active-result'
                                                            : ''
                                                    ],
                                                    'block'
                                                )}
                                            >
                                                <button
                                                    tabIndex={0}
                                                    onMouseOver={() =>
                                                        onDownListCPUModels(
                                                            index
                                                        )
                                                    }
                                                    onFocus={() =>
                                                        onDownListCPUModels(
                                                            index
                                                        )
                                                    }
                                                    onClick={() => {
                                                        const newSearchCriteria = {
                                                            attribute_code:
                                                                FindByFiltersDropdownEnum.CPU_DATA,
                                                            filter_action:
                                                                SearchCriteriaFilterActions.EQ,
                                                            filter_value: motherboardElem
                                                        }
                                                        setFindByDropdownFilters(
                                                            (prevState) => ({
                                                                ...prevState,
                                                                CPUModels: {
                                                                    ...prevState.CPUModels,
                                                                    title: motherboardElem
                                                                }
                                                            })
                                                        )
                                                        setNewSearchCriteria(
                                                            newSearchCriteria
                                                        )
                                                        setCPUModelsTitle(
                                                            motherboardElem
                                                        )
                                                    }}
                                                >
                                                    <span
                                                        className={
                                                            s[
                                                                'sort-item-anchor'
                                                            ]
                                                        }
                                                    >
                                                        {motherboardElem}
                                                    </span>
                                                </button>
                                            </li>
                                        )
                                    )}
                                </ul>
                            )}
                        </Dropdown>
                    )}
                </>
            )
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        findByDropdownFilters,
        findMemoryByOption,
        dataFilterManufacturers,
        indexManufacturersActive,
        openManufacturers,
        dataFilterModels,
        indexModelsActive,
        openModels,
        dataFilterCPUModels,
        indexCPUModelsActive,
        openCPUModels
    ])

    // Reset the common states every time we change the dropdown option
    const resetFindByOptions = (initialSearchCriteria: SearchCriteria) => {
        setFindByDropdownFilters((prevState) => ({
            ...prevState,
            manufacturers: {
                title: FindByFiltersDropdownEnum.MANUFACTURERS,
                options: []
            },
            models: {
                title: FindByFiltersDropdownEnum.MODEL,
                options: []
            }
        }))

        setInitialSearchCriteria(initialSearchCriteria)
        setFilterSearchCriteria([initialSearchCriteria])
        setNewSearchCriteria(initialSearchCriteria)
    }

    return (
        <>
            <div className="md:flex md:flex-row md:justify-items-center mb-8 pt-6 gap-10">
                <button
                    onKeyDown={() => {
                        setFindMemoryByOption(FindByFiltersDropdownEnum.SYSTEMS)
                    }}
                    className={cn(
                        findMemoryByOption === FindByFiltersDropdownEnum.SYSTEMS
                            ? c.containerSelected
                            : c.containerSelection,
                        'cursor-pointer'
                    )}
                    onClick={() => {
                        setFindMemoryByOption(FindByFiltersDropdownEnum.SYSTEMS)
                        resetFindByOptions(systemsInitialSearchCriteria)
                        resetFilters()
                    }}
                    aria-label={t('Find by system')}
                >
                    <div className="w-4/12 lg:w-5/12 xl:w-4/12">
                        <div className={c.imageContainer}>
                            <Image
                                src={placeholderBySystem}
                                layout="fill"
                                alt=""
                            />
                        </div>
                    </div>
                    <div className="w-8/12 lg:w-7/12 xl:w-8/12 text-white justify-center md:h-full flex flex-col ml-3 my-auto">
                        <p className={c.titleSelection}>
                            {t('Find by system')}
                        </p>
                        <p className={c.descriptionSelection}>
                            {t('Identify by your make and model')}
                        </p>
                        <div className={c.underline} />
                    </div>
                </button>
                <button
                    className={cn(
                        findMemoryByOption ===
                            FindByFiltersDropdownEnum.MOTHERBOARDS
                            ? c.containerSelected
                            : c.containerSelection,
                        'cursor-pointer'
                    )}
                    onKeyDown={() => {
                        setFindMemoryByOption(
                            FindByFiltersDropdownEnum.MOTHERBOARDS
                        )
                        resetFilters()
                    }}
                    onClick={() => {
                        setFindMemoryByOption(
                            FindByFiltersDropdownEnum.MOTHERBOARDS
                        )

                        resetFindByOptions(motherboardInitialSearchCriteria)
                    }}
                    aria-label={t('Find by motherboard')}
                >
                    <div className="w-4/12 lg:w-5/12 xl:w-4/12">
                        <div className={c.imageContainer}>
                            <Image
                                src={placeholderByCompatibility}
                                layout="fill"
                                objectFit="contain"
                                alt=""
                            />
                        </div>
                    </div>
                    <div className="w-8/12 lg:w-7/12 xl:w-8/12 text-white justify-center md:h-full flex flex-col ml-5 my-auto">
                        <p className={c.titleSelection}>
                            {t('Find by motherboard')}
                        </p>
                        <p className={c.descriptionSelection}>
                            {t('Find your exact system board')}
                        </p>
                    </div>
                </button>
            </div>
            <div className={cn(c.container, 'justify-start')}>
                {findMemoryByOption === FindByFiltersDropdownEnum.SYSTEMS
                    ? findBySytemsDropdowns
                    : findByMotherboardsDropdowns}
            </div>
        </>
    )
}

export default FindByFilters
