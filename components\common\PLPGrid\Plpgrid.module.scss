.plp-grid {
    // overflow: unset !important;
    @apply grid grid-cols-2 overflow-hidden clear-both;
    column-gap: 6px;

    @screen sm {
        @apply grid-cols-3;
        column-gap: 16px;
    }

    @screen md {
    }

    @screen lg {
        @apply grid-cols-4;
        column-gap: 20px;
    }

    :global {
        [class^='marketing-tile'] {
            @apply md:pr-10 pb-5;
        }
    }
}

.continue-shopping-button {
    background-color: var(--primary);
    font-size: 12.65px;
    padding: 16px 33px;
    letter-spacing: 0.05rem;
    line-height: 14.2px;
    margin-top: 64px;
    margin: 64px auto 0;
}

.no-items-text {
    font-size: 16px;
    letter-spacing: 0.05rem;
    line-height: 24px;
    color: var(--secondary-off-black);

    @media (min-width: 769px) {
        font-size: 1.6rem;
        line-height: 28px;
    }
}
