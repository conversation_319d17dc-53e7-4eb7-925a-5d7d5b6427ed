.header {
    @apply m-auto text-white font-semibold text-center;
    max-width: 80vw;
    .subheading {
        @apply text-sm mb-0;
        color: #ece81a;
        letter-spacing: 0.05rem;
        line-height: 1rem;
        /*Mobile Query*/
        @media only screen and (max-width: 480px) {
            @apply text-xs;
        }
    }
    .heading {
        @apply text-6xl my-0;
        font-size: 3.75rem;
        letter-spacing: 0;
        /*Tablet Query*/
        @media only screen and (min-width: 481px) and (max-width: 768px) {
            font-size: 3rem;
        }
        /*Mobile Query*/
        @media only screen and (max-width: 480px) {
            font-size: 2.7rem;
        }
    }
    .description {
        font-size: 1.375rem;
        letter-spacing: 0.06rem;
        line-height: 2.25rem;
        padding: 20px 0 0;
    }
}
.square {
    span {
        aspect-ratio: 1 / 1;
    }
}

.two-three {
    span {
        aspect-ratio: 2 / 3;
    }
}

.gallery-row {
    gap: 1rem;

    div {
        width: 280px;
        display: flex;
        justify-content: center;
        align-items: center;
        .image {
            object-fit: contain;
        }
    }
}

@media (min-width: 320px) and (max-width: 767px) {
    .gallery-row {
        flex-direction: column;

        div {
            width: 335px;
        }
    }
}

@media (min-width: 768px) and (max-width: 1240px) {
    .gallery-row {
        div {
            width: 280px;
        }
    }
}

@media screen and (min-width: 1241px) {
    .gallery-row {
        div {
            width: 335px;
        }
    }
}
