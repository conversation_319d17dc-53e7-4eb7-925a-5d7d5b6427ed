import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { ProductGalleryType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselProductGallery.module.scss'

export type CarouselProductGalleryItemProps = {
    product: ProductGalleryType
}

const CarouselProductGalleryItem: FC<CarouselProductGalleryItemProps> = ({
    product
}) => {
    const { t } = useTranslation(['common'])

    return (
        <div className={s['container']}>
            <div className={s['slider-item-container']}>
                <div className={cn(s['slider-item-image-container'])}>
                    {/* <CorsairImage
                        keepOrigin
                        src={convertUrlFormat(product.image?.[0]?.secure_url)}
                        alt={t(
                            `alt|${product.image?.[0]?.context?.custom?.alt}`
                        )}
                        objectFit="cover"
                        layout="fill"
                    /> */}
                    <img
                        src={convertUrlFormat(product.image?.[0]?.secure_url)}
                        alt={t(
                            `alt|${product.image?.[0]?.context?.custom?.alt}`
                        )}
                        loading="lazy"
                    />
                </div>
            </div>
        </div>
    )
}

export default CarouselProductGalleryItem
