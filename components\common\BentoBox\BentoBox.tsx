import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { CSSProperties, FC } from 'react'

import ArrowRight from '@components/icons/BentoBox/ArrowRight'

import s from './BentoBox.module.scss'
import {
    BentoBoxProps,
    BentoContentInterface,
    BentoTileGap,
    BentoTileInterface
} from './BentoBox.types'

const layoutGaps: Record<BentoTileGap, number> = {
    none: 0,
    tiny: 8,
    small: 16,
    medium: 20,
    large: 24
}

const BentoBox: FC<BentoBoxProps> = ({ content }) => {
    if (!content) return null

    const {
        heading = '',
        subheading = '',
        tiles = [],
        layout = '2',
        layoutGap = 'medium',
        disclaimerInfo = '',
        additionalInfo = '',
        tileContentPosition = 'bottom-left',
        tileMobileHeight,
        tileDesktopHeight
    } = content

    const getSectionStyle = (): CSSProperties => {
        const {
            paddingTopDesktop = 16,
            paddingBottomDesktop = 16,
            paddingTopMobile = 10,
            paddingBottomMobile = 10
        } = { ...content?.padding }
        const backgroundColor = content?.backgroundColor

        return {
            '--padding-section-desktop': `${paddingTopDesktop}px ${paddingBottomDesktop}px`,
            '--padding-section-mobile': `${paddingTopMobile}px ${paddingBottomMobile}px`,
            '--tile-desktop-height': tileDesktopHeight,
            '--tile-mobile-height': tileMobileHeight,
            backgroundColor
        } as CSSProperties
    }

    return (
        <div
            className={cn(s['section-container'], !!heading && 'pt-4 lg:pt-12')}
            style={getSectionStyle()}
        >
            {heading && (
                <h2 className={s['section-heading']} aria-label={heading}>
                    {heading}
                </h2>
            )}
            {subheading && (
                <h3 className={s['section-subheading']} aria-label={subheading}>
                    {subheading}
                </h3>
            )}
            {!!disclaimerInfo && (
                <div
                    className={s['bento-disclaimer-info']}
                    dangerouslySetInnerHTML={{
                        __html: disclaimerInfo
                    }}
                />
            )}
            <div className={cn(s['bento-container'])}>
                <BentoBoxContent
                    data={{
                        tiles,
                        layout,
                        layoutGap,
                        tileContentPosition,
                        tileDesktopHeight,
                        tileMobileHeight
                    }}
                />
            </div>
            {!!additionalInfo && (
                <div
                    className={s['bento-additional-info']}
                    dangerouslySetInnerHTML={{
                        __html: additionalInfo
                    }}
                />
            )}
        </div>
    )
}

export default BentoBox

interface BentoBoxContentProps {
    data: BentoContentInterface
}
const BentoBoxContent: React.FC<BentoBoxContentProps> = ({ data }) => {
    const { t } = useTranslation(['common'])
    const {
        tiles = [],
        layout = '2',
        layoutGap = 'tiny',
        tileContentPosition = 'bottom-left'
    } = data

    const renderTiles = (tiles: BentoTileInterface[] = []) => {
        if (!tiles?.length) return null

        return tiles.map((tile, index) => {
            const backgroundImage = tile.backgroundImage?.[0]?.secure_url
            const key = `bento-tile-${index}`
            const commonProps = {
                className: cn('corsair-links', s['bento-tile']),
                'data-bannertype': tile.bannerTypeTracking,
                'data-bannername': tile.heading,
                'data-bannermessage': tile.description,
                style: {
                    '--background-image': backgroundImage
                        ? `url(${backgroundImage})`
                        : undefined,
                    '--content-width': tile.contentWidth
                } as CSSProperties
            }

            return (
                <a
                    {...commonProps}
                    key={key}
                    role="button"
                    tabIndex={0}
                    aria-label={`${t('Visit')} ${tile.heading}`}
                    href={tile.cta?.url || '#'}
                >
                    <div className={s['text-wrapper']}>
                        {!!tile.heading && (
                            <h2
                                className={s['tile-heading']}
                                aria-label={tile.heading}
                            >
                                {tile.heading}
                            </h2>
                        )}
                        {!!tile.description && (
                            <p
                                className={s['tile-description']}
                                aria-label={tile.description}
                            >
                                {tile.description}
                            </p>
                        )}
                        {tile.cta && (
                            <p
                                className={s['link-text']}
                                aria-label={tile.cta.displayText}
                                style={{
                                    color: tile.cta.textColor
                                }}
                            >
                                {tile.cta.displayText}
                                <ArrowRight aria-hidden />
                            </p>
                        )}
                    </div>
                </a>
            )
        })
    }

    const renderLayout = () => {
        switch (layout) {
            case '2':
                return (
                    <div className={s['bento-tiles']}>
                        <div
                            className={cn(s['row'])}
                            style={
                                {
                                    '--bento-column': 2,
                                    '--item-width': '713px'
                                } as CSSProperties
                            }
                        >
                            {renderTiles(tiles)}
                        </div>
                    </div>
                )

            case '2-3':
                return (
                    <div className={s['bento-tiles']}>
                        <div
                            className={cn(s['row'])}
                            style={
                                {
                                    '--bento-column': 2,
                                    '--item-width': '1fr'
                                } as CSSProperties
                            }
                        >
                            {renderTiles(tiles.slice(0, 2))}
                        </div>
                        <div
                            className={cn(s['row'])}
                            style={
                                {
                                    '--bento-column': 3,
                                    '--item-width': '1fr'
                                } as CSSProperties
                            }
                        >
                            {renderTiles(tiles.slice(2, 5))}
                        </div>
                    </div>
                )

            default:
                return null
        }
    }

    return (
        <div
            className={cn(s['bento-content-item'])}
            data-layout={layout}
            data-content-position={tileContentPosition}
            style={
                { '--item-gap': `${layoutGaps[layoutGap]}px` } as CSSProperties
            }
        >
            <div className={s['bento-tiles']}>{renderLayout()}</div>
        </div>
    )
}
