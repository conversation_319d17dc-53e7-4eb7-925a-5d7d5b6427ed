import { Maybe } from '@pylot-data/pylotschema'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { CloudinaryImage } from '@components/common/CloudinaryMedia/Cloudinary'
import { RibbonData } from '@components/common/types'
export type MegaMenuType = {
    identifier: string
    navigationItems: Array<NavigationItemType>
    promotionalMobileLinks: Array<CalloutItemType>
    ribbons?: RibbonData[]
}
export enum VisibilityEnum {
    All = 'All',
    Desktop = 'Desktop',
    Mobile = 'Mobile'
}
export type NavigationItemType = {
    title: string
    subTitle?: string
    url?: string
    menuDescription?: Maybe<MenuDescriptionType>
    featuredContentCta?: Maybe<MenuDescriptionCta>
    featuredContentImage?: ImageType
    featuredContentTitle?: string
    featuredContentDescription?: string
    menuItems: Array<MenuItemsType>
    columnsAmount: number
    visibility?: VisibilityEnum
    meta?: { contentType: string }
    cloudinaryFeaturedContentImage: CloudinaryImage[]
    calloutCloudinaryBackground?: CloudinaryImage[]
    calloutColumns?: Array<CalloutColumnType>
}
export type CalloutItemType = {
    title?: string
    image?: CloudinaryImage[]
    imageMobile?: CloudinaryImage[]
    imageShortDesc?: string
    url?: string
    openInANewTab?: boolean
}
export type CalloutColumnType = {
    calloutColumnTitle?: string
    calloutItems: Array<CalloutItemType>
}
export type MenuItemsType = {
    title: string
    subTitle?: string
    url: string
    target: string
    cloudinaryImage: CloudinaryImage[]
    submenuitems?: Maybe<Array<SubMenuItemsType>>
    visibility?: VisibilityEnum
}
export type SubMenuItemsType = {
    title: string
    url: string
    openInANewTab?: boolean
    visibility?: VisibilityEnum
}
export type MenuDescriptionType = {
    text: string
    image: ImageType
    description: string
    cta: {
        displayText: string
        url: string
    }
}
export type MenuDescriptionCta = {
    displayText?: string
    url?: string
    openInANewTab?: boolean
}
export type MenuCalloutCta = {
    displayText?: string
    url?: string
    openInANewTab?: boolean
}
export interface MenuURLType {
    text: string
    url: string
    target: string
    color: string
    icon: IconType
    meta?: { contentType: string }
}
