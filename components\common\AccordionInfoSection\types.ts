import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { IMeta } from '../types'

export type AccordionInfoSectionProps = {
    content: AccordionInfoSectionType
}

export type AccordionInfoSectionType = {
    heading: string
    meta: IMeta<'accordionInfoSection'>
    description: string
    items: AccordionInfoSectionItemType[]
    maxWidth: string
    backgroundColor: string
    cloudinaryBackgroundImage: CloudinaryImage[]
    headingColor: string
    textColor: string
}

export type AccordionInfoSectionItemType = {
    heading: string
    text: string
}
