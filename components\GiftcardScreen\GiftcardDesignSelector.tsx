import { FC } from 'react'
import { ConfigurableVariant } from '@pylot-data/pylotschema'
import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import s from '@pagestyles/GiftcardPDP.module.scss'

type Props = {
    options: ConfigurableVariant[]
    value: ConfigurableVariant | null
    onChange: (design: ConfigurableVariant) => void
    designTitle?: string
}

export const GiftcardDesignSelector: FC<Props> = ({
    options,
    value,
    onChange,
    designTitle
}) => (
    <>
        {designTitle && <p className={s['title']}>{designTitle}</p>}
        <div className={s['swatch-container']}>
            {options.map((design) => (
                <div
                    key={design.sku}
                    className={cn(
                        s['swatch-item'],
                        design.image!.url === value?.image?.url && s.active
                    )}
                    role="button"
                    tabIndex={0}
                    onKeyPress={() => onChange(design)}
                    onClick={() => onChange(design)}
                >
                    <Image
                        src={design.image!.url as string}
                        keepOrigin
                        layout="fill"
                        objectFit="contain"
                    />
                </div>
            ))}
        </div>
    </>
)
