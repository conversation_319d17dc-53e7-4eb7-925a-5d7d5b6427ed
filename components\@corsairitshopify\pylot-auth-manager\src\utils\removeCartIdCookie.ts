export const removeCartIdCookie = (): void => {
    if (document && typeof document !== 'undefined') {
        const domain = getHostnameDomain()
        const domainPart = domain ? `domain=${domain}` : ''
        document.cookie = `cart_id=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;${domainPart}`
    }
}

export const getHostnameDomain = (): string | undefined => {
    if (!window?.location?.hostname) {
        return
    }
    let hostname = window.location.hostname
    const expectedDomains = ['.corsair.com', '.elgato.com']
    expectedDomains.forEach((domain) => {
        if (hostname.includes(domain)) {
            hostname = domain
        }
    })
    return hostname
}
