import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import cn from 'classnames'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import { generateRandomString } from 'helpers/stringHelper'
import { useTranslation } from 'next-i18next'
import { HTMLAttributes, useEffect, useMemo, useRef, useState } from 'react'
import { A11y, Navigation, Pagination } from 'swiper'
import { Swiper, SwiperProps, SwiperSlide } from 'swiper/react'
import styles from './CrosssellCarousel.module.scss'

type BaseCarouselProps<T extends { id: string }> = {
    list: T[]
    renderItem: (item: T) => JSX.Element
    swiperProps?: Omit<SwiperProps, 'speed'>
} & HTMLAttributes<HTMLDivElement>

const BaseCarousel = <D extends { id: string }>({
    list,
    renderItem,
    swiperProps,
    className
}: BaseCarouselProps<D>): JSX.Element => {
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const baseCarouselWrapperRef = useRef<HTMLDivElement | null>(null)
    const prevBtnRef = useRef<HTMLButtonElement | null>(null)

    const isMobile = useMediaQuery('(max-width: 767px)')
    const { t } = useTranslation(['common'])

    const [isStart, setIsStart] = useState(true)
    const [isEnd, setIsEnd] = useState(false)

    const formattedName = useMemo(() => {
        return generateRandomString(10)
    }, [])

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: any) => {
        setIsStart(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
        animatePagination(swiper)
    }

    useEffect(() => {
        const handleResize = () => {
            const windowWidth = window.innerWidth
            const isNavigationBtnDisable =
                prevBtnRef?.current?.classList?.contains('hide-button') ?? false
            const isAbove1100 = windowWidth > 1100
            const itemDisplayOn1100 = 4.2

            if (
                isNavigationBtnDisable &&
                isAbove1100 &&
                list.length < itemDisplayOn1100
            ) {
                baseCarouselWrapperRef?.current?.classList?.add(styles.NoPseudo)
            } else {
                baseCarouselWrapperRef?.current?.classList?.remove(
                    styles.NoPseudo
                )
            }
        }

        handleResize()

        window.addEventListener('resize', handleResize)
        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [list, formattedName])

    return (
        <section className={className}>
            <div
                ref={baseCarouselWrapperRef}
                className={cn(styles.BaseCarouselWrapper, {
                    // [styles['NoPseudoStart']]: isStart,
                    // [styles['NoPseudoEnd']]: isEnd
                })}
            >
                <button
                    className={cn(
                        styles['carousel-nav-button'],
                        styles['prev'],
                        `${formattedName}-prev`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                        // {
                        //     ['hide-button']: isStart
                        // }
                    )}
                    ref={prevBtnRef}
                    aria-label={t('previous')}
                >
                    <ArrowRightIcon />
                </button>
                <button
                    className={cn(
                        styles['carousel-nav-button'],
                        styles['next'],
                        'scuf-button-primary',
                        'scuf-show-right-arrow',
                        `${formattedName}-next`
                        // {
                        //     ['hide-button']: isEnd
                        // }
                    )}
                    aria-label={t('next')}
                >
                    <ArrowRightIcon />
                </button>

                <div className={cn(styles['faded-left'])} />
                <div className={cn(styles['faded-right'])} />

                <Swiper
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    className={cn(
                        styles['swiper-container'],
                        isEnd && styles['last-item-reached'],
                        (!isEnd || isStart) && styles['first-item-reached']
                    )}
                    modules={[Navigation, Pagination, A11y]}
                    speed={500}
                    spaceBetween={swiperProps?.spaceBetween ?? 10}
                    slidesPerView={swiperProps?.slidesPerView ?? 1.2}
                    slidesPerGroup={swiperProps?.slidesPerGroup ?? 1}
                    navigation={{
                        prevEl: `.${formattedName}-prev`,
                        nextEl: `.${formattedName}-next`,
                        disabledClass: `.disabled:opacity-50 .hide-button .${formattedName}-disabled-btn`
                    }}
                    onBreakpoint={(swiper) => {
                        animatePagination(swiper)
                    }}
                    onSlideChange={(swiper) => {
                        animatePagination(swiper)
                        handleSlideChange(swiper)
                    }}
                    pagination={{
                        el: `.${formattedName}-pagination`,
                        clickable: true,
                        renderBullet: (idx, className) => {
                            return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                        }
                    }}
                    breakpoints={
                        swiperProps?.breakpoints ?? {
                            500: {
                                slidesPerView: 2.2,
                                slidesPerGroup: 1
                            },
                            768: {
                                slidesPerView: 3.2,
                                slidesPerGroup: 1,
                                spaceBetween: 20
                            },
                            1024: {
                                slidesPerView: 4.2,
                                spaceBetween: 20
                            }
                        }
                    }
                    onSwiper={(swiper) => {
                        swiperProps?.onSwiper?.(swiper)
                    }}
                    {...(swiperProps ?? {})}
                >
                    {list.map((el, key) => (
                        <SwiperSlide key={key + el.id}>
                            {renderItem(el)}
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            {isMobile && (
                <div
                    className={cn(
                        styles['pagination-container'],
                        styles['pagination-container-mobile']
                    )}
                    ref={containerRef}
                >
                    <div className="inline-block relative">
                        <div
                            ref={paginationRef}
                            className={`${formattedName}-pagination flex gap-4`}
                        />
                        <div className="dots-line" />
                        <div
                            ref={activeDotRef}
                            className="active-animated-dot"
                        />
                    </div>
                </div>
            )}
        </section>
    )
}

export default BaseCarousel
