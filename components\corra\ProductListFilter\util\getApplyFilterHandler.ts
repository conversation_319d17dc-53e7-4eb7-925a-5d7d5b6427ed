import {
    FilterInputData,
    FilterListType,
    FiltersStateInput
} from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { Maybe, Scalars } from '@pylot-data/fwrdschema'
import { ATTR_PRICE_FILTER } from 'hooks/usePlpManager'
import { cloneDeep } from 'lodash'

type GetApplyFilterHandlerType = (
    filterInputData?: FilterInputData,
    isApplied?: boolean,
    isSingleSelect?: boolean,
    appliedFilterData?: FilterInputData[],
    filterListType?: FilterListType
) => FiltersStateInput

// Remove input data of a filter option, then push it again (if the filter is applied)
const getAppliedFilterData = (
    filterInputData: FilterInputData,
    isApplied: boolean,
    isSingleSelect: boolean,
    appliedFilterData: FilterInputData[]
): Array<FilterInputData> => {
    const data = appliedFilterData.filter(
        (item) =>
            item.requestVar !== filterInputData.requestVar ||
            (!isSingleSelect &&
                item.value_string !== filterInputData.value_string)
    )
    if (isApplied) {
        data.push(filterInputData)
    }
    return data
}

/**
 * Rebuild a new filter state and trigger product collection reload
 * @param plpManager
 */
export const getApplyFilterHandler: GetApplyFilterHandlerType = (
    filterInputData,
    isApplied = true,
    isSingleSelect = false,
    appliedFilterData,
    filterListType
) => {
    // Clear all filters
    if (!filterInputData || !filterListType) {
        return { filters: null, appliedFilterData: [] }
    }

    return {
        filters: buildFilterInput(filterInputData, isApplied, filterListType),
        appliedFilterData: getAppliedFilterData(
            filterInputData,
            isApplied,
            isSingleSelect,
            appliedFilterData ?? []
        )
    }
}

/**
 * Manage the applied filters
 * return {}
 */
export const buildFilterInput = (
    filterInputData: FilterInputData,
    isApplied: boolean,
    filters: FilterListType
): FilterListType => {
    const { requestVar, value_string } = filterInputData

    if (!isApplied) {
        return deleteFilter(filters, value_string, requestVar)
    }

    // 'eq' ensures that we filter only by one sub-category at a time
    if (requestVar === 'category_uid') {
        return {
            ...filters,
            ...{ [requestVar]: { ['eq']: value_string } }
        }
    }

    if (requestVar === 'price') {
        return buildPriceInput(filters, value_string, requestVar)
    }

    if (requestVar in filters) {
        return buildAttributeInput(filters, value_string, requestVar)
    }

    return {
        ...filters,
        [requestVar]: {
            in: [value_string]
        }
    }
}

const buildPriceInput = (
    filters: FilterListType,
    value_string: string,
    requestVar: string
): FilterListType => {
    const priceRange = value_string.split('_')
    const priceStart = priceRange[0]
    const priceEnd = priceRange[1]
    if (priceStart == '*') {
        return {
            ...filters,
            ...{ [requestVar]: { from: '0', to: priceEnd } }
        }
    } else if (priceEnd == '*') {
        return {
            ...filters,
            ...{ [requestVar]: { from: priceStart } }
        }
    } else if (!isNaN(parseFloat(priceStart)) && !isNaN(parseFloat(priceEnd))) {
        return {
            ...filters,
            ...{ [requestVar]: { from: priceStart, to: priceEnd } }
        }
    }
    return filters
}

const buildAttributeInput = (
    filters: FilterListType,
    value_string: string,
    requestVar: string
): FilterListType => {
    const defaultFilter = filters[requestVar]['in'] ?? []
    return {
        ...filters,
        ...{
            [requestVar]: {
                in:
                    requestVar === ATTR_PRICE_FILTER
                        ? [value_string]
                        : [...defaultFilter, value_string]
            }
        }
    }
}

// Either delete one option from 'in' condition (if multiple options are selected),
// or delete the whole condition
const deleteFilter = (
    filters: FilterListType,
    value_string: string,
    requestVar: string
): FilterListType => {
    const updatedFilters = cloneDeep(filters)
    let filteredCondition: Maybe<Array<Maybe<Scalars['String']>>> = []
    if (updatedFilters[requestVar]) {
        const inCondition = updatedFilters[requestVar]['in'] ?? []
        filteredCondition = inCondition.filter((value: any) => {
            return value !== value_string
        })
    }
    if (filteredCondition && filteredCondition.length > 0) {
        updatedFilters[requestVar]['in'] = filteredCondition
    } else {
        delete updatedFilters[requestVar]
    }
    return updatedFilters
}
