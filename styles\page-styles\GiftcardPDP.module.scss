.breadcrumbs {
    @apply absolute top-10 z-1 font-sofiaSans;
    @screen md-max {
        @apply top-8;
        left: 24px;
    }
    color: white;
    font-size: 12px;
    left: 5.25rem;
}
.title {
    @apply font-tomorrow font-medium uppercase;
    font-size: 28px;
    line-height: 24px;
    margin-bottom: 15px;
}
.container {
    @apply flex m-auto relative my-10 flex-col md:flex-row;
    padding: 0 20px;
    max-width: 1366px;
    justify-content: space-between;
    .gallery-wrapper {
        @apply relative w-full;
        @screen md {
            width: 55%;
            height: min(730px, calc(55vw - 40px));
        }
        background-color: var(--tertiary-light-gray);
        height: calc(100vw - 40px);
        .gallery {
            @apply w-full h-full;
            img,
            span {
                @apply w-full h-full;
            }
        }
    }

    .main {
        @apply w-full;
        margin-top: 40px;

        @screen md {
            width: 43%;
            padding: 0 calc((43% - 345px) / 2);
            margin-top: 0px;
        }

        p {
            @apply uppercase;
        }

        .swatch-container {
            @apply flex flex-wrap gap-5 pb-10;

            .swatch-item {
                @screen md-max {
                    width: calc((100% - 40px) / 3);
                    height: unset;
                    aspect-ratio: 1 / 1;
                }
                @apply relative cursor-pointer;
                width: 100px;
                height: 100px;
                opacity: 0.5;
                &.active {
                    opacity: 1;
                }

                img,
                span {
                    @apply w-full h-full;
                }
            }
        }

        .denominations {
            @apply flex gap-5 w-full flex-wrap pb-14;
            .denomination-item {
                @apply border align-middle text-center flex items-center justify-center cursor-pointer font-medium;
                @screen md-max {
                    width: calc((100% - 20px) / 2);
                }
                width: 162px;
                height: 85px;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
                color: #e0e0e0;
                &.active {
                    border-color: #333132;
                    color: #333132;
                }
            }
        }

        .email-container {
            @apply flex flex-col flex-1 m-0 lg:items-start xl:pb-0 xl:justify-start w-full;
            margin-bottom: auto;

            .inputWrapper {
                @apply w-full flex-col;
            }
            form {
                @apply h-full w-full;
                .main {
                    padding: 0;
                    max-width: 100%;
                    @apply flex-col;
                }

                .emailInput {
                    border-radius: 5px;
                    opacity: unset;
                    outline: none;
                    height: 50px;
                    background-clip: padding-box;
                    border: 1px solid var(--colspan-light-bg);
                    color: var(--secondary-off-black);
                    font-size: 16px;
                    line-height: 24px;
                    padding: 0 17px;
                    vertical-align: baseline;
                    box-sizing: border-box;
                    @apply bg-white font-sofiaSans flex-1;

                    &:focus {
                        border: 1px solid var(--secondary-off-black);
                    }
                    &::placeholder {
                        color: var(--secondary-off-black);
                    }
                }

                .error-input {
                    border: 1px solid var(--feedback-bright-red) !important;
                }
                .success-input {
                    // border: 1px solid var(--feedback-vivid-green) !important;
                }

                button {
                    height: 40px;
                    width: 100px;
                    background-color: var(--secondary-off-black);
                    border-top-right-radius: 5px;
                    border-bottom-right-radius: 5px;
                    font-size: 14px;
                    @apply border-none outline-none cursor-pointer inline-flex items-center justify-center;

                    svg {
                        @apply hidden;
                    }
                }

                .error {
                    @apply text-alert-danger text-sm italic block pt-1;
                }
            }

            .response-message {
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                @apply font-sofiaSans;

                &-error {
                    color: var(--feedback-bright-red);
                }

                &-success {
                    color: var(--feedback-vivid-green);
                }
            }

            &-input-wrapper {
                @apply relative flex;
            }
        }

        .price-add-to-cart {
            @apply flex md:bg-transparent items-center w-full justify-start pt-9;

            @screen lg-1181 {
                justify-content: flex-start;
            }

            @media (max-width: 639px) {
                justify-content: flex-start;
            }

            .add-to-cart-button {
                @apply text-black m-0 font-tomorrow whitespace-nowrap font-medium;
                min-height: 46px;
                min-width: 165px;
                padding: 10px;
                font-size: 16px;
                line-height: 14px;
                transition: 300ms;
                background: var(--primary);
                padding: 13px;
                border-radius: 2px;
                &:not(:disabled):hover,
                &:not(:disabled):focus-visible {
                    color: var(--primary);
                    background-color: var(--secondary-off-black);
                }
            }
        }

        .description {
            @apply font-sofiaSans;
            line-height: 14px;
            margin: 20px 0 30px;
            letter-spacing: 0.2px;
            font-size: 12px;
            white-space: pre-line;
            color: var(--secondary-off-black);
        }
    }
}
