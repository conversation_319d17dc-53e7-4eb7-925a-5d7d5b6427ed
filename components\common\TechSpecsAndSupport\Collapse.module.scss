.container {
    --thickness: 2px;

    @apply w-full;
    padding: 0 20px;
    max-width: unset;
    margin-inline: auto;

    @screen md {
        max-width: 1366px;
    }

    .trigger {
        @apply w-full block;
        color: var(--secondary-off-black);

        &::marker,
        &::-webkit-details-marker {
            display: none;
        }
    }

    .trigger-content {
        @apply text-center cursor-pointer flex flex-col items-center;
        padding-top: 20px;
        padding-bottom: 10px;
        text-decoration: none;
    }

    .heading {
        @apply font-tomorrow font-medium uppercase tracking-normal;
        font-size: 1.125rem;
        line-height: 1.25rem;
    }

    .icon {
        @apply relative cursor-pointer;
        width: 16px;
        height: 16px;
        margin-block: 10px;
        border: var(--thickness) solid var(--secondary-off-black);
        border-radius: 50%;

        &::before,
        &::after {
            @apply inline-block absolute;
            content: '';
            width: calc(100% - 2 * var(--thickness) - 1px);
            background-color: var(--secondary-off-black);
            height: var(--thickness);
            top: 50%;
            left: 50%;
            transition: 0.3s;
        }

        &::before {
            transform: translateY(-50%) translateX(-50%);
        }

        &::after {
            transform: translateY(-50%) translateX(-50%) rotate(90deg);
        }
    }

    &[open] .icon {
        &::before {
            transform: translateY(-50%) translateX(-50%) rotate(-90deg);
            opacity: 0;
        }
        &::after {
            transform: translateY(-50%) translateX(-50%) rotate(0);
        }
    }

    .content {
        @apply grid grid-cols-12;
        column-gap: 20px;
        row-gap: 30px;
        padding-bottom: 20px;
    }
}
