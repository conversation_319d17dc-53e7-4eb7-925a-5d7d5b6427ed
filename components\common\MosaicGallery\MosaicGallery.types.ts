import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface ButtonBlock {
    buttonType: string
    displayText: string
    meta: Meta
    openInANewTab: boolean
    openInPopup: boolean
    showRightArrow: boolean
    url: string
}

export interface Meta {
    contentType: string
}

export interface MosaicGalleryBlock {
    button?: ButtonBlock
    description?: string
    image: CloudinaryImage[]
    meta: Meta
    title: string
    keepOrigin?: boolean
    objectFit?: string
}

export interface MosaicGalleryContent {
    blocks: MosaicGalleryBlock[]
    meta: Meta
    title: string
    titleColor?: string
    backgroundColor?: string
    backgroundImageDesktop?: CloudinaryImage[]
    backgroundImageMobile?: CloudinaryImage[]
    showRearShadow?: boolean
}

export interface MosaicGalleryProps {
    contents: MosaicGalleryContent
}

export type ImgElementStyle = NonNullable<JSX.IntrinsicElements['img']['style']>
