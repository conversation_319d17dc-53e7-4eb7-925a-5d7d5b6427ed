import { createElement, useRef } from 'react'
import cn from 'classnames'

import { CloudinaryImage, CloudinaryVideo } from '../CloudinaryMedia/Cloudinary'
import { VideoModalButton } from '../VideoModalButton'

import s from './HeroVideoCTA.module.scss'

type ContentPosition =
    | 'left'
    | 'top-left'
    | 'bottom-left'
    | 'right'
    | 'top-right'
    | 'bottom-right'
    | 'center'
    | 'top-center'
    | 'bottom-center'

export interface HeroVideoCTAResponse {
    heading: string
    headingType?: 'h1' | 'h2'
    headingColor?: string
    subHeading?: string
    subHeadingColor?: string
    desktopBackgroundImage?: CloudinaryImage[]
    mobileBackgroundImage?: CloudinaryImage[]
    desktopBackgroundImageHeight?: string
    mobileBackgroundImageHeight?: string
    desktopContentPosition?: ContentPosition
    mobileContentPosition?: ContentPosition
    desktopContentWidth?: string
    mobileContentWidth?: string
    // blockPadding?: inlineStyling
    // inlinePadding?: inlineStyling
    video: CloudinaryVideo[]
    videoButtonText: string
    videoTranscript?: string
}

export interface HeroVideoCTAProps {
    content: HeroVideoCTAResponse
}

const HeroVideoCTA: React.FC<HeroVideoCTAProps> = ({ content }) => {
    const containerRef = useRef<HTMLDivElement>(null)

    const {
        desktopContentPosition = 'bottom-left',
        mobileContentPosition = 'bottom-left',
        heading,
        headingType = 'h2',
        headingColor,
        subHeading = '',
        subHeadingColor
    } = content

    const Heading = createElement(
        headingType,
        {
            className: cn(
                s['hero-video-cta--heading'],
                s[`heading-${headingType.toLowerCase()}`]
            ),
            style: { color: headingColor }
        },
        heading
    )

    return (
        <div className={s['hero-video-cta']} ref={containerRef}>
            <section
                className={s['hero-video-cta--section']}
                style={getBackgroundStyle(content)}
            >
                <div
                    data-desktop-position={desktopContentPosition}
                    data-mobile-position={mobileContentPosition}
                    className={s['hero-video-cta--content']}
                    style={getContentStyle(content)}
                >
                    {Heading}
                    {!!subHeading && (
                        <div
                            className={s['hero-video-cta--subheading']}
                            dangerouslySetInnerHTML={{ __html: subHeading }}
                            style={{ color: subHeadingColor }}
                        />
                    )}
                    {content.video?.[0]?.secure_url && (
                        <div className={s['hero-video-cta--button']}>
                            <VideoModalButton
                                label={content.videoButtonText}
                                src={content.video[0].secure_url}
                                transcript={content.videoTranscript}
                            />
                        </div>
                    )}
                </div>
            </section>
        </div>
    )
}

const getBackgroundStyle = (
    content: HeroVideoCTAResponse
): React.CSSProperties => {
    const mobileUrl = content.mobileBackgroundImage?.[0]?.secure_url
    const desktopUrl = content.desktopBackgroundImage?.[0]?.secure_url
    const desktopHeight = content.desktopBackgroundImageHeight
    const mobileHeight = content.mobileBackgroundImageHeight

    return {
        '--desktop-bg-image': `url(${desktopUrl})`,
        '--mobile-bg-image': `url(${mobileUrl || desktopUrl})`,
        '--desktop-bg-height': desktopHeight,
        '--mobile-bg-height': mobileHeight
    } as React.CSSProperties
}

const getContentStyle = (
    content: HeroVideoCTAResponse
): React.CSSProperties => {
    return {
        '--desktop-content-width': content.desktopContentWidth,
        '--mobile-content-width': content.mobileContentWidth ?? 'auto'
    } as React.CSSProperties
}

export default HeroVideoCTA
