import { CategoryInterface, Maybe } from '@pylot-data/pylotschema'

/**
 * Array of category URL paths that are considered accessories
 */
export const ACCESSORY_CATEGORIES = [
    'scuf-accessories',
    'envision-parts',
    'instinct-parts',
    'reflex-parts'
]

/**
 * Checks if a product is an accessory based on its categories
 * @param categories - Array of product categories
 * @returns boolean - True if the product is an accessory, false otherwise
 */
export const isAccessoryProduct = (
    categories: Maybe<Maybe<CategoryInterface>[]> | undefined
): boolean => {
    if (!categories || categories.length === 0) {
        return false
    }

    return categories.some((category) => {
        if (!category || !category.url_path) {
            return false
        }

        // Check if the category URL path matches any of the accessory categories
        return ACCESSORY_CATEGORIES.some((accessoryCategory) =>
            category.url_path?.includes(accessoryCategory)
        )
    })
}
