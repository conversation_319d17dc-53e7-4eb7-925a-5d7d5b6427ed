import { ProductInterface } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { generateRandomString } from 'helpers/stringHelper'
import { useTranslation } from 'next-i18next'
import { useEffect, useState } from 'preact/hooks'
import { FC, useMemo, useRef } from 'react'
import type { Swiper as SwiperCore } from 'swiper'
import { A11y, Mousewheel, Navigation, Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'
import { ICollapseProductBlockProps } from './CollapseProductBlock'
import OfferCard from './OfferCard'
import s from './OfferCarousel.module.scss'
type OffersCarouselProps = {
    name: string
    items: ProductInterface[]
    content: ICollapseProductBlockProps
    showOOSProducts?: boolean
    addProductHandler: (product: ProductInterface, clickFrom: string) => void
}

const OffersCarousel: FC<OffersCarouselProps> = ({
    items,
    content,
    name,
    showOOSProducts,
    addProductHandler
}) => {
    const [itemsNotOOS, setItemsNotOOS] = useState<ProductInterface[]>([])
    const [slidesPerGroup, setSlidesPerGroup] = useState<number | null>()
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const [swiperRef, setSwiperRef] = useState<SwiperCore | null>(null)
    const { t } = useTranslation(['common'])

    const formattedName = useMemo(() => {
        return name
            ? `${generateRandomString(5)}-${name.replace(/[^a-zA-Z]/g, '')}`
            : ''
    }, [name])

    const handleMouseEnterImage = () => {
        if (swiperRef) {
            swiperRef.mousewheel.enable()
        }
    }

    const onAfterSwiperInit = () => {
        insertLine()
    }

    const handleMouseLeaveImage = () => {
        if (swiperRef) {
            swiperRef.mousewheel.disable()
        }
    }

    const animatePagination = (swiper: any) => {
        // Must set timeout to 0 in-order to make this function run at
        // the very last of call stack or else all Element is null
        setTimeout(() => {
            const swiperPagination = paginationRef?.current?.children
            const nextActiveDot = swiperPagination?.item(swiper?.realIndex)
            const activeDot = activeDotRef.current

            if (!nextActiveDot || !activeDot) {
                return
            }

            const activeDotHeight = activeDot.clientHeight / 2
            const nextDotPosition =
                (nextActiveDot as HTMLElement).offsetLeft - 2
            const nextTopDotPos =
                (nextActiveDot as HTMLElement).offsetTop + activeDotHeight
            activeDot.style.left = `${nextDotPosition}px`
            activeDot.style.top = `${nextTopDotPos}px`
        }, 0)
    }

    const insertLine = () => {
        if (!paginationRef.current) return

        const pagination = paginationRef.current
        const bullets = pagination.children

        const halfDotHeight = Math.round(
            ((bullets.item(0) as HTMLElement)?.offsetHeight ?? 11) / 2
        )

        const uniqueLines = new Set()
        const parentEl = pagination.parentElement

        if (!parentEl) {
            return
        }

        Array.from(bullets).forEach((bullet) => {
            uniqueLines.add((bullet as HTMLElement).offsetTop)
        })

        if (uniqueLines.size > 1) {
            parentEl!.style!.width = '80%'
        } else {
            parentEl!.style!.width = 'auto'
        }

        parentEl.querySelectorAll('.dots-line').forEach((e) => e.remove())

        uniqueLines.forEach((val) => {
            const dotLineEl = document.createElement('div')
            dotLineEl.className = 'dots-line'
            dotLineEl.style.top = `${(val as number) + halfDotHeight}px`
            pagination.after(dotLineEl)
        })
    }

    useEffect(() => {
        const filterItemsOOS = showOOSProducts
            ? items
            : items.filter(
                  (item) =>
                      !!item.price_range?.minimum_price?.final_price?.value
              )
        setItemsNotOOS(filterItemsOOS)

        function handleResize() {
            const screenWidth = window && window.innerWidth
            if (screenWidth > 1536) {
                setSlidesPerGroup(4)
            } else if (screenWidth > 1280) {
                setSlidesPerGroup(3)
            } else if (screenWidth > 1024) {
                setSlidesPerGroup(2)
            } else if (screenWidth > 768) {
                setSlidesPerGroup(2)
            } else {
                setSlidesPerGroup(1.25)
            }
        }

        const onResize = () => {
            handleResize()
            insertLine()
            animatePagination(swiperRef)
        }
        onResize()

        // Attach the event listener to the window object
        window.addEventListener('resize', onResize)

        // Remove the event listener when the component unmounts
        return () => {
            window.removeEventListener('resize', onResize)
        }
    }, [items, content])

    useEffect(() => {
        if (swiperRef) {
            swiperRef.mousewheel.disable()
        }
    }, [swiperRef])

    return (
        <section className="w-full relative">
            <div
                className={`flex flex-col md:flex-row justify-start flex-wrap ${s['gap-38']}`}
            >
                {slidesPerGroup &&
                    itemsNotOOS.length <= slidesPerGroup &&
                    itemsNotOOS.map((product: ProductInterface) => (
                        <OfferCard
                            key={product.sku}
                            product={product}
                            content={content}
                            addProductHandler={addProductHandler}
                            className={s}
                        />
                    ))}
            </div>
            {!content.gridDisplay &&
                slidesPerGroup &&
                itemsNotOOS.length > slidesPerGroup && (
                    <>
                        <div className="lg:block hidden">
                            <button
                                className={`${s['offer-carousel-nav-button']} ${s['prev']} ${formattedName}-prev`}
                                aria-label={t('previous')}
                            >
                                Left
                            </button>
                            <button
                                className={`${s['offer-carousel-nav-button']} ${s['next']} ${formattedName}-next`}
                                aria-label={t('next')}
                            >
                                Right
                            </button>
                        </div>

                        <div
                            className={s['offer-carousel-swiper-container']}
                            ref={containerRef}
                        >
                            <Swiper
                                onSwiper={setSwiperRef}
                                className={`w-full ${s.sliderWrapper}`}
                                modules={[
                                    Navigation,
                                    Pagination,
                                    A11y,
                                    Mousewheel
                                ]}
                                onAfterInit={onAfterSwiperInit}
                                speed={500}
                                autoplay
                                loop={false}
                                spaceBetween={16}
                                slidesPerGroup={1}
                                slidesPerView={1.25}
                                navigation={{
                                    prevEl: `.${formattedName}-prev`,
                                    nextEl: `.${formattedName}-next`,
                                    disabledClass: 'offer-carousel-disabled'
                                }}
                                onBreakpoint={(swiper) => {
                                    animatePagination(swiper)
                                }}
                                onSlideChange={(swiper) => {
                                    animatePagination(swiper)
                                }}
                                loopPreventsSlide={false}
                                pagination={{
                                    el: `.${formattedName}-pagination`,
                                    clickable: true,
                                    renderBullet: (idx, className) => {
                                        return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                                    }
                                }}
                                breakpoints={{
                                    768: {
                                        slidesPerView: 2,
                                        slidesPerGroup: 2
                                    },
                                    1024: {
                                        slidesPerView: 2.5,
                                        slidesPerGroup: 2
                                    },
                                    1280: {
                                        slidesPerView: 3,
                                        slidesPerGroup: 3
                                    },
                                    1536: {
                                        slidesPerView: 4.5,
                                        slidesPerGroup: 4
                                    }
                                }}
                            >
                                {itemsNotOOS.map(
                                    (product: ProductInterface) => (
                                        <SwiperSlide key={product.sku}>
                                            <OfferCard
                                                product={product}
                                                content={content}
                                                addProductHandler={
                                                    addProductHandler
                                                }
                                                mouseEnterImage={
                                                    handleMouseEnterImage
                                                }
                                                mouseLeaveImage={
                                                    handleMouseLeaveImage
                                                }
                                            />
                                        </SwiperSlide>
                                    )
                                )}
                            </Swiper>
                            <div className="flex justify-center mt-6 md:mt-11 lg:hidden">
                                <div className="inline-flex justify-center relative">
                                    <div
                                        ref={paginationRef}
                                        className={cn(
                                            `${formattedName}-pagination`,
                                            'inline-flex flex-wrap justify-center gap-4'
                                        )}
                                    />
                                    <div
                                        ref={activeDotRef}
                                        className="active-animated-dot"
                                    />
                                </div>
                            </div>
                        </div>
                    </>
                )}
            {content.gridDisplay &&
                slidesPerGroup &&
                itemsNotOOS.length > slidesPerGroup && (
                    <div
                        className={`flex flex-row justify-start flex-wrap ${s['gap-38']}`}
                    >
                        {itemsNotOOS.map((product: ProductInterface) => (
                            <OfferCard
                                key={product.sku}
                                product={product}
                                content={content}
                                addProductHandler={addProductHandler}
                                className={s}
                            />
                        ))}
                    </div>
                )}
        </section>
    )
}

export default OffersCarousel
