# CTA Components

This directory contains enhanced, reusable Call-to-Action (CTA) button components that provide consistent styling, accessibility, security, and performance across the application.

## 🚀 Recent Improvements

-   **Performance**: Added React.memo, useMemo, and useCallback optimizations
-   **Security**: Implemented HTML sanitization and URL validation
-   **Accessibility**: Enhanced ARIA attributes, keyboard navigation, and screen reader support
-   **Type Safety**: Improved TypeScript interfaces with comprehensive JSDoc documentation
-   **Styling**: Dedicated CSS module with consistent design system integration
-   **Code Quality**: Reduced duplication, better error handling, and maintainable structure

## Components

### CommonButtonContent

A memoized shared content component used by all button variants to ensure consistent text rendering and styling.

**Features:**

-   Automatic translation support
-   Memoized for performance
-   Consistent styling across all variants

### CommonButton

An enhanced button component with accessibility and performance optimizations.

```tsx
import { CommonButton } from '@components/common/CTA'
;<CommonButton
    displayText="Click Me"
    buttonType="primary"
    textColor="#ffffff"
    disabled={false}
    loading={false}
    onClick={() => console.log('Button clicked')}
    aria-label="Custom accessibility label"
/>
```

**Features:**

-   Loading and disabled states
-   Enhanced accessibility with ARIA attributes
-   Performance optimized with memoization
-   Keyboard navigation support

### LinkButton

A secure link button component with URL validation and enhanced accessibility.

```tsx
import { LinkButton } from '@components/common/CTA'
;<LinkButton
    displayText="Learn More"
    buttonType="primary"
    url="/learn-more"
    openInANewTab={true}
    icon={[{ secure_url: '/icon.png' }]}
    disabled={false}
    className={{
        root: 'custom-button',
        wrapper: 'custom-wrapper',
        icon: 'custom-icon'
    }}
/>
```

**Features:**

-   URL validation for security
-   Proper SEO attributes for external links
-   Icon support with lazy loading
-   Enhanced className structure for customization

### ModalButton

A modal button component with secure video handling and sanitized content.

```tsx
import { ModalButton } from '@components/common/CTA'
;<ModalButton
    displayText="Watch Video"
    buttonType="secondary"
    popupVideoUrl="https://youtube.com/watch?v=example"
    videoTranscript="<p>Video transcript content...</p>"
    popupHtmlContent={{ markup: '<div>Custom HTML</div>' }}
/>
```

**Features:**

-   Secure YouTube URL handling with HTTPS enforcement
-   HTML sanitization for video transcripts
-   Enhanced accessibility for modal interactions
-   Support for both video and HTML content

### CTAButton

The main CTA button component with intelligent routing and comprehensive validation.

```tsx
import { CTAButton } from '@components/common/CTA'

// Regular link button
<CTAButton
    displayText="Learn More"
    url="/learn-more"
    buttonType="primary"
    openInANewTab={false}
    meta={{ contentType: 'componentCta' }}
/>

// Modal button with video
<CTAButton
    displayText="Watch Demo"
    buttonType="secondary"
    openInPopup={true}
    popupVideoUrl="https://youtube.com/watch?v=example"
    videoTranscript="<p>Video transcript content...</p>"
    meta={{ contentType: 'componentCta' }}
/>
```

**Features:**

-   Automatic component selection based on props
-   Comprehensive prop validation with helpful warnings
-   Theme support with CSS custom properties
-   Backward compatibility maintained

## 🎨 Button Types

-   **`primary`**: Yellow background (#ECE81A) with black text, hover effects
-   **`secondary`**: Transparent background with white text and border, hover transitions
-   **`link`**: Text-only button with underline, minimal styling

## 📝 Props Interface

### Enhanced CTAClassNames

```typescript
interface CTAClassNames {
    root?: string // Root container class
    text?: string // Text content class
    icon?: string // Icon container class
    wrapper?: string // Button wrapper class
}
```

### Common Props

All button components support these enhanced props:

-   `displayText`: The text to display in the button (required)
-   `buttonType`: The visual style variant ('primary', 'secondary', 'link')
-   `textColor`: Custom text color
-   `className`: Enhanced className structure for granular styling
-   `style`: Inline styles (merged with theme styles)
-   `children`: Additional content to render inside the button
-   `disabled`: Disable button interactions
-   `loading`: Show loading state
-   `aria-label`: Custom accessibility label

### LinkButton Specific Props

-   `url`: The destination URL (validated for security)
-   `openInANewTab`: Whether to open the link in a new tab (default: false)
-   `icon`: Array of CloudinaryImage objects for icons

### ModalButton Specific Props

-   `popupVideoUrl`: URL for video content (YouTube URLs auto-secured to HTTPS)
-   `videoTranscript`: HTML transcript content (automatically sanitized)
-   `popupHtmlContent`: IHTMLContentPage object for custom HTML content

## 🎨 Styling & Theming

The components use a dedicated CSS module (`CTAButton.module.scss`) with support for CSS custom properties:

### CSS Custom Properties

```css
.my-custom-button {
    --button-text-color: #ffffff;
    --button-bg-color: #007bff;
    --button-border-color: #007bff;
}
```

### Responsive Design

-   Mobile-first approach with responsive breakpoints
-   Optimized padding and font sizes for different screen sizes
-   Touch-friendly interaction areas

## 🔒 Security Features

-   **URL Validation**: Prevents malicious URLs and validates protocols
-   **HTML Sanitization**: Uses the established `decode` utility for XSS prevention
-   **HTTPS Enforcement**: Automatically upgrades YouTube URLs to HTTPS
-   **Input Validation**: Comprehensive prop validation with helpful warnings

## ♿ Accessibility Features

-   **ARIA Attributes**: Proper `aria-label`, `aria-disabled`, `aria-expanded`
-   **Keyboard Navigation**: Full keyboard support with proper focus management
-   **Screen Reader Support**: Semantic HTML and descriptive labels
-   **Focus Management**: Visible focus indicators and logical tab order
-   **Modal Accessibility**: Proper modal labeling and content structure

## 🚀 Performance Optimizations

-   **React.memo**: All components are memoized to prevent unnecessary re-renders
-   **useMemo**: Expensive computations are memoized
-   **useCallback**: Event handlers are memoized for stable references
-   **Lazy Loading**: Icons and iframe content use lazy loading
-   **Bundle Optimization**: Tree-shakeable exports

## 🛠 Utility Functions

### Exported Utilities

```typescript
import {
    getButtonTypeClass,
    sanitizeHTML,
    isValidURL
} from '@components/common/CTA'

// Get CSS class for button type
const className = getButtonTypeClass('primary')

// Sanitize HTML content
const safeHTML = sanitizeHTML('<p>User content</p>')

// Validate URL safety
const isValid = isValidURL('https://example.com')
```

## 📚 Migration Guide

The refactored components maintain backward compatibility. However, for optimal performance and features:

1. **Update className usage**: Use the new CTAClassNames structure
2. **Add accessibility labels**: Provide `aria-label` for better accessibility
3. **Use new props**: Take advantage of `disabled` and `loading` states
4. **Update imports**: Import utility functions if needed externally

## 🧪 Testing Recommendations

```typescript
// Test component rendering
import { render, screen } from '@testing-library/react'
import { CTAButton } from '@components/common/CTA'

test('renders CTA button with correct text', () => {
    render(
        <CTAButton
            displayText="Test Button"
            url="/test"
            buttonType="primary"
            meta={{ contentType: 'componentCta' }}
        />
    )
    expect(screen.getByText('Test Button')).toBeInTheDocument()
})

// Test accessibility
test('has proper accessibility attributes', () => {
    render(
        <CTAButton
            displayText="Accessible Button"
            url="/test"
            buttonType="primary"
            aria-label="Custom label"
            meta={{ contentType: 'componentCta' }}
        />
    )
    expect(screen.getByLabelText('Custom label')).toBeInTheDocument()
})
```

### LinkButtonProps

Extends `CommonButtonProps` with:

-   `url: string` - URL to navigate to
-   `openInANewTab?: boolean` - Whether to open link in new tab
-   `icon?: any` - Optional icon to display

### ModalButtonProps

Extends `CommonButtonProps` with:

-   `popupVideoUrl?: string` - URL for video content in modal
-   `videoTranscript?: string` - Transcript text for video
-   `popupHtmlContent?: any` - HTML content to display in modal

## Benefits

1. **Code Reuse**: Common button logic is shared between components
2. **Consistency**: Unified styling and behavior across all button types
3. **Maintainability**: Changes to button logic only need to be made in one place
4. **Flexibility**: Easy to create new button variants using the common components
5. **Type Safety**: Full TypeScript support with proper interfaces
