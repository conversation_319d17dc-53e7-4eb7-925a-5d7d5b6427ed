# CTA Components

This module provides optimized button components for CTAs (Call-to-Action) with shared functionality to reduce code duplication.

## Components

### CommonButtonContent

A reusable component that renders the inner content structure of buttons (text + children). Used internally by other button components.

```tsx
import { CommonButtonContent } from '@components/common/CTA'
;<CommonButtonContent displayText="Button Text" textColor="#ffffff">
    <span>Additional content</span>
</CommonButtonContent>
```

### CommonButton

A reusable button component that handles basic button styling and functionality. Uses CommonButtonContent internally.

```tsx
import { CommonButton } from '@components/common/CTA'
;<CommonButton
    displayText="Click Me"
    buttonType="primary"
    textColor="#ffffff"
    onClick={() => console.log('Button clicked')}
/>
```

### LinkButton

A button component that renders as a link, supporting all link functionality like opening in new tabs.

```tsx
import { LinkButton } from '@components/common/CTA'
;<LinkButton
    displayText="Learn More"
    buttonType="primary"
    url="/learn-more"
    openInANewTab={true}
/>
```

### ModalButton

A button component that displays a modal when clicked, supporting video and HTML content.

```tsx
import { ModalButton } from '@components/common/CTA'
;<ModalButton
    displayText="Watch Video"
    buttonType="secondary"
    popupVideoUrl="https://youtube.com/watch?v=example"
    videoTranscript="Video transcript content..."
/>
```

### CTAButton

The main CTA button component that automatically chooses between regular link buttons and modal buttons based on props.

```tsx
import { CTAButton } from '@components/common/CTA'

// Regular link button
<CTAButton
    displayText="Learn More"
    url="/learn-more"
    buttonType="primary"
    openInANewTab={false}
    meta={{ contentType: 'componentCta' }}
/>

// Modal button (automatically uses ModalButton internally)
<CTAButton
    displayText="Watch Demo"
    openInPopup={true}
    popupVideoUrl="https://youtube.com/watch?v=demo"
    buttonType="secondary"
    meta={{ contentType: 'componentCta' }}
/>
```

## Props

### CommonButtonContentProps

-   `displayText: string` - Text to display
-   `textColor?: string` - Color of the text
-   `children?: React.ReactNode` - Additional content

### CommonButtonProps

Extends `CommonButtonContentProps` with:

-   `buttonType?: btnType` - Button style type ('primary' | 'secondary' | 'link')
-   `className?: object` - Custom CSS classes
-   `style?: React.CSSProperties` - Inline styles
-   `onClick?: () => void` - Click handler
-   `aria-label?: string` - Accessibility label

### LinkButtonProps

Extends `CommonButtonProps` with:

-   `url: string` - URL to navigate to
-   `openInANewTab?: boolean` - Whether to open link in new tab
-   `icon?: any` - Optional icon to display

### ModalButtonProps

Extends `CommonButtonProps` with:

-   `popupVideoUrl?: string` - URL for video content in modal
-   `videoTranscript?: string` - Transcript text for video
-   `popupHtmlContent?: any` - HTML content to display in modal

## Benefits

1. **Code Reuse**: Common button logic is shared between components
2. **Consistency**: Unified styling and behavior across all button types
3. **Maintainability**: Changes to button logic only need to be made in one place
4. **Flexibility**: Easy to create new button variants using the common components
5. **Type Safety**: Full TypeScript support with proper interfaces
