import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import s from './Plpgrid.module.scss'

const c = /*tw*/ {
    noResultsHeader: 'mt-16 h-full text-center text-white',
    searchButton: 'block uppercase font-semibold text-black'
}

type EmptyPLPProp = {
    searchTerm?: string | string[] | undefined
    isMemoryFinder?: boolean
    className?: string
}

const EmptyPLP: React.FC<EmptyPLPProp> = ({
    searchTerm,
    isMemoryFinder,
    className
}) => {
    const { t } = useTranslation('plp')

    if (isMemoryFinder) {
        return (
            <h4 className={cn('text-dark text-center mt-8', className)}>
                {t('ITEMS NOT AVAILABLE PLEASE ADJUST YOUR FILTERS')}
            </h4>
        )
    }

    const emptyPLPLabel = searchTerm ? (
        <>
            {t('0 items found for keyword')}
            {` `}
            <span className="font-semibold">{searchTerm}</span>
        </>
    ) : (
        t('0 products found')
    )

    return (
        <div className={cn(c.noResultsHeader, className)}>
            <div className={s['no-items-text']}>{emptyPLPLabel}</div>

            <Link href="/">
                <button
                    className={`${s['continue-shopping-button']} ${c.searchButton}`}
                    type="button"
                >
                    <span className="pt-2">{t('Continue Shopping')}</span>
                </button>
            </Link>
        </div>
    )
}

export default EmptyPLP
