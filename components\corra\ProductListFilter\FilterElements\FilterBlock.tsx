import { Accordion } from '@components/common/Accordion'
import InfoCircle from '@components/icons/InfoCircle'
import { FilterList } from '@corsairitshopify/corsair-filters-and-sort/src/FilterElements'
import { FilterBlockProps } from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { useTranslation } from 'next-i18next'
import React, { useState } from 'react'
import { FilterExtraInforBlock } from '../ProductListFilter.types'
import styles from './FilterBlock.module.scss'

interface CompProps extends Omit<FilterBlockProps, 'expand'> {
    expand: number | boolean | number[]
    filterExtraInfo?: FilterExtraInforBlock[]
    onSeeExtraInfo?: (info: FilterExtraInforBlock) => void
}

export const FilterBlock = (props: CompProps): JSX.Element | null => {
    const {
        items,
        availableValues,
        name,
        index,
        applyFilter,
        requestVar,
        isLoading,
        showCount,
        currencyCode,
        appliedSearchCritieria,
        appliedFilterData,
        showFilterCount,
        handleChange,
        expand,
        enableUnavailableOptions,
        filterExtraInfo = [],
        onSeeExtraInfo
    } = props
    const { t } = useTranslation(['common'])
    const [localExpand, setLocalExpand] = useState<boolean>(true)
    const getAppliedFilterCount = (requestVar: string) => {
        const count = appliedFilterData.filter(
            (filter) => filter.requestVar == requestVar
        ).length
        return count > 0 ? ` (${count})` : ''
    }

    const getFilterName = (name: string, requestVar: string) => {
        const extraInfo = filterExtraInfo.find((info) => info.title === name)
        return (
            <>
                {name}
                {showFilterCount ? (
                    <span className="filter-count">
                        {getAppliedFilterCount(requestVar)}
                    </span>
                ) : null}
                {extraInfo && (
                    <button
                        className={styles.ExtraInfoButton}
                        onClick={(e) => {
                            e.stopPropagation()
                            onSeeExtraInfo?.(extraInfo)
                        }}
                        aria-label={t('Extra Info')}
                    >
                        <InfoCircle />
                    </button>
                )}
            </>
        )
    }

    const handleChangeLocal = (
        event: React.MouseEventHandler<HTMLButtonElement>,
        isExpanded: boolean
    ) => {
        setLocalExpand(isExpanded)
    }

    return Array.isArray(items) ? (
        <li key={name}>
            <div className="hidden md:block">
                <Accordion
                    key={name}
                    className={styles.FilterBlockAccordion}
                    isOpen={localExpand}
                    title={getFilterName(name || '', requestVar)}
                    onChange={handleChangeLocal}
                >
                    <FilterList
                        items={items}
                        availableValues={availableValues}
                        requestVar={requestVar}
                        applyFilter={applyFilter}
                        isLoading={isLoading}
                        showCount={showCount}
                        currencyCode={currencyCode}
                        appliedSearchCritieria={appliedSearchCritieria}
                        appliedFilterData={appliedFilterData}
                        enableUnavailableOptions={enableUnavailableOptions}
                    />
                </Accordion>
            </div>
            <div className="block md:hidden">
                <Accordion
                    key={name}
                    className={styles.FilterBlockAccordion}
                    isOpen={
                        Array.isArray(expand)
                            ? expand.some((el) => el === index)
                            : expand === index
                    }
                    title={getFilterName(name || '', requestVar)}
                    onChange={handleChange(index)}
                >
                    <FilterList
                        items={items}
                        availableValues={availableValues}
                        requestVar={requestVar}
                        applyFilter={applyFilter}
                        isLoading={isLoading}
                        showCount={showCount}
                        currencyCode={currencyCode}
                        appliedSearchCritieria={appliedSearchCritieria}
                        appliedFilterData={appliedFilterData}
                        enableUnavailableOptions={enableUnavailableOptions}
                    />
                </Accordion>
            </div>
        </li>
    ) : null
}
