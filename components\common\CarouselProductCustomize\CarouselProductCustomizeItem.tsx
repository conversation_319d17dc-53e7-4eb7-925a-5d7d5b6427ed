import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { ProductCustomizeType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselProductCustomize.module.scss'
import Link from 'next/link'
import { Button } from '@corsairitshopify/pylot-ui'
import CustomizeIcon from '@components/icons/PDP/CustomizeIcon'

export type CarouselProductCustomizeItemProps = {
    product: ProductCustomizeType
}

const CarouselProductCustomizeItem: FC<CarouselProductCustomizeItemProps> = ({
    product
}) => {
    const { t } = useTranslation(['common'])

    // const { region, language } = useBaseUrl(
    //     typeof window !== 'undefined' ? window.location.href : ''
    // )

    const {
        backgroundColor = 'var(--secondary-off-black)',
        textColor = 'var(--white)',
        url
    } = product

    return (
        <div
            className={cn(s['slider-item-container'])}
            style={{ backgroundColor }}
        >
            <div
                className={cn(
                    s['slider-item-image-container'],
                    'relative w-full h-full'
                )}
            >
                <CorsairImage
                    keepOrigin
                    src={convertUrlFormat(product.image?.[0]?.secure_url)}
                    alt={t(`alt|${product.image?.[0]?.context?.custom?.alt}`)}
                    layout="fill"
                    objectFit="contain"
                />
            </div>
            <div className={s['slider-item-divider']} />
            <div
                className={s['slider-item-text-block']}
                style={{ color: textColor }}
            >
                <div className={s['slider-item-name']}>{product.name}</div>
                <div className={s['slider-item-compatibility']}>
                    {product.compatibility}
                </div>
                <div className={s['slider-item-description']}>
                    {product.description}
                </div>
            </div>
            <div className={s['build-your-own-btn']}>
                <Link href={url}>
                    <a className="w-full inline-block">
                        <Button className={cn('scuf-button-secondary')}>
                            <CustomizeIcon />
                            {t('Build your own')}
                        </Button>
                    </a>
                </Link>
            </div>
        </div>
    )
}

export default CarouselProductCustomizeItem
