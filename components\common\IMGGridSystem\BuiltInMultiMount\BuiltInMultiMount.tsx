import Slider from 'react-slick'
import { IMGGridSystemProps, CustomArrowProps } from '../types'
import ChevronRight from '@components/icons/ChevronRight'
import ChevronLeft from '@components/icons/ChevronLeft'
import { twc } from './BuiltInMultiMountStyling'
import { HeaderMount } from './HeaderMount'
import Skeleton from 'react-loading-skeleton'
import { useTranslation } from 'next-i18next'
import s from './BuiltInMount.module.scss'
import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'

type Props = {
    content: IMGGridSystemProps
}

function SampleNextArrow(props: CustomArrowProps) {
    const { t } = useTranslation(['common'])
    const { style, onClick } = props
    return (
        <div
            className={twc.next}
            style={{ ...style }}
            onClick={onClick}
            role="button"
            tabIndex={0}
            aria-hidden="true"
        >
            <ChevronRight className={twc.rightArrow} />
        </div>
    )
}

function SamplePrevArrow(props: CustomArrowProps) {
    const { style, onClick } = props
    return (
        <div
            className={twc.prev}
            style={{ ...style }}
            onClick={onClick}
            role="button"
            tabIndex={0}
            aria-hidden="true"
        >
            <ChevronLeft className={twc.leftArrow} />
        </div>
    )
}

const BuiltInMultiMount = ({ content }: Props): JSX.Element | null => {
    const { t } = useTranslation(['common'])
    if (!content) return <Skeleton height="35vw" />

    const settings = {
        dots: false,
        infinite: true,
        speed: 300,
        slidesToShow: 1,
        slidesToScroll: 1,
        nextArrow: <SampleNextArrow />,
        prevArrow: <SamplePrevArrow />,
        arrrow: true
    }
    return (
        <section className={s['builtinMount']}>
            <HeaderMount content={content} />
            <div className={s['slick-container']}>
                <Slider {...settings}>
                    {content.cloudinaryMobileMedia?.map((d, key) => (
                        <div className={s['slick-height']} key={key}>
                            {d?.secure_url && (
                                <CorsairImage
                                    keepOrigin
                                    src={convertUrlFormat(d.secure_url)}
                                    alt={correctImageAlt(
                                        t(`alt|${d?.context?.custom?.alt}`)
                                    )}
                                    layout="fill"
                                    objectFit="cover"
                                />
                            )}
                        </div>
                    ))}
                </Slider>
            </div>
        </section>
    )
}
export default BuiltInMultiMount
