import { FC } from 'react'
import cn from 'classnames'
import s from '@pagestyles/GiftcardPDP.module.scss'

type Props = {
    options: string[]
    value: string
    onChange: (d: string) => void
    amountTitle?: string
}

export const GiftcardDenominationSelector: FC<Props> = ({
    options,
    value,
    onChange,
    amountTitle
}) => (
    <>
        {amountTitle && <p className={s['title']}>{amountTitle}</p>}
        <div className={s.denominations}>
            {options.map((d) => (
                <div
                    role="button"
                    tabIndex={0}
                    key={d}
                    className={cn(
                        s['denomination-item'],
                        d === value && s.active
                    )}
                    onClick={() => onChange(d)}
                    onKeyPress={() => onChange(d)}
                >
                    {d}
                </div>
            ))}
        </div>
    </>
)
