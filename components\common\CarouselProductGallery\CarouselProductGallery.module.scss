.pagination-container {
    &.pagination-container {
        @screen md-max {
            @apply flex items-center justify-around;
            margin-top: 40px;
        }
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--primary);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }
}

.product-gallery-swiper-container {
    .faded-left {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to left, var(--gradient-stops));
        transition: opacity 0.5s ease-in-out;
        @apply absolute top-0 left-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .faded-right {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to right, var(--gradient-stops));
        transition: opacity 0.5s ease-in-out;
        @apply absolute top-0 right-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .prev:disabled,
    .prev:disabled ~ .faded-left {
        @apply opacity-0;
    }
    .prev:disabled ~ .swiper-container {
        transform: translateX(20px);
        @screen md {
            transform: translateX(80px);
        }
    }
    .next:disabled,
    .next:disabled ~ .faded-right {
        @apply opacity-0;
    }
    .next:disabled ~ .swiper-container {
        transform: translateX(-20px);
        @screen md {
            transform: translateX(-80px);
        }
    }
    .prev:disabled + .next:disabled {
        & ~ .swiper-container {
            transform: translateX(0px) !important;
            padding-inline: 20px;
            @screen md {
                padding-inline: 80px;
            }
        }
    }

    @screen md-max {
        .faded-left,
        .faded-right {
            display: none;
        }
    }

    .slider-item-container {
        @apply flex flex-col;

        .slider-item-text-block {
            padding: 24px 36px;

            .slider-item-heading {
                @apply font-medium;
                font-size: 32px;
                line-height: 38.4px;
                margin-bottom: 12px;

                @screen md-max {
                    font-size: 22px;
                    line-height: 100%;
                    letter-spacing: 0%;
                    @apply font-primary font-medium;
                }
            }

            .slider-item-cta {
                display: flex !important;
                align-items: center !important;
            }

            .slider-item-cta-text {
                @apply font-primary font-medium;
                font-size: 16px;
                line-height: 20.26px;
            }
        }

        .slider-item-image-container {
            flex: 1 1;
        }
    }
    @apply relative;

    .swiper-container {
        @screen md-max {
            padding-inline: 20px !important;
            transform: translateX(0px) !important;
        }

        transition: transform 0.1s linear;

        @screen md {
            transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        }
    }

    .carousel-nav-button {
        transform: translateY(-50%);
        width: 52px;
        height: 48px;
        @apply z-10 top-1/2 absolute flex items-center justify-center overflow-hidden;

        :global(#Oval-2) {
            stroke: transparent;
        }

        div {
            button {
                &:focus {
                    outline: none;
                }
            }
        }

        &.prev {
            left: 3%;
            @screen md-max {
                @apply hidden;
            }

            svg {
                transform: rotateZ(180deg);
            }
        }

        &.next {
            right: 3%;
            @screen md-max {
                @apply hidden;
            }
        }
    }
}
