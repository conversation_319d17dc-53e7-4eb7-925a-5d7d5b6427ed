import { FC } from 'react'
import Link from 'next/link'
import s from './HydroXBlock.module.scss'
import cn from 'classnames'
import CorsairImage from '@corsairitshopify/corsair-image'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
export interface HydroXCards {
    title: string
    url: string
    image?: CloudinaryImage[]
    Key: string
    newTab: boolean
    heading: string
    description: string
    cloudinaryImage?: CloudinaryImage[]
}

const twClasses = /*tw*/ {
    imageHeading:
        'text-white font-semibold font-secondary absolute flex justify-center items-center w-full h-full px-2.5 py-0 text-center'
}
export const HydroXBlockItem: FC<HydroXCards> = ({
    title,
    url,
    image,
    newTab
}): JSX.Element => {
    const { t } = useTranslation(['common'])
    return (
        <Link href={url || ''}>
            <a target={newTab ? '_blank' : '_self'}>
                <div
                    className={cn(
                        s['ImageWithText'],
                        'flex flex-col relative cursor-pointer'
                    )}
                >
                    {image?.[0]?.secure_url && (
                        <CorsairImage
                            keepOrigin
                            alt={correctImageAlt(
                                t(`alt|${image?.[0]?.context?.custom?.alt}`)
                            )}
                            height={120}
                            src={convertUrlFormat(image?.[0]?.secure_url)}
                            width={148}
                            layout="responsive"
                        />
                    )}

                    <div
                        className={cn(
                            s['ImageHeading'],
                            s['hydroXblock-fonts'],
                            twClasses.imageHeading
                        )}
                    >
                        {title}
                    </div>
                </div>
            </a>
        </Link>
    )
}

export default HydroXBlockItem
