import { PopupVideoPlayer } from '@components/common/Banner23Tile/Banner2Tile/PopupVideoPlayer'
import Modal from '@corsairitshopify/pylot-ui/src/Modal/Modal'
import { ClickOutside } from '@corsairitshopify/pylot-utils'
import { useTranslation } from 'next-i18next'
import { FC, useState } from 'react'
import { ImageWithDescriptionProps } from '../types'
import s from './ImageWithDescription.module.scss'
import { default as Desktop } from './ImageWithDescriptionDesktop'
import { default as Mobile } from './ImageWithDescriptionMobile'

const ImageWithDescription: FC<ImageWithDescriptionProps> = ({
    content
}): JSX.Element | null => {
    const { t } = useTranslation('common')
    const [isOpen, setIsOpen] = useState(false)
    return (
        <div
            className={s['image-with-description-container']}
            style={{ backgroundColor: content?.backgroundColor }}
        >
            <div className="text-center mb-24">
                {content?.headingType === 'H1' ? (
                    <h1
                        className={`${s['heading']} text-yellow font-tomorrow uppercase font-semibold`}
                    >
                        {content?.heading}
                    </h1>
                ) : (
                    <h2
                        className={`${s['heading']} text-yellow font-tomorrow uppercase font-semibold`}
                    >
                        {content?.heading}
                    </h2>
                )}
                <h2
                    className={`${s['sub-heading']} text-white font-tomorrow uppercase font-semibold`}
                >
                    {content?.subheading}
                </h2>
                <button
                    className={`${s['open-video-button']} bg-white text-black uppercase font-medium cursor-pointer font-tomorrow focus:outline-none popup-youtube`}
                    onClick={(e) => {
                        e.preventDefault()
                        setIsOpen(true)
                    }}
                >
                    {t('Watch Video')}
                </button>
            </div>
            <div>
                <Desktop content={content} />
                <Mobile content={content} />
            </div>
            <ClickOutside active={isOpen} onClick={() => setIsOpen(false)}>
                <div>
                    <Modal
                        className={s['vidgallery-item-modal']}
                        open={isOpen}
                        onClose={() => setIsOpen(false)}
                        focusFirst={false}
                    >
                        <PopupVideoPlayer videourl={content!.ctaButton} />
                    </Modal>
                </div>
            </ClickOutside>
        </div>
    )
}

export default ImageWithDescription
