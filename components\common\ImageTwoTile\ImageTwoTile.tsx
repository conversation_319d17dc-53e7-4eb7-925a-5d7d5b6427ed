import { FC, useRef } from 'react'
import cn from 'classnames'
import s from './ImageTwoTile.module.scss'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import CorsairImage from '@corsairitshopify/corsair-image'

const DEFAULT_TEXT_CONTAIN_SUBHEAD_COLOR = '#ece81a'

export interface ItextContain {
    heading: string
    meta: { contentType: string }
    subHeading: string
    text: string
    title: string
}

export interface ImageTwoTileResponse {
    title: string
    textContainData?: ItextContain
    cloudinaryImage1: CloudinaryImage[]
    cloudinaryImage2: CloudinaryImage[]
    cloudinaryBackgroundImage?: CloudinaryImage[]
    heading: string
    subheading: string
    headingsPosition: boolean
    headingType: string
    textContainSubheadingColor: string
    backgroundColorHex?: string
    textColor?: string
    cta: {
        displayText: string
        textColor: string
        url: string
        openInANewTab: boolean
    }
}

interface ImageTwoTilePrpos {
    content: ImageTwoTileResponse
}
const c = {
    imageTwoTile: `${s['imageTwoTile']} relative pt-32 text-2xl lg:px-20 box-border mx-auto`,
    imageBg: `${s['imageTwoTile-BackgroundImage']} mt-32 px-6`,
    imageContainer: `${s['imageTwoTile-imageContainer']} md:flex md:justify-center md:items-center`,
    colOne: `${s['Image-Col-one']} md:pl-4 md:pr-4 md:mr-10 md:ml-10 flex flex-col`,
    colTwo: `${s['Image-Col-two']} md:pl-4 md:pr-4 md:mr-10 md:ml-10 pt-20 pb-10 md:pb-20 flex flex-col`,
    imgWithDescription: `${s['imageTwoTile-imageWithDescp']} rounded-2xl`,
    subHeading: `${s['imageTwoTile-subHeading']} font-primary`,
    Heading: `${s['Heading']} font-secondary text-3xl leading-none mb-4 md:mb-10`,
    descriptiion: `${s['imageTwoTile-Description']} font-primary text-lg lg:text-3xl leading-normal md:leading-10`,
    cta: `${s['cta']} text-base md:text-xl`,
    icon: 'ml-1 md:ml-2.5 flex items-center'
}
const DEFAULT_BACKGROUND_GRADIENT =
    'linear-gradient(135deg, rgba(24, 46, 40, 0), #000 33%, #000 66%, transparent)'
const DEFAULT_CTA_COLOR = '#333'
const ImageTwoTile: FC<ImageTwoTilePrpos> = ({ content }) => {
    const animateRef = useRef(null)
    const { isOnScreen } = useOnScreen(animateRef, true)

    if (!content) {
        return null
    }

    const styles = {
        background: content?.cloudinaryBackgroundImage?.[0]?.secure_url
            ? `url("${convertUrlFormat(
                  content.cloudinaryBackgroundImage[0].secure_url
              )}") 100% 100% repeat fixed`
            : '',
        backgroundSize: '42px 68px'
    }

    const {
        textContainData,
        heading,
        subheading,
        headingsPosition,
        headingType,
        textContainSubheadingColor,
        textColor = '#fff',
        backgroundColorHex = DEFAULT_BACKGROUND_GRADIENT,
        cta
    } = content
    return (
        <div
            className="Image-Two-Tile relative"
            style={content?.cloudinaryBackgroundImage && styles}
        >
            <div
                style={{
                    background:
                        backgroundColorHex !== 'transparent'
                            ? backgroundColorHex
                            : DEFAULT_BACKGROUND_GRADIENT,
                    color: textColor
                }}
            >
                {(heading || subheading) && (
                    <div className={s['header']}>
                        {subheading && (
                            <p
                                className={`${s['subheading']} ${
                                    headingsPosition ? 'text-left' : ''
                                }`}
                            >
                                {subheading}
                            </p>
                        )}
                        {heading &&
                            (headingType === 'H1' ? (
                                <h1
                                    className={`${s['heading']} ${
                                        headingsPosition ? 'text-left' : ''
                                    }`}
                                >
                                    {heading}
                                </h1>
                            ) : (
                                <h2
                                    className={`${s['heading']} ${
                                        headingsPosition ? 'text-left' : ''
                                    }`}
                                >
                                    {heading}
                                </h2>
                            ))}
                    </div>
                )}
                <div className={c.imageTwoTile}>
                    <div className={c.imageBg}>
                        <div className={c.imageContainer}>
                            {content?.cloudinaryImage1?.[0]?.secure_url && (
                                <div className={c.colOne}>
                                    <CorsairImage
                                        keepOrigin
                                        className={c.imgWithDescription}
                                        src={convertUrlFormat(
                                            content.cloudinaryImage1[0]
                                                .secure_url
                                        )}
                                        alt={
                                            content.cloudinaryImage1[0].context
                                                ?.custom?.alt || ''
                                        }
                                        width={552}
                                        height={564}
                                        objectFit="cover"
                                    />
                                    <div
                                        className={cn(
                                            s['imageTwoTile-ImageText'],
                                            'pt-4',
                                            s['has-animate'],
                                            { [s['onScreen']]: isOnScreen }
                                        )}
                                        ref={animateRef}
                                    >
                                        <h6
                                            style={{
                                                color:
                                                    textContainSubheadingColor ||
                                                    DEFAULT_TEXT_CONTAIN_SUBHEAD_COLOR
                                            }}
                                            className={c.subHeading}
                                        >
                                            {textContainData?.subHeading}
                                        </h6>
                                        <h2 className={c.Heading}>
                                            {textContainData?.heading}
                                        </h2>
                                        <div className={c.descriptiion}>
                                            {textContainData?.text}
                                        </div>
                                        {cta && (
                                            <div className={c.cta}>
                                                <a
                                                    href={cta.url!}
                                                    target={
                                                        cta.openInANewTab
                                                            ? '_blank'
                                                            : '_self'
                                                    }
                                                    className="corsair-links"
                                                    rel="noopener noreferrer"
                                                    style={{
                                                        color:
                                                            cta.textColor ||
                                                            DEFAULT_CTA_COLOR
                                                    }}
                                                >
                                                    <span className="uppercase font-semibold">
                                                        {cta.displayText}
                                                    </span>
                                                    <div className={c.icon}>
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            width="7"
                                                            height="12"
                                                            viewBox="0 0 7 12"
                                                            fill="none"
                                                        >
                                                            <path
                                                                d="M1.33198 0L0 1.41L4.33603 6L0 10.59L1.33198 12L7 6L1.33198 0Z"
                                                                fill={
                                                                    cta.textColor ||
                                                                    DEFAULT_CTA_COLOR
                                                                }
                                                            />
                                                        </svg>
                                                    </div>
                                                </a>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                            {content?.cloudinaryImage2?.[0]?.secure_url && (
                                <div
                                    className={cn(
                                        c.colTwo,
                                        s['has-animate-image'],
                                        { [s['onScreen']]: isOnScreen }
                                    )}
                                >
                                    <CorsairImage
                                        keepOrigin
                                        className={cn(
                                            s['imageTwoTile-imageWithDescp'],
                                            'rounded-2xl'
                                        )}
                                        src={convertUrlFormat(
                                            content.cloudinaryImage2[0]
                                                .secure_url
                                        )}
                                        alt={
                                            content.cloudinaryImage2[0].context
                                                ?.custom?.alt || ''
                                        }
                                        width={552}
                                        height={568}
                                        objectFit="cover"
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ImageTwoTile
