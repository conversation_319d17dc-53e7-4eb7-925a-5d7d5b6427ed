import { CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import { Ref } from 'react'
import Slider from 'react-slick'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { IMeta } from '../types'

export type CarouselImageTextProp = {
    content: CarouselImageTextSlideType
}

export type CarouselImageTextSlideProp = {
    slides: CarouselImageTextSlideType[]
    current: number
    onChange: (next: number) => void
    ref: Ref<Slider>
}

export type ImageTextSlideType = {
    title: string
    heading?: string
    headingColor?: string | undefined
    copyColor?: string | undefined
    cta?: CTAType
    cloudinaryMainImage: CloudinaryImage[]
    backgroundColor?: string | undefined
    backgroundImageDesktop?: CloudinaryImage[]
    backgroundImageMobile?: CloudinaryImage[]
    meta: IMeta<'cslImagetextSlide'>
    quote?: string
}

export type ProductsSlideType = {
    identifier: string
    heading: string
    headingColor?: string | undefined
    cta?: CTAType
    cloudinaryMainImage: CloudinaryImage[]
    cloudinarySecondImage?: CloudinaryImage[]
    backgroundColor?: string | undefined
    textColor?: string
    description?: string
    backgroudColor?: string
    productPrice?: string
    imageBackgroundColor: string
    url?: string
    showAnimation?: boolean
}

export type ProductGalleryType = {
    identifier: string
    image: CloudinaryImage[]
}

export type ProductCustomizeType = {
    identifier: string
    image: CloudinaryImage[]
    name: string
    compatibility: string
    description: string
    url: string
    backgroundColor?: string
    textColor?: string
}

export type BackgroundVideoType = {
    identifier: string
    cloudinaryBackgroundVideo: CloudinaryImage[]
    cloudinaryImage: CloudinaryImage[]
    heading: string
    copy?: string
    cta?: CTAType
}

export type CarouselWrapperType = {
    identifier: string
    displayType: string
    heading?: string
    headingColor?: string
    headingType?: 'h1' | 'h2' | 'h3' | 'h4'
    productSlide:
        | ProductsSlideType[]
        | ImageTextSlideType[]
        | BackgroundVideoType[]
        | ProductGalleryType[]
        | ProductCustomizeType[]
    meta: IMeta<'carouselppContainer'>
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryMobileBackgroundImage?: CloudinaryImage[]
    backgroundColor?: string
    desktopPaddingTop?: string
    desktopPaddingBottom?: string
    mobilePaddingTop?: string
    mobilePaddingBottom?: string
    padding: 'none' | 'tiny' | 'small' | 'medium' | 'large'
    navigationButtonsPosition: 'top' | 'default'
    showStickyBanner?: boolean
    stickyBannerLabel?: string
    stickyBannerBtnLabel?: string
    showRearShadow?: boolean
}

export type CarouselImageTextSlideType = {
    title: string
    heading?: string
    text?: string
    cloudinaryMainImage: CloudinaryImage[]
    imageWidth?: string
    imageHeight?: string
    cloudinarySecondaryImage?: CloudinaryImage[]
    secondaryImageCoordinates?: ImageCoordinates
    meta: IMeta<'cslImagetextSlide'>
}

export type ImageCoordinates = {
    top?: string
    left?: string
    meta: IMeta<'calloutTextPoints'>
}

export type CarouselSlideProp = {
    slide: CarouselImageTextSlideType
}

export type CarouselWrapperProps = {
    content: CarouselWrapperType
}
