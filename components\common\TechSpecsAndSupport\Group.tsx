import React from 'react'
import cn from 'classnames'

import s from './Group.module.scss'
import { GroupProps } from './types'

const Group: React.FC<GroupProps> = ({
    className,
    heading,
    gap = 20,
    children
}) => {
    return (
        <div
            className={cn(s['group'], className)}
            style={{ '--item-gap': `${gap}px` } as React.CSSProperties}
        >
            <div className={s['group--heading']} aria-label="heading">
                {heading}
            </div>
            <div className={s['group--content']}>{children}</div>
        </div>
    )
}

export default Group
