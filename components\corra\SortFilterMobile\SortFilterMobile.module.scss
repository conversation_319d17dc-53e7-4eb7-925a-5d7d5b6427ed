.SortFilterMobileWrapper {
    max-height: 100vh;
    left: 0;
    top: 0;
    // z-index above header
    z-index: 1000;
    box-shadow: 0px 8px 40px 0px #00000029;
    @apply fixed bg-white w-full animate-fade-in-right overflow-y-auto flex flex-col;

    .ButtonGroupSection {
        padding-block: 8px;
        border-top: 1px solid #dbdbdb;
        border-bottom: 1px solid #dbdbdb;
        @apply flex;

        button {
            gap: 9px;
            font-size: 18px;
            line-height: 22px;
            @apply flex flex-1 justify-center items-center font-semibold font-sofiaSans uppercase;

            &:not(:last-child) {
                border-right: 1px solid #dbdbdb;
            }
        }
    }

    .MainContent {
        max-height: 100%;
        @apply flex-1 overflow-y-scroll;

        [class*='layered-navigations'] {
            z-index: 1001;
            @apply static h-full;
        }
    }

    .Footer {
        border-top: 1px solid #4d5257;
        box-shadow: 0px 0px 50px 0px #00000040;
        padding-block: 18px;
        gap: 10px;
        @apply flex flex-col items-center;

        .ApplyBtn {
            width: 183px;
            height: 36px;
            gap: 8px;
            font-size: 14px;
            line-height: 22px;
            @apply flex font-semibold items-center justify-center font-sofiaSans;
        }

        .ClearAllBtn {
            height: 28px;
            font-size: 14px;
            line-height: 22px;
            @apply text-black font-bold bg-white text-center font-sofiaSans;
        }
    }
}
