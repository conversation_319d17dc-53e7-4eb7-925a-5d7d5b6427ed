import { CSSProperties, useEffect, useRef } from 'react'
interface VideoFilePlayerType {
    secure_url: string
    className?: string
    fallbackImgUrl?: string
    style?: CSSProperties
    autoPlay?: boolean
    controls?: boolean
    muted?: boolean
    loop?: boolean
    videoDescription?: string
}
const CorsairVideo = ({
    secure_url,
    className,
    style,
    fallbackImgUrl = '',
    autoPlay = true,
    controls = false,
    muted = true,
    loop = true,
    videoDescription
}: VideoFilePlayerType): JSX.Element | null => {
    const videoRef = useRef() as React.MutableRefObject<HTMLVideoElement>
    return (
        <video
            autoPlay={autoPlay}
            controls={controls}
            loop={loop}
            playsInline
            preload=""
            poster={fallbackImgUrl}
            muted={muted}
            style={style}
            className={className}
            ref={videoRef}
        >
            <track kind="captions" />
            <source src={secure_url} />
            {videoDescription && <p className="sr-only">{videoDescription}</p>}
        </video>
    )
}

export default CorsairVideo
