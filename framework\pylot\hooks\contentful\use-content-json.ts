import { CloudinaryImage } from '@components/common/CloudinaryMedia/Cloudinary'
import { IHTMLContentPage, IMeta } from '@components/common/types'
import { EPreviewHeader } from '@pylot-data/getRequestHeaders'
import { GraphQLError } from '@pylot-data/graphqlFetch'
import { RequestInit } from '@vercel/fetch'
import { getPreviewDataFromLocalStorage } from 'helpers/getPreviewDataFromLocalStorage'
import type { SWRConfiguration } from 'swr'
import { useQuery } from '../use-query'
import { getContentJsonQuery } from './graphql/getContentJsonQuery'

export interface VideoFileType {
    url: string
    details: {
        size: number
    }
    fileName: string
    contentType: string
}

export interface VideoType {
    title: string
    description: string
    file: VideoFileType
}

export interface FileType {
    url: string
    details: {
        size: number
        image: {
            width: number
            height: number
        }
    }
    fileName: string
    contentType: string
}

export interface ImageType {
    title: string
    description: string
    file: FileType
}
export interface BackgroundImageType {
    title?: string
    description?: string
    file: {
        url: string
        fileName: string
        contentType: string
        details: string
    }
    meta?: any
}
export interface ImageLinkType {
    title: string
    heading?: string
    headingColor?: string
    image: ImageType
    width?: number
    height?: number
    url: string
    description?: string
    newTab: boolean
    backgroundImage: BackgroundImageType
    cloudinaryImage?: CloudinaryImage[]
    cloudinaryBackgroundImage?: CloudinaryImage[]
}

export interface ContentJsonItem<T> {
    parsedEntries: T & { meta: { contentType: string } }
    identifier: string
    entries: string
}

export interface ContentJson<T> {
    contentJson: ContentJsonItem<T>[]
}

export interface ContentJsonResponse<T> {
    data?: ContentJsonItem<T>[]
    isValidating?: boolean
    error?: GraphQLError
}

export interface ContentJsonOptionsInput {
    queryField?: string
    level?: number
}

export interface QueryVariables {
    identifier?: string[]
    contentType?: string
    options?: ContentJsonOptionsInput
}

export type ctaLayout = 'With Border' | 'With Background'
export type btnType = 'primary' | 'secondary' | 'link'
export interface CTAType {
    displayText: string
    url: string
    image?: ImageType
    openInANewTab: boolean
    openInPopup?: boolean
    meta?: IMeta<'componentCta'>
    cloudinaryImage?: CloudinaryImage[]
    textColor?: string
    backgroundColor?: string
    buttonType?: btnType
    showRightArrow?: boolean
}

export interface CTAButtonType {
    displayText: string
    textColor?: string
    url: string
    openInANewTab: boolean
    openInPopup?: boolean
    popupVideoUrl?: string
    videoTranscript?: string
    popupHtmlContent?: IHTMLContentPage
    icon?: CloudinaryImage[]
    iconPosition?: boolean
    showRightArrow?: boolean
    buttonType?: btnType
    className?: {
        root?: string
        text?: string
        icon?: string
    }
    style?: React.CSSProperties
    children?: React.ReactNode
    image?: ImageType
    meta?: IMeta<'componentCta'>
}

export interface IconType {
    title: string
    image: ImageType
    position: string
    color: IconType
    cloudinaryImage?: CloudinaryImage[]
}

export interface URLType {
    mobileVisibility?: boolean
    text: string
    url: string
    target: string
    color: string
    icon: IconType
    iconPosition?: boolean
    cta?: boolean
}

export interface EmbedVideoType {
    title: string
    url: string
}
export interface cloudinaryDimensionsModelType {
    cloudinaryDimensionsModelId: string
    arViewerDescription: string
}
export interface BackgroundColorType {
    gradientColor: boolean
    gradient: string
    opacity: number
    colorHex: string
    backgroundImage: ImageType
}

export const useContentJson = <T>(
    queryVariables: QueryVariables,
    swrOptions?: SWRConfiguration,
    fetchOptions?: RequestInit
): ContentJsonResponse<T> => {
    const previewDataFromStorage = getPreviewDataFromLocalStorage()

    if (previewDataFromStorage?.isPreview) {
        fetchOptions = fetchOptions ? fetchOptions : {}
        fetchOptions.headers = {
            ...fetchOptions?.headers,
            [EPreviewHeader.preview]: `${previewDataFromStorage.isPreview}`,
            [EPreviewHeader.previewDate]: `${
                previewDataFromStorage.previewDate || ''
            }`
        }
    }

    const { data, error, isValidating } = useQuery<
        QueryVariables,
        ContentJson<T>
    >(getContentJsonQuery, queryVariables, swrOptions, fetchOptions)

    if (data?.data?.contentJson) {
        try {
            data.data.contentJson.forEach((content) => {
                if (typeof content.entries === 'string') {
                    content.parsedEntries = JSON.parse(content.entries)
                }
            })
        } catch (err) {
            console.log(
                `Error parsing content json for ${JSON.stringify(
                    queryVariables
                )}`
            )
            console.error(err)
        }
    }

    return {
        data: data?.data?.contentJson,
        isValidating,
        error
    }
}
