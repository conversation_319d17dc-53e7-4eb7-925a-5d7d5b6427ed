import React from 'react'
import { IHTMLContentPage } from '../types'
import { useScript } from './hooks/useScript'
import decode from '../../../lib/utils/htmlencoder'

interface HtmlContentPagecontent {
    content: IHTMLContentPage
}

const HtmlContentPage = ({ content }: HtmlContentPagecontent) => {
    //inject linked js to render in order

    useScript(content.linkedScript)

    const encodedHtml = decode(content.markup)

    const externalCss: string[] = []

    if (content.linkedScript?.length) {
        content.linkedScript?.forEach((data) => {
            if (data.linkedCss?.length) {
                data.linkedCss?.forEach((link) => {
                    if (externalCss.indexOf(link) === -1) {
                        externalCss.push(link)
                    }
                })
            }
        })
    }
    const linkedCss = externalCss.map((link) => (
        <link key={link} href={link} rel="stylesheet" type="text/css" />
    ))

    return (
        <>
            <div>{linkedCss}</div>
            <div
                dangerouslySetInnerHTML={{
                    __html: encodedHtml
                }}
            />
        </>
    )
}

export default HtmlContentPage
