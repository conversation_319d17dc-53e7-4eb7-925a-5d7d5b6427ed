import { CTA } from '@components/common/CTA'
import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { btnType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { HeroStandardResponse } from './HeroStandard'
import s from './HeroStandard.module.scss'

export type HeroStandardCTAProps = {
    content: HeroStandardResponse
}

const c = /*tw*/ {
    heroStandardCTAButton: `heroStandardCTAButton`
}

export const HeroStandardCTA = ({
    content
}: HeroStandardCTAProps): JSX.Element => {
    const position =
        content.ctaLocation?.toLocaleLowerCase().replace(' ', '-') || 'top'
    const typeVideo =
        content?.cloudinaryDesktopMedia?.[0]?.resource_type === 'video'
    const typeImage =
        content?.cloudinaryDesktopMedia?.[0]?.resource_type === 'image'

    const showRightArrow = content?.ctaButton?.showRightArrow

    const getButtonTypeClass = (type?: btnType) => {
        if (!type) {
            return ''
        }

        switch (type) {
            case 'primary':
                return 'scuf-button-primary'
            case 'secondary':
                return 'scuf-button-secondary'
            default:
                return ''
        }
    }

    const getRightIcon = (isShowRightArrow = false) => {
        if (!isShowRightArrow) {
            return <div />
        }
        return (
            <div className={cn(s['suffix-icon'])}>
                <ArrowRightIcon />
            </div>
        )
    }

    return (
        <div
            className={cn(
                s[`hero-standard-cta`],
                s[`hero-standard-cta-${position}`]
            )}
        >
            {content?.ctaButton && (
                <CTA
                    cta={content.ctaButton}
                    className={cn(
                        c.heroStandardCTAButton,
                        s[`hero-standard-cta-button-image-${position}`],
                        getButtonTypeClass(content.ctaButton.buttonType),
                        showRightArrow && 'scuf-show-right-arrow'
                    )}
                    containerClassname={s['hero-standard-cta-container']}
                >
                    {getRightIcon(showRightArrow)}
                </CTA>
            )}

            {content?.ctaButtons && content?.ctaButtons.length > 0 && (
                <div className="flex flex-row justify-center gap-4">
                    {content.ctaButtons.map((btn, index) => {
                        return (
                            <CTA
                                cta={btn}
                                key={index}
                                className={cn(
                                    c.heroStandardCTAButton,
                                    s['heroStandardCTAButton'],
                                    s[
                                        `hero-standard-cta-button-image-${position}`
                                    ],
                                    getButtonTypeClass(btn.buttonType),
                                    btn.showRightArrow &&
                                        'scuf-show-right-arrow'
                                )}
                                containerClassname={
                                    s['hero-standard-cta-container']
                                }
                            >
                                {getRightIcon(btn.showRightArrow)}
                            </CTA>
                        )
                    })}
                </div>
            )}
        </div>
    )
}
