import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface TechSpecsAndSupportResponse {
    title: string
    techSpecsIdentifier: string
    techSpecsGroupColumn1: GroupColumn[]
    techSpecsGroupColumn2: GroupColumn[]
    techSpecsGroupColumn3: GroupColumn[]
    techSpecsImageSlider: ImageSlider
    supportIdentifier: string
    supportGroupColumn: GroupColumn[]
}

export interface GroupColumn {
    title: string
    heading: string
    features: FeatureProps[]
    featuresGap?: number
}

export interface ImageSlider {
    heading: string
    slides: ImageSlide[]
}

export interface ImageSlide {
    image: CloudinaryImage[]
    description: string
}

export interface TechSpecsAndSupportProps {
    content: TechSpecsAndSupportResponse
}

export interface CollapseProps {
    id: string
    heading: string
    children?: React.ReactNode
}

export interface GroupProps {
    heading: string
    gap?: number
    className?: string
    children?: React.ReactNode
}

export type FeatureType = 'list-item' | 'highlight-1' | 'highlight-2' | 'url'

export interface FeatureProps {
    title: string
    headline?: string
    label?: string
    description?: string
    type?: FeatureType
    content: string
}
