.brand-wrapper {
    background-color: var(--background-color);
    padding: 40px 0px;
    h2 {
        @apply font-tomorrow uppercase;
        letter-spacing: 0;
        font-size: 32px;
        font-weight: 500;
        line-height: 34px;
        color: var(--title-color);
        margin-bottom: 40px;
    }

    .brand-items {
        @apply flex md-max:flex-col md-max:items-center;
        .brand-item {
            @apply relative;
            width: 100px;
            height: 600px;
            transition: transform 0.3s ease;
            &:hover {
                transform: scale(1.02);
                cursor: pointer;
            }

            img {
                width: 100%;
                height: 100%;
            }
            @screen md-max {
                width: 100%;
                height: auto;
                max-width: 640px;

                img {
                    max-height: 100px;
                }
            }
        }
    }
}
