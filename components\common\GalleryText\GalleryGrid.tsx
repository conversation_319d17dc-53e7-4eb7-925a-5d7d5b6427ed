import { GalleryGridProps } from './GalleryText.types'
import cn from 'classnames'
import s from './GalleryText.module.scss'
import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import CarouselGallery from './CarouselGallery'

const GalleryGrid: React.FC<GalleryGridProps> = ({
    galleryImages,
    identifier
}) => {
    return (
        <div className="h-full">
            <div
                className={cn(
                    `h-full grid grid-cols-2 grid-rows-2`,
                    s['gallery-grid']
                )}
            >
                {galleryImages &&
                    galleryImages.slice(0, 4).map((item, index) => {
                        return (
                            <div key={index} className={s['gallery-grid-item']}>
                                <CorsairImage
                                    keepOrigin
                                    alt="demo"
                                    src={convertUrlFormat(item.secure_url)}
                                    objectFit="cover"
                                    layout="fill"
                                />
                            </div>
                        )
                    })}
            </div>

            <div className="md:hidden md-max:block">
                <CarouselGallery
                    galleryImages={galleryImages}
                    identifier={identifier}
                />
            </div>
        </div>
    )
}

export default GalleryGrid
