.root {
    @screen md {
        top: 0;
    }
}

.layered-navigations {
    z-index: 1000000;
    &.open {
        @apply block opacity-100;
    }
    @screen md {
        @apply w-full;
        z-index: 0;
        top: 0 !important;
    }
}

.filter-mob {
    font-size: 20px;
    background-color: #1c1c1c;
    border-color: #4d4d4d;
}
.filter-body {
    @apply overflow-hidden;
    > ul {
        @apply flex flex-col;

        & > li {
            @apply p-0;
            @screen md-max {
                padding: 0px 20px 0px 20px;
            }
        }
    }

    h2 {
        @apply text-white uppercase text-sm font-normal;
    }
}
.filters-list {
    background-color: #1c1c1c;
}

.linked-pages-container {
    @apply md-max:hidden mb-6 mr-0 lg:mr-6 xl:mr-10;
}

.filter-wrapper {
    @apply p-0 hidden items-center md:bg-black  mx-5 md:mx-0;
    border-color: #3f3f3f;
    height: 3rem;
    background-color: #262626;
    position: relative;

    @screen md {
        @apply flex;
    }

    @screen xl {
        margin-right: 25px;
    }
}

.selected-items-list {
    @screen md-max {
        margin-left: 1rem;
    }
    @apply flex items-center h-full;
}

.selected-items-clear-all,
.selected-items-clear-all:hover {
    color: #e9e600 !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    @apply font-sairaCopy font-semibold uppercase;

    &:before {
        content: '';
        @apply absolute top-0 bottom-0 left-0 right-0;
    }
}

.filter-close-btn {
    margin-right: 0.5rem;
}
