import s from './ProductFamily.module.scss'
import Slider from 'react-slick'
import ProductFamilyBlock from './ProductFamilyBlock'
import cn from 'classnames'
import { useState } from 'react'
import { Arrows } from '../Arrows'
import { ProductFamilyResponse, ProductFamilyBlockType } from '../types'
import { useTranslation } from 'next-i18next'

const c = /*tw*/ {
    blockContainer: s['block-container'],
    hasAnimation: [s['extra-info-container'], s['has-animate']],
    onScreen: s['onScreen'],
    slider: s['slider'],
    sliderContainer: s['slider-container'],
    container: s['container'],
    headerWrapper: s['extra-info-wrapper'],
    textContainer: s['text-container'],
    heading: s['heading'],
    subHeading: s['sub-heading'],
    headingText: s['heading-text']
}

export type ProductFamilyProps = {
    content: ProductFamilyResponse | null
}

const ProductFamily = ({ content }: ProductFamilyProps): JSX.Element | null => {
    const [currentSlider, setCurrentSlider] = useState<number>(1)
    const { t } = useTranslation(['common'])

    if (!content) {
        return null
    }

    const {
        heading,
        backgroundColor,
        productBlocks,
        subheading,
        headingsPosition,
        headingType
    } = content

    const defaultSettings = {
        afterChange: (current: number) => {
            setCurrentSlider(current + 1)
        },
        arrows: true,
        autoplay: false,
        classNames: 'center',
        draggable: true,
        infinite: true,
        nextArrow: <Arrows direction="right" s={s} />,
        prevArrow: <Arrows direction="left" s={s} />,
        responsive: [
            {
                breakpoint: 1050,
                settings: {
                    slidesToShow: 1
                }
            },
            {
                breakpoint: 1280,
                settings: {
                    slidesToShow:
                        productBlocks.length >= 3 ? 2.5 : productBlocks.length
                }
            },
            {
                breakpoint: 1440,
                settings: {
                    slidesToShow:
                        productBlocks.length >= 4 ? 3.5 : productBlocks.length
                }
            },
            {
                breakpoint: 1670,
                settings: {
                    slidesToShow:
                        productBlocks.length >= 4 ? 4 : productBlocks.length
                }
            }
        ],
        slidesToScroll: 1,
        slidesToShow: productBlocks.length >= 5 ? 4.5 : productBlocks.length
    }

    const SliderDots = () => {
        return (
            <div className="slick-dots">
                <p className={s['dot-counter']}>{currentSlider}</p>
                <ul
                    className={cn(
                        s['product-carousel-custom-dots'],
                        'w-full flex items-center justify-center'
                    )}
                >
                    {productBlocks?.map(
                        (
                            productBlock: ProductFamilyBlockType,
                            index: number
                        ) => (
                            <li
                                key={productBlock.sku}
                                className={
                                    index + 1 === currentSlider
                                        ? 'slick-active'
                                        : ''
                                }
                            >
                                <button aria-label={t('select')} />
                            </li>
                        )
                    )}
                </ul>
                <p className={s['dot-counter']}>{productBlocks.length}</p>
            </div>
        )
    }

    return (
        <div
            className={`${cn(c.container)}`}
            style={{ backgroundColor: backgroundColor }}
        >
            <div className={s['header']}>
                {subheading && (
                    <p
                        className={`${s['subheading']} ${
                            headingsPosition ? 'text-left' : 'text-center'
                        }`}
                    >
                        {subheading}
                    </p>
                )}
                {headingType === 'H1' ? (
                    <h1
                        className={`w-full mx-auto text-white text-6xl font-semibold uppercase ${
                            headingsPosition ? 'text-left' : 'text-center'
                        }`}
                    >
                        {heading}
                    </h1>
                ) : (
                    <h2
                        className={`w-full mx-auto text-white text-6xl font-semibold uppercase ${
                            headingsPosition ? 'text-left' : 'text-center'
                        }`}
                    >
                        {heading}
                    </h2>
                )}
            </div>
            <div
                className={`mt-32 ${cn(s['productfamily-container'])}`}
                style={{ backgroundColor: backgroundColor }}
            >
                <div className={c.sliderContainer}>
                    <Slider {...defaultSettings} className={c.slider}>
                        {productBlocks?.map(
                            (
                                productBlock: ProductFamilyBlockType,
                                index: number
                            ) => (
                                <ProductFamilyBlock
                                    key={index}
                                    productBlock={productBlock}
                                />
                            )
                        )}
                    </Slider>
                    <SliderDots />
                </div>
            </div>
        </div>
    )
}

export default ProductFamily
