.header-text-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.product-tiles-top-container {
    @apply flex items-center justify-between;
    padding-inline: 20px;
    margin-bottom: 28px;

    @screen md {
        margin-bottom: 65px;
        padding-inline: 80px;
    }

    .header {
        text-align: left;
        text-wrap: balance;
        font-size: 32px;
        line-height: 38.4px;

        @screen md {
            font-size: 54px;
            line-height: 65px;
        }

        @apply font-medium text-white;
    }
}

.pagination-container {
    &.pagination-container {
        @screen md-max {
            @apply flex items-center justify-around;
            margin-top: 40px;
        }
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--primary);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }
}

.product-tiles-swiper-container {
    // .faded-right,
    // .faded-left {
    //     @apply h-full absolute top-0 block z-2;
    //     background: #0f0f0f;
    //     opacity: 0.4;
    //     width: 4.8%;

    //     @screen md-max {
    //         width: 15%;
    //     }
    // }

    // .faded-right {
    //     right: -1px;
    //     background: #0f0f0f;
    // }
    .faded-left {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to left, var(--gradient-stops));
        transition: opacity 0.5s ease-in-out;
        @apply absolute top-0 left-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .faded-right {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to right, var(--gradient-stops));
        transition: opacity 0.5s ease-in-out;
        @apply absolute top-0 right-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    // &.last-item-reached {
    //     margin-right: 20px;
    //     transition: left 0.35s ease-in-out;
    //     @screen md {
    //         margin-right: 80px;
    //     }
    // }

    // &.first-item-reached {
    //     margin-left: 20px;
    //     transition: left 0.35s ease-in-out;
    //     @screen md {
    //         margin-left: 80px;
    //     }
    // }

    .prev:disabled,
    .prev:disabled ~ .faded-left {
        @apply opacity-0;
    }
    .prev:disabled ~ .swiper-container {
        // margin-left: 20px;
        transform: translateX(20px);
        @screen md {
            // margin-left: 80px;
            transform: translateX(80px);
        }
    }
    .next:disabled,
    .next:disabled ~ .faded-right {
        @apply opacity-0;
    }
    .next:disabled ~ .swiper-container {
        // margin-right: 20px;
        transform: translateX(-20px);
        @screen md {
            // margin-right: 80px;
            transform: translateX(-80px);
        }
    }
    .prev:disabled + .next:disabled {
        & ~ .swiper-container {
            transform: translateX(0px) !important;
            margin-inline: 20px;
            @screen md {
                margin-inline: 80px;
            }
        }
    }

    @screen md-max {

        .faded-left,
        .faded-right {
            display: none;
        }
    }

    .slider-item-container {
        @apply flex flex-col;

        @screen xl-1280 {
            max-width: 428px;
            height: 429px;
        }

        @media screen and (max-width: 1500px) {
            height: 320px;
        }

        @screen md-max {
            height: 306px;
        }

        .slider-item-text-block {
            @apply flex flex-col;
            row-gap: 12px;
            padding: 1.5rem 2.25rem;

            @media screen and (min-width: 1100px) and (max-width: 1500px) {
                padding: 20px 18px;
            }

            @media screen and (min-width: 800px) and (max-width: 1100px) {
                padding: 16px;
            }

            .slider-item-heading {
                @apply font-primary font-medium;
                font-size: 1.75rem;
                line-height: 2rem;

                @media screen and (max-width: 1860px) {
                    font-size: 1.5rem;
                    line-height: 1.75rem;
                }

                @media screen and (max-width: 1500px) {
                    font-size: 1.25rem;
                    line-height: 1.5rem;
                }

                @media screen and (min-width: 1100px) and (max-width: 1500px) {
                    font-size: 1.125rem;
                    line-height: 1.375rem;
                }

                @screen md-max {
                    font-size: 22px;
                    line-height: 100%;
                    letter-spacing: 0%;
                }
            }

            .slider-item-display-text {
                font-size: 1rem;
                line-height: 20.26px;
                @apply font-bold;
            }

            .slider-item-cta {
                display: flex !important;
                align-items: center !important;

                .slider-icon {
                    margin-left: 8px;
                }
            }

            .slider-item-cta-text {
                @apply font-primary font-medium;
                font-size: 1rem;
                line-height: 20.26px;
            }
        }

        .slider-item-image-container {
            flex: 1 1;
        }
    }

    @apply relative;

    .swiper-container {
        transition: transform 0.1s linear;

        @screen md {
            transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        }
        // &.last-item-reached {
        //     margin-right: 20px;
        //     transition: left 0.35s ease-in-out;
        //     @screen md {
        //         margin-right: 80px;
        //     }
        // }

        // &.first-item-reached {
        //     margin-left: 20px;
        //     transition: left 0.35s ease-in-out;
        //     @screen md {
        //         margin-left: 80px;
        //     }
        // }
    }

    .carousel-nav-button {
        transform: translateY(-50%);
        width: 52px;
        height: 48px;
        @apply z-10 top-1/2 absolute flex items-center justify-center overflow-hidden;

        :global(#Oval-2) {
            stroke: transparent;
        }

        div {
            button {
                &:focus {
                    outline: none;
                }
            }
        }

        &.prev {
            left: 3%;

            @screen md-max {
                @apply hidden;
            }

            svg {
                transform: rotateZ(180deg);
            }
        }

        &.next {
            right: 3%;

            @screen md-max {
                @apply hidden;
            }
        }
    }
}

// Variant double image
.double-image {
    .product-tiles-top-container {
        max-width: 1920px;
        @apply mx-auto;

        @screen md-max {
            margin: 0;
            max-width: 100%;
            padding-top: 32px;
            padding-bottom: 28px;
            background-color: #333132;
        }
    }

    .fade-right,
    .fade-left {
        @screen md-max {
            display: none !important;
        }
    }

    .swiper-container {
        @screen md-max {
            margin: 0 !important;
        }
    }

    .product-tiles-swiper-container {
        max-width: 1920px;
        @apply mx-auto;

        @screen md-max {
            margin: 0;
            max-width: 100%;
        }
    }

    .pagination-container-mobile {
        @screen md-max {
            margin-top: 0;
            padding-bottom: 40px;
            background: var(--tertiary-light-gray) !important;
        }

        .custom-dot-container {
            background-color: #D9D9DB;
        }

        .active-animated-dot {
            background-color: #333132;
        }
    }
}