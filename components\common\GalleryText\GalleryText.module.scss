.gallery-layout {
    @screen xl-2000 {
        @apply mx-auto;
        max-width: 1920px;
    }

    @screen md {
        padding: 40px 0px;
        flex-direction: var(--block-layout);
    }

    @screen md-max {
        padding: 40px 20px;
    }

    // &.md-max-padding-right-0 {
    //     @apply md-max:pr-0;
    // }
}

.gallery-container {
    @apply md:flex md-max:h-auto md:justify-between;

    @screen md {
        padding: 0px 80px;
        flex-direction: var(--block-layout);
    }

    .heading {
        @apply font-tomorrow font-medium uppercase;
        font-size: 2rem;
        line-height: 120%;
    }

    .heading-mobile {
        @apply md:hidden;
    }

    .gallery-column {
        @apply w-full flex flex-col justify-center relative;

        @screen md {
            width: 46.6%;
        }

        .gallery-grid-item {
            @apply w-full h-full relative;
        }

        @screen md-max {
            .gallery-grid-item {
                height: 360px;
            }
        }

        &.text-only {
            align-items: start;

            @screen md {
                padding-inline: 20px;
            }

            .heading-desktop {
                @apply md-max:hidden;
            }

            .general-information {
                @apply mt-6;
                height: fit-content;

                .general-information-container {
                    row-gap: 2rem;

                    .general-information-item {
                        h5 {
                            @apply font-semibold font-sofiaSans #{!important};
                            font-size: 20px;

                            @screen md-max {
                                font-size: 16px;
                            }
                        }

                        p {
                            @apply font-normal font-sofiaSans #{!important};
                            font-size: 20px;

                            @screen md-max {
                                font-size: 16px;
                            }
                        }
                    }
                }
            }

            .description {
                @apply text-base md:text-xl font-normal font-sofiaSans mt-12 #{!important};
                line-height: 1.4;
            }

            .follow-section {
                h4.label {
                    @apply font-sofiaSans font-normal mt-10 lg-max:mt-8;
                    font-size: 16px;
                }

                .social-media {
                    @apply flex relative gap-4 mt-2;

                    .social-media-item {
                        height: 25px;
                    }
                }
            }

            .button {
                @apply mt-4;

                a {
                    @apply font-tomorrow font-medium;
                    font-size: 16px;
                }
            }
        }

        &.image-only {
            @media (min-width: 768px) {
                aspect-ratio: 1 / 1;
                max-height: fit-content;
            }

            .gallery-grid {
                @apply md-max:hidden;
                gap: 8px;
            }

            div.flex {
                gap: 5px;
            }
        }
    }
}

.carousel-nav-button {
    --btn-width: 52px;
    --btn-height: 48px;

    @screen md-max-768 {
        --btn-width: 44px;
        --btn-height: 40px;
    }

    transform: translateY(-50%);
    width: var(--btn-width);
    height: var(--btn-height);

    &.prev {
        left: calc(var(--btn-width) / -2);
    }

    &.next {
        right: calc(var(--btn-width) / -2);
    }

    @apply z-10 top-1/2 absolute flex items-center justify-center overflow-hidden;

    :global(#Oval-2) {
        stroke: transparent;
    }

    div {
        button {
            &:focus {
                outline: none;
            }
        }
    }

    &.prev,
    &.next {
        @screen md-max {
            @apply hidden;
        }
    }

    &.prev {
        svg {
            transform: rotateZ(180deg);
        }
    }
}

.slider-container {
    width: 100%;
    height: 100%;

    // @screen md-max {
    //     height: 500px;
    // }

    // Pagination dots
    .pagination-container {
        @apply flex items-center justify-around;
        padding-block: 32px;

        &.hide-pagination {
            @apply lg:hidden;
        }

        :global {
            .dots-line {
                position: absolute;
                top: 50%;
                width: calc(100% + 42px);
                left: -21px;
                height: 1px;
                z-index: 0;
                background-color: var(--white);
                transform: translateY(-50%);
            }

            .active-animated-dot {
                z-index: 2;
                position: absolute;
                top: 50%;
                width: 14px;
                height: 14px;
                background-color: var(--secondary-off-black);
                border: 1px solid var(--white);
                left: 0px;
                border-radius: 50%;
                transform: translateY(-50%);
            }

            .custom-dot-container {
                @apply relative;
                margin: 0 !important;
                z-index: 1;
                width: 11px;
                height: 11px;
                background-color: var(--steel-gray20);
                border: 1px solid var(--white);
                border-radius: 50%;
                opacity: 1;
            }

            .swiper-pagination {
                display: flex;
                justify-content: center;
                padding: 0;
                list-style: none;
                bottom: -35px;
            }

            .swiper-pagination .swiper-pagination-bullet {
                margin: 0 3px;
            }
        }

        .default-dot {
            background-color: var(--dark-bg-btn-primary);
        }
    }
}

.slider-container {
    .faded-left {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to left, var(--gradient-stops));
        @apply absolute top-0 left-0 h-full w-1/12 z-2;
    }

    .faded-right {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to right, var(--gradient-stops));
        @apply absolute top-0 right-0 h-full w-1/12 z-2;
    }

    .faded-left {
        @apply md-max:hidden;

        &.is-last-item {
            @apply md-max:block;
        }
    }
}