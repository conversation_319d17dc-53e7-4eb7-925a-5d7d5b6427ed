import { IHTMLContentPage } from '../types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { btnType } from '@pylot-data/hooks/contentful/use-content-json'

// Type definitions
interface CTAClassNames {
    root?: string
    text?: string
    icon?: string
    wrapper?: string
}

interface CommonButtonContentProps {
    displayText: string
    textColor?: string
    children?: React.ReactNode
    iconPosition?: boolean
    className?: any
}

interface CommonButtonProps extends CommonButtonContentProps {
    buttonType?: btnType
    className?: CTAClassNames
    style?: React.CSSProperties
    onClick?: () => void
    'aria-label'?: string
    disabled?: boolean
    loading?: boolean
    showRightArrow?: boolean
    icon?: CloudinaryImage[]
    iconPosition?: boolean
}

interface LinkButtonProps extends CommonButtonProps {
    url: string
    openInANewTab?: boolean
    icon?: CloudinaryImage[]
    showRightArrow?: boolean
    iconPosition?: boolean
}

interface ModalButtonProps extends CommonButtonProps {
    popupVideoUrl?: string
    videoTranscript?: string
    popupHtmlContent?: IHTMLContentPage
    showRightArrow?: boolean
    className?: any
}

export type {
    CTAClassNames,
    CommonButtonContentProps,
    CommonButtonProps,
    LinkButtonProps,
    ModalButtonProps
}
