.carousel-wrapper {
    @screen md {
        @apply pt-16 pb-8;
    }

    @apply bg-secondaryOffBlack pt-8 pb-4;
}

.image-text-swiper-container {
    @apply relative;
}

.slider-item-container {
    height: 512px;
    @apply flex items-center bg-black flex-row;
    @screen md-max {
        height: 612px;
        @apply flex-col-reverse;
    }

    .slider-item-text-block {
        @apply flex-1 h-full flex flex-col justify-center items-center;
        @screen md-max {
            @apply items-start justify-start;
        }

        .inner-text-wrapper {
            padding: 30px 24px;

            @screen md {
                max-width: 361px;
            }
        }
    }

    .slider-item-image-container {
        @apply relative h-full;
        @screen md {
            @apply flex-1;
        }
        @screen md-max {
            @apply h-1/2 w-full;
        }
    }

    .slider-item-heading {
        font-size: 1.5rem;
        line-height: 120%;
        letter-spacing: 0px;
        @apply font-primary font-medium align-middle;
    }

    .slider-item-quote {
        font-size: 1.25rem;
        line-height: 150%;
        margin-bottom: 24px;
        @apply font-sofiaSans font-light #{!important};
        @apply text-white align-middle;
    }
}

.swiper-container {
    :global {
        .swiper-slide-shadow-right {
            background-image: linear-gradient(
                90deg,
                rgba(0, 0, 0, 0.2) 0%,
                rgba(0, 0, 0, 0.8) 15%,
                rgba(0, 0, 0, 1) 100%
            );
        }

        .swiper-slide-shadow-left {
            background-image: linear-gradient(
                270deg,
                rgba(0, 0, 0, 0.2) 0%,
                rgba(0, 0, 0, 0.8) 15%,
                rgba(0, 0, 0, 1) 100%
            );
        }
    }
}

.carousel-nav-button {
    transform: translateY(-50%);
    width: 52px;
    height: 48px;
    border: none;
    outline: none;
    @apply z-10 top-1/2 absolute bg-primary flex items-center justify-center overflow-hidden;

    :global(#Oval-2) {
        stroke: transparent;
    }

    div {
        button {
            &:focus {
                outline: none;
            }
        }
    }

    &.prev,
    &.next {
        @screen md-max {
            @apply hidden;
        }
    }

    &.prev {
        left: 3%;
        svg {
            transform: rotateZ(180deg);
        }
    }

    &.next {
        right: 3%;
    }
}

.pagination-container {
    &.pagination-container {
        margin-top: 45px;
        @apply flex items-center justify-around;
    }

    @screen md-max {
        &.pagination-container {
            margin-top: 40px;
        }
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--primary);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }
}
