.ExtraInfoBlockWrapper {
    gap: 16px;
    @apply flex flex-col;

    & > h5 {
        font-size: 24px;
        line-height: 28.8px;
        letter-spacing: 0px;
        @apply italic font-tomorrow font-semibold text-white uppercase;

        @screen md-max {
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0px;
        }
    }

    .ImageWrapper {
        height: 200px;
        @apply relative w-full;

        @screen md-max {
            height: 150px;
        }
    }

    & > p {
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
        text-transform: none;
        @apply font-normal text-white;
    }
}

.FilterExtraInfoWrapper {
    z-index: -1;
    top: 0;
    left: 0;
    @apply h-full absolute;
    width: 0;
    overflow: hidden;
    transition: width 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    transform-origin: left center;

    @screen md-max {
        left: -65px;
        padding-top: 24px;
        position: unset;
        @apply h-screen;
    }

    .ContentWrapper {
        padding-inline: 32px;
        gap: 32px;
        background-color: #1f1f1f;
        box-shadow: -2px 2px 8px 0px #0000000f;
        @apply w-full h-full flex flex-col;
        width: 100%;

        & * {
            opacity: 0;
            transition: opacity 0.25s ease;
        }

        @screen md-max {
            gap: 24px;
            padding-top: 16px;
        }

        .Header {
            .CloseWrapper {
                padding: 46px 14px 30px 14px;
                @apply flex justify-end;

                @screen md-max {
                    padding: 30px 14px;
                }
            }

            & > h4 {
                font-size: 32px;
                line-height: 38.4px;
                letter-spacing: 0px;
                padding-bottom: 32px;
                border-bottom: 1px solid #414141;
                @apply font-tomorrow font-semibold italic text-white uppercase;
            }
        }

        .Body {
            gap: 32px;
            overflow: hidden;
            @apply flex flex-1 flex-col overflow-y-auto;
        }
    }

    .PaddingNone {
        padding: 0;
    }
}

.ShowExtraInfo {
    @apply z-50 absolute block;
    width: 100%;

    .ContentWrapper * {
        opacity: 1;
        transition-delay: 0.2s;
    }

    @screen md-max {
        background-color: #000000cc;
        @apply m-0 fixed left-0;
    }
}

.FilterExtraInfoWrapper:not(.ShowExtraInfo) {
    transition:
        width 0.4s cubic-bezier(0.25, 1, 0.5, 1),
        z-index 0s linear 0.4s;
    .ContentWrapper * {
        opacity: 0;
        transition-delay: 0s;
    }
}
