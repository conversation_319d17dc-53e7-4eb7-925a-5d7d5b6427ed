import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC, useMemo } from 'react'
import CorsairImage from '@corsairitshopify/corsair-image'

import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'

import { ImageTextSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselImageText.module.scss'

type CarouselImageTextItemProps = {
    product: ImageTextSlideType
}

const CarouselImageTextItem: FC<CarouselImageTextItemProps> = ({ product }) => {
    const { t } = useTranslation(['common'])
    const isMobile = useMediaQuery('(max-width: 767px)')

    const imgSrc = useMemo(
        () => convertUrlFormat(product.cloudinaryMainImage?.[0]?.secure_url),
        [product.cloudinaryMainImage]
    )

    const style = {
        background: product?.backgroundColor
            ? product.backgroundColor
            : isMobile && product?.backgroundImageMobile
            ? `url(${product?.backgroundImageMobile?.[0]?.secure_url})` ||
              `url(${product?.backgroundImageDesktop?.[0]?.secure_url})`
            : `url(${product?.backgroundImageDesktop?.[0]?.secure_url})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
    }

    return (
        <div className={s['slider-item-container']} style={style}>
            <div className={s['slider-item-text-block']}>
                <div className={s['inner-text-wrapper']}>
                    {product?.quote && (
                        <div
                            className={s['slider-item-quote']}
                            style={{ color: product.copyColor }}
                            dangerouslySetInnerHTML={{
                                __html: product?.quote
                            }}
                        />
                    )}

                    <div
                        className={s['slider-item-heading']}
                        style={{ color: product.headingColor }}
                    >
                        {product.heading}
                    </div>
                </div>
            </div>
            <div className={cn(s['slider-item-image-container'])}>
                <CorsairImage
                    keepOrigin
                    src={imgSrc}
                    alt={t(
                        `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                    )}
                    objectFit="cover"
                    layout="fill"
                />
            </div>
        </div>
    )
}

export default CarouselImageTextItem
