/* eslint-disable @typescript-eslint/no-empty-function */
import React, { createContext, FC, useContext, useMemo } from 'react'

export interface DispatchActions {
    openForm: (urlDriver: string, softwareVersion: string) => void
    closeForm: () => void
}
type Action =
    | {
          type: 'OPEN_FORM'
          urlDriver: string
          softwareVersion: string
      }
    | {
          type: 'CLOSE_FORM'
      }

export interface State {
    displayForm: boolean
    urlDriver: string
    softwareVersion: string
}

const initialState = {
    displayForm: false,
    urlDriver: '',
    softwareVersion: ''
}

export const DownloadDriverNewsletterContext = createContext<
    State & DispatchActions
>({
    ...initialState,
    openForm: () => {},
    closeForm: () => {}
})
DownloadDriverNewsletterContext.displayName = 'DownloadDriverNewsletterContext'

function downloadDriverNewsletterReducer(state: State, action: Action) {
    switch (action.type) {
        case 'OPEN_FORM': {
            return {
                ...state,
                displayForm: true,
                urlDriver: action.urlDriver,
                softwareVersion: action.softwareVersion
            }
        }
        case 'CLOSE_FORM': {
            return {
                ...state,
                displayForm: false
            }
        }
    }
}

export const DownloadNewsletterProvider: FC = (props) => {
    const [state, dispatch] = React.useReducer(
        downloadDriverNewsletterReducer,
        initialState
    )

    const openForm = (urlDriver: string, softwareVersion: string) =>
        dispatch({ type: 'OPEN_FORM', urlDriver, softwareVersion })
    const closeForm = () => dispatch({ type: 'CLOSE_FORM' })

    const value = useMemo(
        () => ({
            ...state,
            openForm,
            closeForm
        }),
        [state]
    )

    return <DownloadDriverNewsletterContext.Provider value={value} {...props} />
}

export const useDownloadDriverNewsletter = () =>
    useContext(DownloadDriverNewsletterContext)

export const ManagedDownloadDriverNewsletterContext: FC = ({ children }) => (
    <DownloadNewsletterProvider>{children}</DownloadNewsletterProvider>
)
