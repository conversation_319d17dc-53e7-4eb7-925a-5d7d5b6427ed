import { graphqlFetch, GraphqlFetchOutput } from '@pylot-data/graphqlFetch'
import { useRouter } from 'next/router'
import { useState } from 'react'
import { requestPasswordResetMutation } from './graphql/requestPasswordResetMutation'

export type RequestPasswordResetInput = {
    email: string
}

export type RequestPasswordResetOutput = {
    requestPasswordResetEmail: boolean
}

type RequestPasswordResetResult = { result?: boolean; error?: string }

export interface UseRequestPasswordResetReturn {
    requestPasswordReset: (email: string) => Promise<RequestPasswordResetResult>
    isLoading: boolean
}

const formatData = (
    output: GraphqlFetchOutput<RequestPasswordResetOutput>
): RequestPasswordResetResult => {
    const result = output.data?.requestPasswordResetEmail
    if (result) return { result }

    return {
        error: output.errors?.[0]?.message
    }
}

export const useRequestPasswordReset = (): UseRequestPasswordResetReturn => {
    const { locale } = useRouter()
    const [isLoading, setIsLoading] = useState(false)
    const requestPasswordReset = async (email: string) => {
        setIsLoading(true)

        const response = await graphqlFetch<
            RequestPasswordResetInput,
            RequestPasswordResetOutput
        >({
            query: requestPasswordResetMutation,
            variables: {
                email
            },
            locale
        })

        const result = formatData(response)
        setIsLoading(false)
        return result
    }

    return {
        isLoading,
        requestPasswordReset
    }
}
