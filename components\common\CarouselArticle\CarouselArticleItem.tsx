import cn from 'classnames'
import Link from 'next/link'
import { useTranslation } from 'next-i18next'

import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'

import { ProductsSlideType } from '../CarouselWrapper/CarouselWrapper.types'

import s from './CarouselArticleItem.module.scss'

type CarouselArticleItemProps = {
    product: ProductsSlideType
}

const CarouselArticleItem: React.FC<CarouselArticleItemProps> = ({
    product
}: CarouselArticleItemProps) => {
    const { t } = useTranslation(['common'])
    const { showAnimation } = product
    return (
        <div
            className={cn(s['slider-item-container'], s['article-item'], {
                [s['show-animation']]: showAnimation
            })}
            key={product.identifier}
        >
            <Link href={product.url || ''}>
                <a className={s['article-link']}>
                    <div
                        className={cn(
                            s['slider-item-image-container'],
                            'relative w-full h-full'
                        )}
                    >
                        <CorsairImage
                            keepOrigin
                            src={convertUrlFormat(
                                product.cloudinaryMainImage?.[0]?.secure_url
                            )}
                            alt={t(
                                `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                            )}
                            layout="fill"
                            objectFit="cover"
                        />
                    </div>
                    <div className={cn(s['slider-item-text-block'])}>
                        <div
                            className={cn(s['slider-item-heading'])}
                            style={{
                                color:
                                    product.headingColor ??
                                    'var(--secondary-off-black)'
                            }}
                        >
                            {product.heading}
                        </div>
                    </div>
                </a>
            </Link>
        </div>
    )
}

export default CarouselArticleItem
