import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { CSSProperties, FC } from 'react'
import { ProductsSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import s from '../CarouselProductCategory/CarouselProductCategory.module.scss'
import Link from 'next/link'

type CarouselArticleItemProps = {
    product: ProductsSlideType
}

const CarouselArticleItem: FC<CarouselArticleItemProps> = ({
    product
}: CarouselArticleItemProps) => {
    const { t } = useTranslation(['common'])
    const { showAnimation } = product
    return (
        <div
            className={cn(s['slider-item-container'], s['article-item'], {
                [s['show-animation']]: showAnimation
            })}
            key={product.identifier.toString()}
            style={
                {
                    backgroundColor:
                        product?.backgroundColor ||
                        'var(--secondary-off-black)',
                    '--img-bg-color':
                        product.imageBackgroundColor ||
                        'var(--tertiary-light-gray)'
                } as CSSProperties
            }
        >
            <Link href={product.url || ''}>
                <a className={s['article-link']}>
                    <div
                        className={cn(
                            s['slider-item-image-container'],
                            {
                                [s['lg-img']]: product.horizonPadding == 'none'
                            },
                            'relative w-full h-full'
                        )}
                    >
                        <CorsairImage
                            keepOrigin
                            src={convertUrlFormat(
                                product.cloudinaryMainImage?.[0]?.secure_url
                            )}
                            alt={t(
                                `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                            )}
                            layout="fill"
                            objectFit="cover"
                        />
                    </div>
                    <div
                        className={cn(s['slider-item-text-block'], {
                            [s['noHorizonPadding']]:
                                product.horizonPadding == 'none'
                        })}
                    >
                        <h5
                            className={cn(s['slider-item-heading'])}
                            style={{
                                color: product.headingColor ?? 'var(--white)'
                            }}
                        >
                            {product.heading}
                        </h5>
                    </div>
                </a>
            </Link>
        </div>
    )
}

export default CarouselArticleItem
