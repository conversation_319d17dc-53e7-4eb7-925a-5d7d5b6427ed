.dropdown {
    @apply font-sofiaSans flex flex-row flex-wrap items-center justify-end md:justify-start w-full;
    gap: 8px;
    @media (min-width: 768px) {
        margin-right: 24px;
    }

    .wrapper {
        position: relative;
        @media (min-width: 768px) {
            width: auto;
        }
    }

    // .dropdown_label
    .label {
        @apply font-sofiaSans font-normal;
        color: var(--secondary-off-black);
        font-size: 1rem;
        letter-spacing: normal;
        line-height: 1.5rem;
        // text-transform: uppercase;

        // @screen md-max {
        //     color: black !important;
        //     border-color: black !important;
        // }
    }

    .disable-label {
        pointer-events: none;
        display: list-item;
        color: #ccc;
        text-align: left;
        cursor: default;
        padding: 5px 6px;
        opacity: 0.7;
        font-weight: 600;
    }

    // .dropdown_button
    .button {
        @apply flex items-center md:w-full relative uppercase bg-white mr-0 md:mr-5 font-semibold;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        // color: black !important;
        font-size: 1rem;
        letter-spacing: normal;
        line-height: 1.5rem;
        margin-left: auto;
        max-width: 248px;
        // padding: 0.5rem 0.75rem;
        padding-left: 10px;
        padding-right: 30px;
        height: 36px;
        position: relative;
        text-transform: uppercase;
        width: 100%;

        // @screen md-max {
        //     color: black !important;
        //     border-color: black !important;
        // }

        @screen md {
            font-weight: 600;
        }

        @media (min-width: 768px) {
            max-width: 100%;
        }
        &:focus {
            outline: none;
        }
        &:focus-visible {
            outline: 1px solid #ffffff;
        }
        // .dropdown_button::after
        &::after {
            // @apply top-1/2 -translate-y-1/2 absolute w-2.5 h-2.5 border-white;
            // height: 7px;
            // width: 12px;
            // border-right: 6px solid transparent;
            // border-left: 6px solid transparent;
            // top: 35%;
            // border-top: 7px solid white;
            // content: '';
            // right: 11px;

            // @screen md {
            //     border-top-color: #909090;
            // }
            content: '';
            position: absolute;
            right: 10px;
            top: 42%;
            width: 8px;
            height: 8px;
            border-bottom: 2px solid #454444;
            border-right: 2px solid #454444;
            transform: translateY(-42%) rotate(45deg);
        }

        &.isExposed {
            border-color: var(--secondary-off-black);
        }

        &.isExposed::after {
            transform: rotate(225deg);
            // top: 40%;
        }
    }

    // .dropdown_option
    .option {
        @apply font-sofiaSans font-normal outline-none border-none relative;
        // width: 130px;
        // color: black !important;
        letter-spacing: normal;
        // line-height: 15px;
        // padding: 0.5rem 0.75rem;
        padding-left: 10px;
        padding-right: 30px;
        height: 36px;
        text-transform: uppercase;
        font-size: 1rem;
        width: 100%;

        // .dropdown_option:hover
        &:hover {
            background-color: #909090;
            @apply text-white;
        }
        &-active {
            background-color: #909090;
            @apply text-white;
        }
        &:focus-visible {
            outline: 1px solid #ffffff;
        }
    }

    // .dropdown.menu
    .menu {
        background-color: white;
        // border: 1px solid #e0e0e0;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
        // box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        position: absolute;
        width: 100%;
        right: 0;
        transform: translateY(4px);
        white-space: nowrap;
        z-index: 100;
        font-size: 1rem;
        white-space: nowrap;
        @screen md {
            // border: 1px solid #909090;
        }
        &:focus-visible {
            outline: 1px solid #ffffff;
        }
        li {
            // padding: 2px;
            &:first-of-type {
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;

                .option {
                    border-top-left-radius: 8px;
                    border-top-right-radius: 8px;
                }
            }

            &:last-of-type {
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;

                .option {
                    border-bottom-left-radius: 8px;
                    border-bottom-right-radius: 8px;
                }
            }
        }
    }
}
