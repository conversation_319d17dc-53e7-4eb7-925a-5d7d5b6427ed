import {
    Search<PERSON>riteria,
    SearchCriteriaFilterActions
} from '@corsairitshopify/corsair-filters-and-sort'
import { FilterInputData } from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { ATTR_PRICE_FILTER } from 'hooks/usePlpManager'

type GetApplyCriterionHandlerType = (
    filterInputData?: FilterInputData,
    isApplied?: boolean,
    appliedSearchCritieria?: SearchCriteria[]
) => SearchCriteria[] | null

const buildSearchCriteria = (
    newCriterionData: FilterInputData,
    appliedSearchCritieria: SearchCriteria[]
) => {
    const isPriceRange = newCriterionData.requestVar === ATTR_PRICE_FILTER
    return [
        ...appliedSearchCritieria,
        {
            attribute_code: newCriterionData.requestVar,
            filter_action: isPriceRange
                ? SearchCriteriaFilterActions.TO
                : SearchCriteriaFilterActions.EQ,
            filter_value: newCriterionData.value_string
        }
    ]
}

/**
 * Rebuild a new filter state and trigger product collection reload
 * @param plpManager
 */
export const getApplyCriterionHandler: GetApplyCriterionHandlerType = (
    newCriterionData,
    isShouldApply = true,
    appliedSearchCritieria
) => {
    // Clear all criterion
    if (!newCriterionData || !appliedSearchCritieria) {
        return []
    } else {
        let skipIndex = -1
        const matchCriterion = (
            params: {
                attribute_code: string
                filter_value: string
                filter_action: string
            },
            index: number
        ) => {
            const { attribute_code, filter_value, filter_action } = params
            let filterValue = filter_value

            if (
                filter_action === SearchCriteriaFilterActions.FROM &&
                appliedSearchCritieria[index + 1] &&
                appliedSearchCritieria[index + 1].filter_action ===
                    SearchCriteriaFilterActions.TO
            ) {
                filterValue = `${filter_value}_${
                    appliedSearchCritieria[index + 1].filter_value
                }`
                if (
                    attribute_code === newCriterionData.requestVar &&
                    filterValue === newCriterionData.value_string
                ) {
                    skipIndex = index + 1
                }
            }

            return (
                skipIndex == index ||
                (attribute_code === newCriterionData.requestVar &&
                    filterValue === newCriterionData.value_string)
            )
        }

        const indexIfCriterionExists = appliedSearchCritieria.findIndex(
            matchCriterion
        )

        const isCriterionApplied = indexIfCriterionExists > -1

        // Criterion is already in list so we don't need to reapply it,
        // or it's not in the list so we don't need to remove it.
        // This should never happen if the UI is correctly using search criteria functionality.
        if (
            (isCriterionApplied && isShouldApply) ||
            (!isCriterionApplied && !isShouldApply)
        ) {
            return null
        }
        // Criterion is already in list and we need to remove it
        else if (isCriterionApplied && !isShouldApply) {
            return appliedSearchCritieria.filter(
                (criterion, index) => !matchCriterion(criterion, index)
            )
        }
        // Criterion is not in the list and we need to add it
        else {
            return buildSearchCriteria(
                newCriterionData,
                appliedSearchCritieria ?? []
            )
        }
    }
}
