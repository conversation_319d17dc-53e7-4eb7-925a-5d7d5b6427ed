// Define variables (without using :root selector for CSS Modules compatibility)
$grid-card-padding-mobile: 80px;
$grid-card-padding-desktop: 96px;
$grid-card-max-width: 1920px;
$grid-card-side-padding-mobile: 24px;

$transition-duration: 0.3s;

.image-grid-card {
    padding: $grid-card-padding-mobile 0;
    transition: background-color $transition-duration ease;

    @screen md {
        padding: $grid-card-padding-desktop 0;
    }

    .items {
        max-width: 100%;
        padding: 0 $grid-card-side-padding-mobile;
        margin: 0 auto;

        @screen md {
            max-width: $grid-card-max-width;
            padding: 0 min(80px, 4vw);
        }
    }
}

.card-item {
    transition: transform $transition-duration ease;

    .image-container {
        position: relative;
        height: min(170px, calc(170 / 393 * 100vw));
        width: min(170px, calc(170 / 393 * 100vw));
        overflow: hidden;
        border-radius: 4px;
        transition: transform $transition-duration ease;

        @screen sm {
            height: min(250px, calc(250 / 834 * 100vw));
            width: min(250px, calc(250 / 834 * 100vw));
        }

        @screen lg {
            height: min(429px, calc(429 / 1920 * 100vw));
            width: min(429px, calc(429 / 1920 * 100vw));
        }
    }

    .heading {
        transition: color $transition-duration ease;
        margin-top: 0.5rem;

        @screen md {
            margin-top: 1.75rem;
        }
    }

    .description {
        transition: color $transition-duration ease;
        margin-top: 0.5rem;
        line-height: 1.5;
    }
}