.accordion-info-section {
    @screen md-max {
        max-width: 90% !important
    }
}

.dark-accordion {
    @apply border-0 bg-transparent border-t border-white;

    &:last-child {
        @apply border-b border-white;
    }

    :global {
        .accordion-wrapper {
            @apply px-0;
            background-color: transparent !important;

            .accordion-header {
                @apply m-0;
                background-color: transparent !important;

                .accordion-header-title {
                    @apply text-white text-lg font-normal;
                }

                .accordion-icon {
                    @apply text-white;
                    background-color: transparent !important;
                }
            }

            .accordion-body {
                @apply m-0;
                background-color: transparent !important;

                .accordion-content {
                    background-color: transparent !important;
                    color: #eeeeee !important;
                }
            }
        }
    }

    .icon-accordion {
        height: 100% !important;
    }

    .icon-open {
        transform: rotateZ(45deg);
    }

    .icon-container {
        margin-right: 0;
        border-width: 2px;

        [stroke-width="1.5"] {
            stroke-width: 4px;
        }
    }

    .icon-svg {
        top: 3px;
    }
}