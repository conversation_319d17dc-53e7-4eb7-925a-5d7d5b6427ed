.MosaicGalleryWrapper {
    // Default background color
    background-color: var(--tertiary-rick-black);
    padding-block: 40px 20px;

    // Added for background image and color
    @apply relative;
    background-color: var(--mosaic-bg-color);
    background-image: var(--mosaic-bg-image-desktop);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;

    @screen md-max {
        background-image: var(--mosaic-bg-image-mobile);
    }

    .SectionTitle {
        letter-spacing: 0;
        font-size: 24px;
        line-height: 26px;
        margin-bottom: 20px;
        // Default title color
        color: var(--primary);
        @apply uppercase font-bold font-tomorrow;

        // Added for title color
        color: var(--mosaic-title-color);
    }

    .MosaicCarousel {
        [class*='first-item-reached'] {
            margin: 0 !important;
        }
    }

    .HidePseudo {
        [class*='BaseCarouselWrapper'] {

            &::before,
            &::after {
                display: none;
            }
        }
    }
}

.MosaicGalleryItem {
    height: 710px;
    @apply relative;

    .ImageWrapper {
        @apply relative h-full;
    }

    .Content {
        z-index: 2;
        bottom: 25px;
        left: 20px;
        width: 88%;
        @apply absolute;

        h5 {
            margin-bottom: 10px;
            font-style: normal;
            letter-spacing: 0;
            font-size: 24px;
            line-height: 26px;
            color: var(--white);
            @apply font-tomorrow font-bold uppercase;
        }

        p {
            color: var(--white);
            font-style: normal;
            line-height: 24px;
            font-size: 16px;
            @apply font-sofiaSans font-normal;
        }

        a {
            gap: 5px;
            transition: color 0.3s ease;
            -webkit-transition: color 0.3s ease;
            -moz-transition: color 0.3s ease;
            -ms-transition: color 0.3s ease;
            margin-top: 10px;
            @apply font-bold font-tomorrow uppercase flex items-center;

            span {
                color: var(--primary);
                font-size: 16px;
                line-height: 16px;
            }

            .Icon {
                font-size: 15px;
            }
        }
    }
}