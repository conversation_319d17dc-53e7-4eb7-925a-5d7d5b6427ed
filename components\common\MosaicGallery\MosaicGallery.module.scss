.MosaicGalleryWrapper {
    --mosaic-title-color: var(--primary);
    --mosaic-bg-color: var(--tertiary-rick-black);
    --mosaic-bg-image-desktop: none;
    --mosaic-bg-image-mobile: none;

    padding-block: 4rem;

    // Added for background image and color
    @apply relative;
    background-color: var(--mosaic-bg-color);
    background-image: var(--mosaic-bg-image-desktop);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;

    @screen md-max {
        background-image: var(--mosaic-bg-image-mobile);
    }

    .SectionTitle {
        padding-inline: 20px;
        margin-bottom: 28px;
        line-height: 100%;
        font-size: 2rem;
        color: var(--mosaic-title-color);
        @apply font-medium;
        @screen md {
            margin-bottom: 65px;
            padding-inline: 80px;
            font-size: 2.25rem;
        }
    }

    .MosaicCarousel {
        [class*='first-item-reached'] {
            margin: 0 !important;
        }
    }

    .HidePseudo {
        [class*='BaseCarouselWrapper'] {
            &::before,
            &::after {
                display: none;
            }
        }
    }
}

.MosaicGalleryItem {
    // height: 710px;
    @apply relative;

    .ImageWrapper {
        @apply relative;

        img {
            @apply w-full;
        }
    }

    .Content {
        z-index: 2;
        width: 100%;
        padding: 25px 20px;
        @apply absolute left-0 bottom-0 box-border;

        h5 {
            margin-bottom: 10px;
            font-style: normal;
            letter-spacing: 0;
            font-size: 1.5rem;
            line-height: 1.75rem;
            color: var(--white);
            @apply font-tomorrow font-medium uppercase;
        }

        p {
            color: var(--white);
            font-style: normal;
            font-size: 1rem;
            line-height: 1.5rem;
            white-space: pre-wrap;
            @apply font-sofiaSans font-normal;
        }

        a {
            gap: 5px;
            transition: color 0.3s ease;
            -webkit-transition: color 0.3s ease;
            -moz-transition: color 0.3s ease;
            -ms-transition: color 0.3s ease;
            margin-top: 10px;
            @apply font-medium font-tomorrow uppercase flex items-center;

            span {
                color: var(--primary);
                font-size: 1rem;
                line-height: 1rem;
            }

            .Icon {
                font-size: 15px;
            }
        }
    }
}
