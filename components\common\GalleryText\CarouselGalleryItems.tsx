import CorsairImage from '@corsairitshopify/corsair-image'
import { useMemo } from 'react'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import s from './GalleryText.module.scss'

interface CarouselGalleryItems {
    galleryImages: CloudinaryImage
}

const CarouselGalleryItems: React.FC<CarouselGalleryItems> = ({
    galleryImages
}) => {
    const { t } = useTranslation(['common'])
    const imgSrc = useMemo(
        () =>
            galleryImages.secure_url
                ? convertUrlFormat(galleryImages.secure_url)
                : '',
        [galleryImages]
    )

    const altText = useMemo(() => {
        const customAlt = galleryImages.context?.custom?.alt
        return customAlt ? t(`alt|${customAlt}`) : 'Product image'
    }, [galleryImages, t])

    return (
        <div className={s['gallery-grid-item']}>
            <CorsairImage
                keepOrigin
                alt={altText}
                src={imgSrc}
                objectFit="cover"
                layout="fill"
            />
        </div>
    )
}

export default CarouselGalleryItems
