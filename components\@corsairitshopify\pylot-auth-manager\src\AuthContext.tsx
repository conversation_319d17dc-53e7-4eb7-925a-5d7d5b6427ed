import React, { FC, useMemo } from 'react'

type ModalViewData = {
    email?: string
} & {
    [key: string]: string | number
}

export interface State {
    displayModal: boolean
    modalView: string
    modalViewData: ModalViewData | null
    loginCallbacks: { (): void }[]
    signUpCallbacks: { (): void }[]
}

export interface DispatchActions {
    openAuthModal: () => void
    closeAuthModal: () => void
    setModalView: (view: MODAL_VIEWS, data?: ModalViewData) => void
    onLoginCallback: (callback: { (): void }[]) => void
    onSignUpCallback: (callback: { (): void }[]) => void
    clearAuthCallback: (view: MODAL_VIEWS) => void
}

const initialState = {
    displayModal: false,
    modalView: 'LOGIN_VIEW',
    modalViewData: null,
    loginCallbacks: [],
    signUpCallbacks: []
}

type Action =
    | {
          type: 'OPEN_MODAL'
      }
    | {
          type: 'CLOSE_MODAL'
      }
    | {
          type: 'SET_MODAL_VIEW'
          view: MODAL_VIEWS
          data?: ModalViewData
      }
    | {
          type: 'LOGIN_CALLBACK'
          callbacks: { (): void }[]
      }
    | {
          type: 'SIGNUP_CALLBACK'
          callbacks: { (): void }[]
      }
    | {
          type: 'CLEAR_AUTH_CALLBACK'
          view: MODAL_VIEWS
      }

type MODAL_VIEWS = 'SIGNUP_VIEW' | 'LOGIN_VIEW' | 'FORGOT_VIEW'

export const AuthContext = React.createContext<State | any>(initialState)

AuthContext.displayName = 'AuthContext'

function authReducer(state: State, action: Action) {
    switch (action.type) {
        case 'OPEN_MODAL': {
            return {
                ...state,
                displayModal: true,
                displaySidebar: false
            }
        }
        case 'CLOSE_MODAL': {
            return {
                ...state,
                displayModal: false,
                modalViewData: null,
                loginCallbacks: [],
                signUpCallbacks: []
            }
        }
        case 'SET_MODAL_VIEW': {
            const { data } = action
            return {
                ...state,
                modalView: action.view,
                modalViewData: data ?? null
            }
        }
        case 'LOGIN_CALLBACK': {
            const { callbacks } = action
            return {
                ...state,
                loginCallbacks: [...state.loginCallbacks, ...callbacks]
            }
        }
        case 'SIGNUP_CALLBACK': {
            const { callbacks } = action
            return {
                ...state,
                signUpCallbacks: [...state.signUpCallbacks, ...callbacks]
            }
        }
        case 'CLEAR_AUTH_CALLBACK': {
            return {
                ...state,
                ...(action.view === 'LOGIN_VIEW' && { loginCallbacks: [] }),
                ...(action.view === 'SIGNUP_VIEW' && { signUpCallbacks: [] })
            }
        }
    }
}

export const AuthProvider: FC = (props) => {
    const [state, dispatch] = React.useReducer(authReducer, initialState)

    const openAuthModal = () => dispatch({ type: 'OPEN_MODAL' })
    const closeAuthModal = () => dispatch({ type: 'CLOSE_MODAL' })

    const setModalView = (view: MODAL_VIEWS, data?: ModalViewData) =>
        dispatch({ type: 'SET_MODAL_VIEW', view, data })

    const onLoginCallback = (callbacks: { (): void }[]) =>
        dispatch({ type: 'LOGIN_CALLBACK', callbacks })

    const onSignUpCallback = (callbacks: { (): void }[]) =>
        dispatch({ type: 'SIGNUP_CALLBACK', callbacks })

    const clearAuthCallback = (view: MODAL_VIEWS) =>
        dispatch({ type: 'CLEAR_AUTH_CALLBACK', view })

    const value = useMemo(
        () => ({
            ...state,
            openAuthModal,
            closeAuthModal,
            setModalView,
            onLoginCallback,
            onSignUpCallback,
            clearAuthCallback
        }),
        // eslint-disable-next-line
        [state]
    )

    return <AuthContext.Provider value={value} {...props} />
}

export const useAuthUI = (): State & DispatchActions => {
    const context = React.useContext(AuthContext)
    if (context === undefined) {
        throw new Error(`useAuthUI must be used within a AuthProvider`)
    }
    return context
}

export const ManagedAuthContext: FC = ({ children }) => (
    <AuthProvider>{children}</AuthProvider>
)
