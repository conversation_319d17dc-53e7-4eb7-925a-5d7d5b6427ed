import { useState } from 'react'
import ImageGridDetails from './ImageGridDetails'
import PlayButton from '@components/common/VidGallery/PlayButton'
import { ClickOutside } from '@corsairitshopify/pylot-utils'
import { Modal } from '@corsairitshopify/pylot-ui'
import { PopupVideoPlayer } from '@components/common/Banner23Tile/Banner2Tile/PopupVideoPlayer'
import { GridImage } from '../types'
import { useTranslation } from 'next-i18next'
import c from '../../VidGallery/VidGallery.module.scss'
import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'

type ImageGridCardProps = {
    contents: GridImage
}

const ImageGridCard = ({
    contents
}: ImageGridCardProps): JSX.Element | null => {
    const [isOpen, setIsOpen] = useState(false)
    const { t } = useTranslation(['common'])
    return contents?.cloudinaryDesktopMedia?.[0]?.resource_type.startsWith(
        'video'
    ) ? (
        <>
            <a
                rel="noreferrer"
                href={
                    contents?.cloudinaryDesktopMedia?.[0]?.secure_url
                        ? convertUrlFormat(
                              contents?.cloudinaryDesktopMedia[0].secure_url
                          )
                        : ''
                }
                onClick={(e) => {
                    e.preventDefault()
                    setIsOpen(true)
                }}
            >
                {contents?.cloudinaryThumbnail?.[0]?.secure_url && (
                    <CorsairImage
                        keepOrigin
                        src={convertUrlFormat(
                            contents?.cloudinaryThumbnail[0].secure_url
                        )}
                        alt={correctImageAlt(
                            t(
                                `alt|${contents?.cloudinaryThumbnail?.[0]?.context?.custom?.alt}`
                            )
                        )}
                        layout="fill"
                        objectFit="cover"
                    />
                )}
                <ImageGridDetails content={contents} />
                {contents?.cloudinaryDesktopMedia?.[0]?.resource_type?.startsWith(
                    'video'
                ) &&
                    contents?.cloudinaryDesktopMedia?.[0]?.secure_url && (
                        <PlayButton className="play-button" />
                    )}
            </a>
            <ClickOutside active={isOpen} onClick={() => setIsOpen(false)}>
                <div>
                    <Modal
                        className={c['vidgallery-item-modal']}
                        open={isOpen}
                        onClose={() => setIsOpen(false)}
                        focusFirst={false}
                    >
                        <PopupVideoPlayer
                            videourl={
                                contents?.cloudinaryDesktopMedia?.[0]
                                    ?.secure_url
                                    ? convertUrlFormat(
                                          contents.cloudinaryDesktopMedia[0]
                                              .secure_url
                                      )
                                    : ''
                            }
                        />
                    </Modal>
                </div>
            </ClickOutside>
        </>
    ) : (
        <>
            {contents?.cloudinaryDesktopMedia?.[0]?.resource_type?.startsWith(
                'image'
            ) && (
                <CorsairImage
                    keepOrigin
                    src={
                        contents?.cloudinaryDesktopMedia?.[0]?.secure_url
                            ? convertUrlFormat(
                                  contents?.cloudinaryDesktopMedia?.[0]
                                      ?.secure_url
                              )
                            : ''
                    }
                    alt={correctImageAlt(
                        t(
                            `alt|${contents?.cloudinaryDesktopMedia?.[0]?.context?.custom?.alt}`
                        )
                    )}
                    layout="fill"
                    objectFit="cover"
                />
            )}
            <ImageGridDetails content={contents} />
        </>
    )
}

export default ImageGridCard
