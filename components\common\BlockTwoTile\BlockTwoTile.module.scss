@mixin cta {
    width: 254px;
    height: 48px;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 0.3s;
    padding: 16px 20px 12px 20px;
    letter-spacing: 0.2rem;
    line-height: 1rem;
    font-size: 0.75rem;
    border-width: 1px;
    height: 45px;
    @apply border-white w-full text-white font-medium #{!important};

    &:hover {
        @apply bg-white text-black border-none #{!important};
    }
}

@mixin description {
    font-size: 1rem;
    letter-spacing: inherit;
    line-height: 1.5rem;
}

.info-icons {
    grid-gap: 1rem;
}

.carousel-container {
    overflow-x: hidden;

    :global {
        .slick-track {
            display: flex !important;
        }

        .slick-slide {
            height: inherit !important;

            & > div {
                height: 100%;
            }

            & .BlockTwoTile-container {
                height: 100%;
            }
        }
    }
}

.inner-wrapper {
    @apply flex flex-col justify-start items-start;
    // @apply flex flex-col items-center justify-center;

    // @screen lg {
    //     @apply flex flex-col justify-start items-start;
    // }
}

.visible {
    @apply pt-0 opacity-100;
}

.info-subHeading {
    font-size: 0.875rem;
    letter-spacing: 0.2rem;
    line-height: 1rem;
}

.info-heading {
    letter-spacing: inherit;
    font-size: 2.31rem;
    margin-bottom: 1.25rem;

    sup {
        font-size: 1rem;
        top: -2rem;
    }

    @screen md-max {
        font-size: 1.5rem;
        line-height: 1.625rem;
        padding: 0 54px 0 20px;
        margin-top: 0px;
    }

    @screen lg {
        margin-top: 5px;
    }
}

.info-copy-block {
    font-size: 1rem;
    letter-spacing: inherit;
    line-height: 2.25rem;

    & ul,
    & ol {
        @apply pl-5 text-left ml-3 md:ml-4;
        list-style: unset;
        font-size: inherit;
        line-height: inherit;
    }
}

.info-callouts {
    font-size: 2rem;
    letter-spacing: 0.05rem;
}

.background-image {
    @apply w-full;

    .image-or-video {
        @apply w-9/12 flex flex-col;
    }

    .block-content-top {
        @apply w-full;

        @screen lg {
            @apply w-1/2 flex flex-col;
        }
    }

    .block-content-center {
        @apply w-full;

        @screen lg {
            @apply w-1/2 flex flex-col items-center;
        }
    }
}

.blockTwoTileMedia {
    @apply block opacity-0;
    transition: opacity 0.5s ease-out;

    &.blockOnScreen {
        @apply opacity-100;
        transition: opacity 0.5s ease-in;
    }
}

.sticky-position {
    @apply lg:sticky lg:top-0;
}

.info-section-title {
    font-size: 25px;
    line-height: 2.6rem;
    letter-spacing: 0.19rem;
}

.info-description {
    @apply font-sofiaSans md-max:text-left;
    line-height: 1.5rem;
    font-size: 1rem;
    font-weight: 400;
    white-space: pre-wrap;

    & ul,
    & ol {
        @apply pl-5 text-left ml-3 md:ml-4;
        list-style: unset;
        font-size: inherit;
        line-height: inherit;
    }

    @screen md-max {
        padding: 0px 20px;
    }
}

.info-disclaimer {
    @apply font-sofiaSans;

    font-size: 1rem;
    letter-spacing: inherit;
    line-height: 1.5rem;

    p {
        @apply font-sofiaSans #{!important};
    }

    a,
    a:visited {
        color: var(--primary);
        text-decoration: none;
    }

    @screen md-max {
        padding: 0px 20px;
    }
}

.info-title {
    font-size: 18px;
    line-height: 1.3rem;
    letter-spacing: 0.125rem;
    font-weight: 600;
    margin-top: 12px;
}

.info-title-3 {
    @apply font-tomorrow;
    font-size: 22px;
    margin-top: 10px;
}

.toggle-image-overflow span {
    overflow: visible !important;
}

.fullwidthImageblockTile {
    max-width: 90%;

    @screen lg {
        @apply max-w-full;
    }

    span {
        width: 100%;
    }
}

.frame-image-container {
    width: 80%;
}

.frame-image-container-right {
    width: 80%;

    span {
        img {
            height: 50vh !important;
        }
    }
}

.image-frame,
.image-frame-left {
    @apply absolute;
    top: 1%;
    left: 11%;
    width: 78vw;
}

.cta-position-Bottom {
    @apply flex flex-col;
}

.cta-position-Top {
    @apply flex flex-col-reverse;
}

.cta-position-Right {
    @apply flex flex-row;
}

.cta-position-Left {
    @apply flex flex-row-reverse justify-center;
}

.cta-position-text-Left {
    margin-right: 20px;
    @include description;
}

.cta-position-text-Right {
    margin-left: 20px;
    @include description;
}

.cta-position-text-Bottom {
    margin-top: 16px;
    @include description;
}

.cta-position-text-Top {
    margin-bottom: 16px;
    @include description;
}

.icons-description {
    @include description;
    @apply flex justify-center font-normal font-tomorrow text-center;
    font-weight: 600;
}

.cta-button-Right {
    @include cta;
    margin-right: 20px;
    @apply flex justify-center items-center;
}

.cta-button-Left {
    @include cta;
    margin-left: 20px;
    @apply flex justify-center items-center;
}

.cta-button-Bottom {
    margin-bottom: 10px;
    @include cta;
    @apply flex justify-center;
}

.cta-button-Top {
    @include cta;
    @apply flex justify-center;
}

.cta-button-base-styles {
    background-color: #ece81a;
    font-weight: 600 !important;
    color: #000 !important;
    border: none;
    font-size: 12px;
    letter-spacing: 0.05rem;
    padding: 1.3em 4.7em 1.5em;
    display: inline-block;
    width: unset !important;
    line-height: 1rem;
}

.description-image-Bottom,
.description-image-Top {
    @apply mx-auto lg:mx-0;
}

.description-Right,
.description-Left {
    @apply text-left;
}

.description-Top,
.description-Bottom {
    @apply text-center lg:text-left;
}

.has-animate {
    @apply opacity-0;
    transform: translateY(50px);
    transition: 250ms transform ease-in, 350ms opacity ease-in;

    &.onScreen {
        @apply opacity-100;
        transform: translateY(0);
    }
}

.slider-video {
    width: 100%;
}

.number_of_columns_4 {
    display: grid !important;
    grid-template-rows: repeat(4, auto);
    grid-column-gap: 2rem;
}

.number_of_columns_3 {
    grid-column-gap: 2.5rem;
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.fixed-column-gap {
    grid-column-gap: 45px;
}

.component-cta {
    @apply text-xs md:text-lg font-semibold not-italic flex relative p-0;
    line-height: 25px;
    letter-spacing: 1.6px;

    @screen md {
        line-height: 30px;
    }

    &-wrapper {
        margin-top: 20px;
    }

    & a,
    button {
        @apply inline-flex items-center relative;
    }

    &-icon {
        @apply items-center justify-center inline-flex absolute;
        right: -15px;
        top: 50%;
        width: 15px;
        height: 15px;
        transform: translateY(-50%);

        @screen md {
            width: 24px;
            right: -24px;
            height: 24px;
        }
    }

    &-background {
        padding: 1em 2em;
        line-height: 0.75rem;

        @screen md {
            line-height: 1.125rem;
        }
    }
}

@screen md {
    .inner-wrapper {
        .number_of_columns_4 {
            grid-column-gap: 1.5rem;
        }

        .number_of_columns_5 {
            grid-template-columns: repeat(5, 1fr);
        }

        .number_of_columns_3 {
            grid-template-columns: 1fr 1fr 1fr;
        }

        .info-subHeading {
            font-size: 1rem;
        }

        .info-heading {
            @apply text-left;
            font-size: 1.875rem;
        }

        .info-copy-block {
            font-size: 1.3rem;
            letter-spacing: inherit;
            line-height: 2.25rem;
        }

        .info-callouts {
            font-size: 3rem;
        }

        .info-section-title {
            font-size: 1.5625rem;
            line-height: 1;
            margin-bottom: 2rem;
            margin-top: 1.5rem;
        }

        .info-description {
            @apply text-left;
            line-height: 1.875rem;
            font-size: 1.25rem;
        }

        .info-disclaimer {
            @apply text-left;
            line-height: 1.875rem;
            font-size: 1.25rem;
        }

        .background-image {
            .image-or-video,
            .block-content {
                flex: 1 1 50%;
            }
        }

        .slider-video {
            .image-or-video,
            .block-content {
                flex: 1 1 50%;
            }
        }

        .cta-position-text-Right,
        .cta-position-text-Left,
        .cta-position-text-Bottom,
        .cta-position-text-Top {
            font-size: 1.3rem;
            line-height: 2.25rem;
        }

        .icons-description {
            font-size: 1rem;
            line-height: 1.4rem;
        }
    }

    .image-or-video {
        span {
            overflow: inherit !important;
        }
    }

    .frame-image-container,
    .frame-image-container-right {
        width: 65%;
        @apply relative lg:mt-28;

        span {
            img {
                display: block !important;
            }
        }
    }

    .image-frame,
    .image-frame-left {
        @apply absolute;
        left: 1%;
        width: 64vw;
    }

    .contentWidth_wrapperMaxWidth {
        padding: 0 6.25rem;
    }
}

@screen lg {
    .ml-auto {
        margin-left: auto;
    }

    .background-image {
        flex-wrap: nowrap;
    }

    .slider-video {
        flex-wrap: nowrap;
    }

    // .contentWidth_info-main-image-left {
    //     margin-right: 11rem !important;
    // }

    // .contentWidth_info-main-image-right {
    //     margin-left: 11rem !important;
    // }

    .inner-wrapper {
        max-width: 512px;

        .info-subHeading {
            font-size: 0.875rem;
        }

        .info-section-title {
            font-size: 3rem;
        }

        .cta-position-text-Bottom,
        .cta-position-text-Left,
        .cta-position-Right,
        .cta-position-text-Top {
            @apply text-left;
        }
    }

    .frame-image-container {
        transform: scale(1.3) translate(15%);
        width: 100%;

        span {
            img {
                height: 50vh !important;
            }
        }
    }

    .frame-image-container-right {
        transform: scale(1.3) translate(-15%);
        width: 100%;

        span {
            img {
                height: 50vh !important;
            }
        }
    }

    .image-frame {
        @apply absolute;
        width: 49.2vw;
    }

    .image-frame-left {
        @apply absolute;
        width: 48.5vw;
    }

    .info-heading {
        font-weight: 500;
        font-size: 2rem !important;
        line-height: 2.125rem;
    }

    .info-section-title {
        font-size: 2.5rem !important;
        line-height: 2.5rem;
    }
}

@screen xl {
    .contentWidth_wrapperMaxWidth {
        max-width: 640px;
    }

    .fullwidthImage_wrapperMaxWidth,
    .fullWidth_wrapperMaxWidth {
        max-width: 715px;
    }

    .number_of_columns_5 {
        grid-template-columns: repeat(5, minmax(0, 1fr));
        column-gap: 32px;
        row-gap: 8px;
    }

    .number_of_columns_3 {
        grid-column-gap: 4.5rem;
    }
}

@screen 2xl {
    .contentWidth_wrapperMaxWidth {
        max-width: 663px;
        padding: 0 6.25rem;
    }

    .fullwidthImage_wrapperMaxWidth,
    .fullWidth_wrapperMaxWidth {
        max-width: 780px;
    }
}

@screen xl-1610 {
    .number_of_columns_4 {
        grid-template-rows: repeat(3, auto) !important;
        grid-template-columns: repeat(4, minmax(0, 2fr)) !important;
        grid-column-gap: 0.5rem !important;
    }
}

@media (width: 768px) {
    .contentWidth_wrapperMaxWidth {
        padding: 0 4rem;
    }
}

.block-table {
    :global {
        .tableStyle {
            flex: 1 1 auto;
        }
    }
}

.negativeBottomMargin {
    margin-bottom: -2px;
}

.BlockTwoTile-container {
    &--padding-Bottom {
        @apply pb-8;
    }

    &--padding-Top {
        @apply pt-8 md-max:pt-0;
    }

    &--padding-Both {
        @apply py-8;
    }

    &--padding-None {
        @apply p-0;
    }

    .container-fluid {
        @screen md {
            padding-left: 1.25rem;
            padding-right: 1.25rem;

            .inner-wrapper {
                padding: 0 1rem;

                .info-heading {
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    display: -webkit-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .info-description {
                    -webkit-line-clamp: 6;
                    -webkit-box-orient: vertical;
                    display: -webkit-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        @screen lg {
            .inner-wrapper {
                padding: 0;
            }

            &.layout-left .inner-wrapper {
                margin-left: min(10.25vw, 105px);
                margin-right: min(3.47vw, 35px);
                padding: 0;
            }

            &.layout-right .inner-wrapper {
                margin-left: min(3.47vw, 35px);
                margin-right: min(10.25vw, 105px);
                padding: 0;
            }
        }

        @screen xl-1440 {
            padding-left: 5rem;
            padding-right: 5rem;
            max-width: 1920px;

            &.layout-left .inner-wrapper {
                margin-left: min(14.84vw, 285px);
                margin-right: min(8.59vw, 165px);
            }

            &.layout-right .inner-wrapper {
                margin-left: min(8.59vw, 165px);
                margin-right: min(14.84vw, 285px);
            }
        }
    }

    // Variant small
    &--Small {
        .background-image {
            @screen md-max {
                gap: 15px;
            }

            @screen md {
                flex-wrap: nowrap;
                justify-content: space-between;
            }

            .blockTwoTileContent {
                @screen md-max {
                    @apply mt-0;
                }

                @screen md {
                    flex-shrink: 0;
                }
            }

            .blockTwoTileMediaCustom {
                .imageVideo {
                    @screen md-max {
                        @apply pt-0;
                    }
                }
            }

            .blockTwoTileMediaCustom,
            .blockTwoTileContent {
                @screen md {
                    width: auto;
                }

                @screen lg {
                    width: 50%;
                }
            }
        }

        .inner-wrapper {
            align-items: flex-start;

            @screen md {
                max-width: 304px;
                padding: 0 20px;
                margin: 0;
            }

            @screen lg {
                width: 100%;
                max-width: 390px;
            }

            .info-heading {
                @screen md-max {
                    @apply mb-3;
                }

                @screen md {
                    font-size: 1.5rem !important;
                    line-height: 26px;
                }
            }

            .info-description {
                @screen md-max {
                    @apply mb-4;
                }

                @screen md {
                    font-size: 1rem;
                    line-height: 1.5rem;
                }
            }

            .component-cta {
                &-wrapper {
                    @screen md-max {
                        @apply m-0 px-5;
                    }
                }

                @screen md-max {
                    padding: 15px 30px;
                    font-size: 1rem;
                    line-height: 14px;
                }

                @screen md {
                    padding: 15px 30px;
                    font-size: 1rem;
                    line-height: 14px;
                }
            }
        }
    }
}
