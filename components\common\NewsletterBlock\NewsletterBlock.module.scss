.NewsletterBlockWrapper {
    padding-block: 40px;
    padding-inline: 30px 20px 30px 20px;
    gap: 20px;
    @apply flex justify-center flex-col;

    @screen md {
        padding-inline: 0;
        @apply flex-row;
    }

    .LeftContent {
        margin-bottom: 20px;
        @apply w-full;

        h3 {
            margin-bottom: 10px;
            font-style: normal;
            letter-spacing: 0;
            font-size: 32px;
            line-height: 34px;
            color: var(--secondary-off-black);
            @apply font-tomorrow font-bold uppercase;
        }

        p {
            font-size: 20px;
            line-height: 30px;
            color: var(--secondary-off-black);
            @apply font-sofiaSans #{!important};
        }

        @screen md {
            width: 41%;
            @apply m-0;
        }
    }

    .RightContent {
        gap: 20px;
        margin-bottom: 5px;
        @apply w-full flex flex-col;

        @screen sm {
            max-width: 420px;
        }

        @screen md {
            width: 41%;
        }

        [class*='newsletter-form'] {
            @apply w-full max-w-none flex-none m-0;

            h3,
            p {
                @apply hidden;
            }

            button {
                background-color: var(--secondary-off-black);
                @apply w-2/5 text-white;

                &:disabled,
                &[disabled] {
                    opacity: 0.5;
                    pointer-events: none;
                    cursor: default;
                }
            }

            input {
                border-radius: 2px;
            }

            [class*='newsletter-form-input-wrapper'] {
                @apply flex-1;

                [class*='success-input'] {
                    border: 1px solid var(--colspan-light-bg) !important;
                    @apply bg-white;
                }
            }

            [class*='response-message-success'] {
                color: var(--secondary-off-black) !important;
            }
        }

        .CheckBoxWrapper {
            gap: 8px;
            @apply flex items-start;

            .InputWrap {
                width: 25px;

                input {
                    width: 25px;
                    height: 25px;
                    outline: none;
                    border: 1px solid var(--colspan-light-bg);
                    border-radius: 5px;

                    &[type='checkbox'] {
                        accent-color: var(--secondary-off-black);
                        -ms-transform: scale(0.8);
                        /* IE */
                        -moz-transform: scale(0.8);
                        /* FF */
                        -webkit-transform: scale(0.8);
                        /* Safari and Chrome */
                        -o-transform: scale(0.8);
                        /* Opera */
                        transform: scale(0.8);
                    }
                }
            }

            p {
                font-size: 12px;
                line-height: 16px;
                @apply flex-1 m-0 font-sofiaSans #{!important};

                a {
                    color: var(--primary);
                }
            }
        }
    }
}