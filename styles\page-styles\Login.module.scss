.login {
    &-wrapper {
        width: 100%;
        padding: 32px 24px;
        max-width: 100%;

        @media (min-width: 1024px) {
            padding: 80px 0;
            max-width: 968px;
            @apply mx-auto;
        }
    }

    &-form {
        @apply w-full;

        &__title {
            @apply font-primary text-left uppercase;
            font-size: 24px;
            margin-bottom: 24px;
            color: black;
            font-weight: 600;
        }

        &__desc {
            @apply font-primary text-left;
            font-size: 16px;
            margin-bottom: 24px;
            color: black;
            font-weight: 300;
        }

        &__terms {
            @apply text-black;

            &.--with-btn {
                @apply underline;
                color: var(--primary)
            }
        }
    }

    &-select {
        appearance: none !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;

        &::-ms-expand {
            display: none !important;
            /* IE10 and IE11 */
        }

        &__arrow {
            @apply absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 14px;
            pointer-events: none;
        }
    }

    &-input {
        @apply font-primary w-full h-full;
        line-height: 20px;
        font-size: 13px;
        flex: 1 1 auto;
        padding: 10px 14px;
        outline-style: auto;

        &__password-toggle {
            outline: none;
            border: none;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        &__container {
            height: 40px;
            background-color: black;
            margin-top: 4px;
            @apply flex items-center justify-between;

            &.--with-icon {
                position: relative;

                input {
                    padding: 10px 44px 10px 14px;
                }
            }
        }

        &__label {
            @apply font-primary uppercase;
            color: black;
            font-size: 13px;
        }

        &__helper-text {
            color: black;
            font-size: 13px;
            font-weight: 500;
            @apply font-primary;

            &.--error {
                color: #FF4C4C;
            }
        }

        &__checkbox {
            @apply uppercase text-black text-xs font-normal flex items-center leading-tight;
        }
    }

    &-btn-submit {
        @apply md:w-full p-5 relative overflow-hidden;
        width: 100%;
        transition: background-color .3s ease;
        z-index: 1;

        &:not(:disabled):hover {
            background: var(--secondary-off-black);

            span {
                color: var(--dark-bg-btn-primary);
            }
        }

        &:after {
            content: '';
            position: absolute;
            z-index: -1;
            padding: 0 30px;
            display: block;
        }

        @apply opacity-100 text-black border-none font-tomorrow font-semibold #{!important};
        background: var(--primary);

        &[disabled] {
            @apply opacity-50 #{!important};
        }

        &__action-response {
            margin-top: 4px;
            color: black;
            font-size: 14px;
            font-weight: 500;
            @apply font-primary w-full text-center inline-block;

            &.--error {
                color: #FF4C4C;
            }

            &.--success {
                color: green;
            }
        }
    }

    &-forgot-password-btn {
        @apply text-black text-xs font-normal flex items-center leading-tight whitespace-nowrap underline;
    }

    &-modal {
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 999;
        top: 0;
        left: 0;

        :global {
            .modal-wrapper {
                @apply w-full block items-center content-center overflow-hidden outline-none;
                background-color: rgba(0, 0, 0, 0);
                max-height: 600px;
                height: calc((100vw - 20px) * 0.7);
                position: relative;
                padding-top: 40px;
                padding-bottom: 40px;
                padding-left: 8px;
                padding-right: 8px;
            }

            .modal-content {
                @apply w-full;
                background-color: rgba(0, 0, 0, 0);
                height: 95%;
                max-width: 900px;
                padding: 0rem;
                margin-left: auto;
                margin-right: auto;
                border-style: none;
            }

            .modal-body {
                @apply h-full w-full;
            }

            .modal:focus {
                @apply outline-none;
            }

            .close-panel {
                @apply absolute m-0;
                height: 44px;
                background-color: rgba(0, 0, 0, 0);
                width: 100%;
                top: -45px;
                cursor: pointer !important;

                &:hover>svg {
                    color: black;
                }

                &:focus {
                    @apply outline-none;
                }
            }

            .close-panel>svg {
                @apply transition ease-in-out duration-150 absolute text-gray-500;
                right: -3px;
                top: 10px;

                &:hover {
                    color: black;
                }

                &:focus {
                    @apply outline-none;
                }
            }

            .focus-trap {
                width: 100%;
            }
        }
    }
}