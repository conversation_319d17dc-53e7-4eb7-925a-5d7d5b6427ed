.SelectedFilterWrapper {
    gap: 8px;
    @apply flex flex-col;
    margin-bottom: 25px;

    @screen md-max {
        padding-bottom: 10px;
        margin-top: 7px;
        margin-bottom: 20px;
        border-bottom: 1px solid var(--steel-gray20);
    }

    button {
        min-height: 24px;
        gap: 5px;
        font-size: 16px;
        padding-inline: 5px;
        color: var(--secondary-off-black);
        svg {
            @apply cursor-pointer;
        }
        @apply flex flex-row-reverse justify-end font-sofiaSans font-normal items-center;
    }

    .ClearAllBtn {
        min-height: 16px;
        padding-inline: 14px;
        font-size: 14px;
        height: 16px;
        color: var(--steel-gray40);
        &:hover {
            color: var(--steel-gray80);
        }
        @apply font-medium;
    }
}

.Subtitle {
    padding: 8px 0px 10px 0px;
    font-size: 18px;
    line-height: 1.2;
    color: var(--secondary-off-black);
    @apply font-semibold uppercase font-sofiaSans;

    @screen md-max {
        @apply hidden;
    }
}
