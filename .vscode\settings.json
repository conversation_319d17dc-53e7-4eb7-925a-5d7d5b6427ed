{"scss.lint.unknownAtRules": "ignore", "terminal.integrated.env.windows": {"node_options": "--max-old-space-size=8192"}, "tailwindCSS.experimental.classRegex": [["/\\*tw\\*/ ([^]*) \\}", "(?:`|')([^]*?)(?:'|`)"]], "tailwindCSS.rootFontSize": 13, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "files.trimTrailingWhitespace": true, "prettier.configPath": ".prettierrc.js"}