import React from 'react'
import { BreadcrumbItem } from '@corsairitshopify/corsair-breadcrumbs/src/BreadcrumbItem'

interface BreadcrumbsPageProps {
    rootText?: string
    separator?: string
    slug: string[]
}

export const BreadcrumbsPage = (props: BreadcrumbsPageProps): JSX.Element => {
    const { rootText, separator = ' / ', slug } = props

    return (
        <span>
            {rootText && (
                <>
                    <BreadcrumbItem url="/" title={rootText} />
                </>
            )}

            {slug.map((s, i) => (
                <>
                    {separator}
                    {i + 1 === slug.length ? (
                        <strong>
                            <BreadcrumbItem title={s} />
                        </strong>
                    ) : (
                        <BreadcrumbItem title={s} />
                    )}
                </>
            ))}
        </span>
    )
}
