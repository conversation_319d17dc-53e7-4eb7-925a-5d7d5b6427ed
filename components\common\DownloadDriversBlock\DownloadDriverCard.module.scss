.download-driver-card {
    @apply w-full flex flex-col md:flex-row items-start md:items-center;
    background-color: var(--item-bg-color);

    &--image {
        @apply relative w-full md:w-2/12 flex-shrink-0;
        height: 210px;

        @screen md {
            height: 167px;
            width: 167px;
        }

        span {
            margin: auto !important;
            max-width: 167px;
            max-height: 167px;
        }
    }

    &--content {
        @apply flex-grow font-sofiaSans flex flex-row justify-between items-center;
        gap: 20px;
        padding: 20px;
        white-space: pre-wrap;
        color: var(--item-text-color);

        @screen md-max {
            @apply w-full;
        }
    }

    &--title {
        @apply font-tomorrow font-medium uppercase tracking-normal;
        margin-bottom: 10px;
        font-size: 1.25rem;
        line-height: 1.5rem;
    }

    &--platform {
        @apply uppercase font-medium;
    }

    &--release-info {
        font-size: 0.875rem;

        p {
            @apply font-sofiaSans #{!important};
        }

        a,
        a:visited {
            color: var(--primary);
            text-decoration: none;
        }
    }

    &--download-button {
        @apply flex items-center font-tomorrow font-medium leading-none uppercase;
        padding: 15px 30px;
        font-size: 1rem;
        line-height: 0.875rem;
        border: 1px solid transparent;

        @screen md-max {
            padding: 16px;

            span {
                display: none;
            }
        }

        &:hover {
            border-color: var(--tertiary-modern-white);
        }

        &::after {
            margin-left: 10px;

            @screen md-max {
                margin-left: 0;
            }

            width: 17px;
            height: 17px;
            display: inline-block;

            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: inherit;
            line-height: inherit;
            color: inherit;
            content: '\e914';
            font-family: 'Scuf-Icon';
            vertical-align: top;
            display: inline-block;
            font-weight: 400;
            overflow: hidden;
            speak: none;
            text-align: center;
        }
    }
}
