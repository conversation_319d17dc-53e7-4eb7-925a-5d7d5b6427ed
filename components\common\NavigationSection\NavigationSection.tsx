import { btnType, CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import classNames from 'classnames'
import { CTA } from '../CTA'
import s from './NavigationSection.module.scss'

export type NavigationSection = {
    content: {
        items: CTAType[]
    }
}

const NavigationSection: React.FC<NavigationSection> = (
    props: NavigationSection
) => {
    const { content } = props
    const getButtonTypeClass = (type?: btnType) => {
        if (!type) {
            return ''
        }

        switch (type) {
            case 'primary':
                return 'scuf-button-primary'
            case 'secondary':
                return 'scuf-button-secondary'
            case 'link':
                return 'scuf-button-link'
            default:
                return ''
        }
    }

    return (
        <section className={s['pdp_slim_navigation']}>
            <ul>
                {content.items.map((item) => {
                    const { displayText, openInANewTab, url, buttonType } = item
                    return (
                        <li key={url}>
                            <CTA
                                cta={{
                                    displayText: displayText,
                                    openInANewTab: openInANewTab,
                                    openInPopup: false,
                                    meta: { contentType: 'componentCta' },
                                    url: url
                                }}
                                showDisplayText
                                className={classNames(
                                    s['button'],
                                    getButtonTypeClass(buttonType)
                                )}
                            />
                        </li>
                    )
                })}
            </ul>
        </section>
    )
}

export default NavigationSection
