import { ModelTypeEnum } from '@config/base'
import Image from '@corsairitshopify/corsair-image'
import { useProductUrlBuilder } from '@lib/hooks/useBuildProductUrl'
import { Products } from '@pylot-data/hooks/product/use-related-products-fetch'
import { ConfigurableProduct, GiftCardProduct } from '@pylot-data/pylotschema'
import Link from 'next/link'
import { FC, memo, useMemo } from 'react'
import { ProductPrice } from '../ProductPrice/ProductPrice'
import BaseCarousel from './BaseCarousel'
import cn from 'classnames'
import styles from './CrosssellCarousel.module.scss'

type ProductProps = Pick<Products, 'productData'>

interface Props {
    products: ProductProps
    title?: string
    titleStyles?: any
    showItemCategory?: boolean
    containerClassName?: string
    variantClassName?: string
}

const Item: FC<{
    product: ConfigurableProduct & GiftCardProduct
    showItemCategory?: boolean
}> = ({ product, showItemCategory }) => {
    const productUrlBuilder = useProductUrlBuilder({
        page: ModelTypeEnum.PRODUCT
    })

    if (!product) {
        return null
    }

    return (
        <div className={styles.CarouselItemWrapper}>
            <div className={styles.ImageWrapper}>
                <Image
                    src={
                        product?.thumbnail?.url ??
                        '/images/default-product-image.png'
                    }
                    layout="fill"
                />
            </div>
            <Link href={productUrlBuilder({ product })}>
                <a role="button" aria-label="Go to product page">
                    <h5 className={styles.ProductName}>{product?.name}</h5>
                </a>
            </Link>
            {showItemCategory ? (
                <p className={styles.ProductCategory}>
                    {product?.categories?.[0]?.name}
                </p>
            ) : null}
            <div className={styles.PdpPrice}>
                <ProductPrice
                    priceRange={product?.price_range}
                    showCurrencyCode
                />
            </div>
        </div>
    )
}

const CrosssellCarousel: FC<Props> = ({
    products,
    title,
    titleStyles,
    showItemCategory,
    containerClassName,
    variantClassName
}) => {
    const updatedProduct = useMemo(
        () =>
            products.productData.map((item) => ({
                ...item,
                id: item.uid
            })),
        [products]
    )

    return (
        <div
            className={cn(
                styles.CrossellWrapper,
                variantClassName ? styles[variantClassName] : ''
            )}
        >
            <div className={cn(containerClassName)}>
                {title && (
                    <h3 className={styles.Heading} style={titleStyles}>
                        {title}
                    </h3>
                )}
                <BaseCarousel
                    list={updatedProduct}
                    renderItem={(item) => (
                        <Item
                            product={item as any}
                            showItemCategory={showItemCategory}
                        />
                    )}
                    swiperProps={{
                        spaceBetween: 20
                    }}
                />
            </div>
        </div>
    )
}

export default memo(CrosssellCarousel)
