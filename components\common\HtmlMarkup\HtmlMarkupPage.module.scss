.custom-cms-page {
    font-family: "Sofia Sans Semi Condensed", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    unicode-bidi: isolate;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1rem;
    padding: 0 20px;

    @media (min-width: 769px) {
        max-width: 1366px;
        margin-left: auto;
        margin-right: auto;
    }

    p {
        min-height: 24px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        text-transform: uppercase;
        letter-spacing: 0;

        @screen md-max {
            overflow-wrap: anywhere;
        }
    }

    h1 {
        margin: 40px 0px;
        font-family: "Tomorrow", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-style: normal;
        text-transform: uppercase;
        letter-spacing: 0;
        font-size: 54px;
        line-height: 56px;
        text-align: center;
        font-weight: 500;
    }

    h2 {
        font-family: "Tomorrow", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-weight: 500;
        line-height: 46px;
        font-size: 2.75rem;
        margin-top: 1.5625rem;
        margin-bottom: 1.25rem;
    }

    ul,
    ol {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    ul {
        display: block;
        list-style-type: disc;
        margin-block-start: 1em;
        margin-block-end: 1em;
        padding-inline-start: 40px;
        unicode-bidi: isolate;
    }

    ol {
        display: block;
        list-style-type: decimal;
        margin-block-start: 1em;
        margin-block-end: 1em;
        padding-inline-start: 40px;
        unicode-bidi: isolate;
    }

    li {
        display: list-item;
        text-align: -webkit-match-parent;
        unicode-bidi: isolate;
        margin-top: 0.625rem;
        margin-bottom: 0.625rem;
        font-family: "Sofia Sans Semi Condensed", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-weight: 400;

        p {
            @apply mb-0;
        }
    }

    h3 {
        font-family: "Tomorrow", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-weight: 500;
        line-height: 34px;
        font-size: 2rem;
        margin-top: 0.9375rem;
        margin-bottom: 0.625rem;
    }

    h6 {
        font-family: "Sofia Sans Semi Condensed", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-weight: 700;
        font-size: 1rem;
        line-height: 24px;
    }

    a,
    .alink {
        color: #ff8f1c;
        text-decoration: none;
    }

    u + div,
    h6 + div {
        @apply hidden;
    }

    u:has(+ div),
    h6:has(+ div) {
        @apply block;
        margin-bottom: 0.625rem;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        max-width: 100%;
    }

    table > thead > tr > th,
    table > tbody > tr > th,
    table > tfoot > tr > th,
    table > thead > tr > td,
    table > tbody > tr > td,
    table > tfoot > tr > td {
        padding: 11px 10px;
    }

    table > tbody > tr > th,
    table > tfoot > tr > th,
    table > tbody > tr > td,
    table > tfoot > tr > td {
        vertical-align: top;
    }
}
