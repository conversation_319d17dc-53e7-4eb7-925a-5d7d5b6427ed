import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface BrandSelectorInterface {
    brandItems: BrandItemInterface[]
    backgroundColor: Color
    title: string
    titleColor: Color
}

export interface BrandItemInterface {
    image: CloudinaryImage[]
    mobileImage: CloudinaryImage[]
    url: string
    identifier: string
}

interface Color {
    id: string
    name: string
    value: string
}
