import ChevronRightBanner from '@components/icons/ChevronRightBanner'
import Link from '@corsairitshopify/pylot-ui/src/Link'
import { GridImage } from '../types'

import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import s from './image.module.scss'

type ImageGridDetailsProps = {
    content: GridImage
}

const c = /*tw*/ {
    cta: `flex font-tomorrow text-learnmore cursor-pointer ${s['learn-more']}`,
    cardContainer: 'w-full h-full px-4 ',
    caption: `font-tomorrow text-white uppercase ${s['caption']}`,
    captionPostion: `w-full md:w-1/2 lg:w-full absolute ${s['caption-position']}`
}

const ImageGridDetails = ({ content }: ImageGridDetailsProps) => {
    const ctaPosition = content?.ctaButtonPosition
        ?.toLocaleLowerCase()
        .replace(' ', '-')
    const logoPosition = content?.logoPosition?.toLowerCase().replace(' ', '-')
    const showLogo =
        (content?.ctaButton && ctaPosition !== logoPosition) ||
        !content?.ctaButton
    const isCaptionTop =
        (!content?.cloudinaryDesktopMedia?.[0]?.resource_type?.startsWith(
            'video'
        ) &&
            content.ctaButton &&
            ctaPosition === 'lower-left' &&
            s['caption-position-cta']) ||
        (!content?.cloudinaryDesktopMedia?.[0]?.resource_type?.startsWith(
            'video'
        ) &&
            content.cloudinaryLogo &&
            logoPosition === 'lower-left' &&
            s['caption-position-logo'])
    let bgLogo
    if (content.cloudinaryLogo) {
        bgLogo = {
            backgroundImage: `url("${
                content.cloudinaryLogo?.[0]?.secure_url
                    ? convertUrlFormat(content.cloudinaryLogo[0].secure_url)
                    : ''
            }")`
        }
    }
    return (
        <div className={c.cardContainer}>
            {content?.caption && (
                <p
                    className={cn(
                        c.caption,
                        isCaptionTop,
                        !isCaptionTop && c.captionPostion
                    )}
                    dangerouslySetInnerHTML={{ __html: content.caption }}
                />
            )}
            {!content?.cloudinaryDesktopMedia?.[0]?.resource_type?.startsWith(
                'video'
            ) &&
                content?.ctaButton &&
                ctaPosition && (
                    <div className={cn(s[`cta-link-${ctaPosition}`], 'w-full')}>
                        <Link href={content?.ctaButton[0]?.url}>
                            <a
                                href={content?.ctaButton[0]?.url}
                                target={
                                    content?.ctaButton[0]?.openInANewTab
                                        ? '_blank'
                                        : '_self'
                                }
                                rel="noopener noreferrer"
                                className={cn(c.cta, s[`cta-${ctaPosition}`])}
                            >
                                {content?.ctaButton && (
                                    <p
                                        dangerouslySetInnerHTML={{
                                            __html:
                                                content?.ctaButton[0]
                                                    .displayText
                                        }}
                                    />
                                )}
                                <div className={cn(s['right-arrow-wrapper'])}>
                                    <ChevronRightBanner />
                                </div>
                            </a>
                        </Link>
                    </div>
                )}
            {showLogo &&
                !content?.cloudinaryDesktopMedia?.[0]?.resource_type?.startsWith(
                    'video'
                ) &&
                content?.cloudinaryLogo &&
                logoPosition && (
                    <div
                        className={`${s[`logo-container-${logoPosition}`]}`}
                        style={bgLogo}
                    >
                        {/* {content?.logo?.map((data, key) => (
                            <GridIcon
                                key={key}
                                icon={data.image.file.url}
                                alt={data.image.description}
                                url={data.url}
                                newTab={data.newTab}
                            />
                        ))} */}
                    </div>
                )}
        </div>
    )
}

export default ImageGridDetails
