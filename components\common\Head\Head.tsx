/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable react/no-unescaped-entities */
/* eslint-disable i18next/no-literal-string */
import { Rollbar } from '@components/common/Rollbar'
import { useStoreConfig } from '@config/hooks/useStoreConfig'
import { socialDefaultContent } from '@config/seo/defaultContents'
import { OneTrust } from '@corsairitshopify/corsair-onetrust'
import { SEO } from '@corsairitshopify/corsair-seo'
import { LANGUAGE_CODES_MAP } from 'localesConfig'
import NextHead from 'next/head'
import { useRouter } from 'next/router'
import Script from 'next/script'
import { FC } from 'react'
const fontsToPreload = [
    // Load Tomorrow
    '/fonts/Tomorrow/Tomorrow-Black.ttf',
    '/fonts/Tomorrow/Tomorrow-BlackItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-Bold.ttf',
    '/fonts/Tomorrow/Tomorrow-BoldItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-ExtraBold.ttf',
    '/fonts/Tomorrow/Tomorrow-ExtraBoldItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-ExtraLight.ttf',
    '/fonts/Tomorrow/Tomorrow-ExtraLightItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-Italic.ttf',
    '/fonts/Tomorrow/Tomorrow-Light.ttf',
    '/fonts/Tomorrow/Tomorrow-LightItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-Medium.ttf',
    '/fonts/Tomorrow/Tomorrow-MediumItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-Regular.ttf',
    '/fonts/Tomorrow/Tomorrow-SemiBold.ttf',
    '/fonts/Tomorrow/Tomorrow-SemiBoldItalic.ttf',
    '/fonts/Tomorrow/Tomorrow-Thin.ttf',
    '/fonts/Tomorrow/Tomorrow-ThinItalic.ttf',
    '/fonts/Verveine/Verveine-Regular.woff2',

    // Load SofiaSansSemiCondensed
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-Black.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-BlackItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-Bold.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-BoldItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-ExtraBold.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-ExtraBoldItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-ExtraLight.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-ExtraLightItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-Italic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-LightItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-Medium.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-MediumItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-Regular.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-SemiBold.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-SemiBoldItalic.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-Thin.ttf',
    '/fonts/SofiaSansSemiCondensed/SofiaSansSemiCondensed-ThinItalic.ttf'
]
const rel = 'alternate'
const gigyaLocaleSpecialCase: { [key: string]: string | undefined } = {
    pt: 'pt-br',
    zh: 'zh-cn',
    tw: 'zh-tw'
}

const URL_NOT_FOUND = '/404'
const URL_PDP = '/p/[product_slug]'
const REGION_UNAVAILABLE_HREFLANG = ['WW', 'EU', 'LM']

const convertBaseUrl = 'https://cdn-4.convertexperiments.com/js'

const Head: FC = () => {
    const {
        base: {
            gigya,
            customMetaTags,
            regionMapLanguages,
            url,
            oneTrust: { dataDomainScript, enabled: otEnabled } = {}
        },
        seo: {
            tagManager: { convert }
        }
    } = useStoreConfig()
    const apiKey = gigya?.apiKey
    const enabled = gigya?.enabled

    const { locale, asPath, pathname } = useRouter()
    const origin = url.baseUrl
    const isConvertEnabled = convert && convert?.enabled
    const convertID =
        isConvertEnabled && convert?.containerId ? convert.containerId : ''
    const currentLang = locale?.split('-')[0] || ''
    const currentRegion = locale?.split('-')[1] || ''
    const gigyaLang = gigyaLocaleSpecialCase[currentLang] || currentLang
    let path = asPath
    if (path.includes('?')) {
        path = path.substring(0, path.indexOf('?'))
    }

    if (path.endsWith('/')) {
        path = path.slice(0, -1)
    }

    const fonts = fontsToPreload.map((font) => (
        <link rel="preload" href={font} as="font" crossOrigin="" key={font} />
    ))
    const getRegionAvailable = () => {
        switch (pathname) {
            case URL_NOT_FOUND:
            case URL_PDP:
                return []
            default:
                return regionMapLanguages
        }
    }
    const getLanguageAvailable: any = () => {
        switch (pathname) {
            case URL_NOT_FOUND:
            case URL_PDP:
                return []
            default:
                return Object.values(LANGUAGE_CODES_MAP).filter(
                    (language) =>
                        language !== currentLang && //
                        !getRegionAvailable().some(
                            (region) =>
                                region.redirectLanguage === language &&
                                region.redirectRegion ===
                                    currentRegion.toLocaleLowerCase()
                        )
                )
        }
    }
    const getCurrentRegion: any = () => {
        const region = regionMapLanguages.find(
            (country) =>
                country.redirectRegion === currentRegion?.toLocaleLowerCase() ||
                country.region === currentRegion?.toLocaleLowerCase()
        )
        return region?.region
    }
    const generateCustomMetaTags: any = () => {
        return customMetaTags?.map((tag) => (
            <meta
                key={tag.name}
                name={tag?.name || ''}
                content={tag?.contentKey || ''}
            />
        ))
    }

    return (
        <>
            <NextHead>
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1"
                />
                {generateCustomMetaTags()}
                <link
                    rel="manifest"
                    href="/site.webmanifest"
                    key="site-manifest"
                />
                {fonts}
                {isConvertEnabled && (
                    <script
                        type="text/javascript"
                        src={`${convertBaseUrl}/${convertID}.js`}
                    />
                )}
                {getRegionAvailable()?.map((country: any) =>
                    country?.redirectRegion ? (
                        <link
                            key={`${country?.redirectLanguage}-${country.region}`}
                            rel={rel}
                            hrefLang={`${country.redirectLanguage}-${country.region}`}
                            href={`${origin}${country?.redirectRegion}/${country?.redirectLanguage}${path}`}
                        />
                    ) : (
                        country.languages.map(
                            (language: any, index: number) => {
                                const href = `${origin}${country.region}/${language}${path}`
                                const hrefLang = `${language}-${country.region}`
                                return (
                                    <>
                                        {index === 0 &&
                                            country.defaultLanguage && (
                                                <link
                                                    key={`${country.defaultLanguage}-${country.region}`}
                                                    rel={rel}
                                                    hrefLang={`${country.defaultLanguage}`}
                                                    href={`${origin}${country.region}/${country.defaultLanguage}${path}`}
                                                />
                                            )}
                                        <link
                                            key={hrefLang}
                                            rel={rel}
                                            hrefLang={hrefLang}
                                            href={href}
                                        />
                                    </>
                                )
                            }
                        )
                    )
                )}
                {!REGION_UNAVAILABLE_HREFLANG.includes(
                    currentRegion.toUpperCase()
                ) &&
                    getLanguageAvailable().map((lang: any) => (
                        // eslint-disable-next-line react/jsx-key
                        <link
                            rel={rel}
                            hrefLang={`${lang}-${getCurrentRegion()}`}
                            href={`${origin}${currentRegion.toLocaleLowerCase()}/${lang}${path}`}
                        />
                    ))}
            </NextHead>
            {otEnabled && dataDomainScript ? (
                <OneTrust dataDomainScript={dataDomainScript} />
            ) : null}
            {enabled && (
                <Script
                    id="gigya"
                    src={`https://cdns.gigya.com/js/gigya.js?apiKey=${apiKey}&lang=${gigyaLang}`}
                    type="text/javascript"
                />
            )}
            <SEO socialDefaultContent={socialDefaultContent} />
            <Rollbar />
        </>
    )
}

export default Head
