import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { ReactNode, useMemo, useRef } from 'react'
import { A11y, Navigation, Pagination } from 'swiper'
import { Swiper, SwiperProps, SwiperSlide } from 'swiper/react'

import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { deepMerge } from '@typescript-eslint/experimental-utils/dist/eslint-utils/deepMerge'
import { executeAnimatePagination } from 'helpers/paginationHelpers'
import { generateRandomString } from 'helpers/stringHelper'

import { CtaButton } from '../Carousel/Carousel'
import { CarouselWrapperType } from '../CarouselWrapper/CarouselWrapper.types'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { IMeta } from '../types'

import s from './CarouselSlider.module.scss'

type CarouselSliderProps = {
    content: CarouselWrapperType
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    renderSlideItem: (prod: any) => ReactNode
    overrideCarouselConfig?: SwiperProps
}

type DesktopMobileImage = {
    desktopProductTitle: string
    desktopProductDescription: string
    mobileProductTitle: string
    desktop: ImageComponent
    mobile: ImageComponent
    ctaButton?: CtaButton
}

type ImageComponent = {
    title: string
    image: ImageType
    url: string
    cloudinaryImage?: CloudinaryImage[]
}

export type CSLObject = {
    productImages: DesktopMobileImage[]
    identifier: string
    heading: string
    cloudinaryLogo: CloudinaryImage[]
    cloudinaryMainImage: CloudinaryImage[]
}

export type CslContainerTiles = {
    identifier: string
    productSlide: CSLObject[]
    copyBlock?: { subHeading?: string; heading?: string; text?: string }
    meta: IMeta<'carouselppContainer'>
    displayType: string
    cloudinaryBackgroundImage?: CloudinaryImage[]
    cloudinaryMobileBackgroundImage?: CloudinaryImage[]
    backgroundColor?: string
    desktopPaddingTop?: string
    desktopPaddingBottom?: string
    mobilePaddingTop?: string
    mobilePaddingBottom?: string
}

const CarouselSlider = ({
    content,
    renderSlideItem,
    overrideCarouselConfig
}: CarouselSliderProps): JSX.Element | null => {
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const isMobile = useMediaQuery('(max-width: 767px)')
    const { t } = useTranslation(['common'])
    const formattedName = useMemo(() => {
        return generateRandomString(10)
    }, [])

    const slides = content?.productSlide || []
    const numberOfSlides = slides.length

    const carouselConfig: SwiperProps = useMemo(() => {
        const baseConfig: SwiperProps = {
            modules: [Navigation, Pagination, A11y],
            mousewheel: true,
            speed: 500,
            spaceBetween: 20,
            slidesPerGroup: 1,
            slidesPerView: numberOfSlides > 1 ? 1.2 : 1,
            centerInsufficientSlides: true,
            navigation: {
                prevEl: `.${formattedName}-prev`,
                nextEl: `.${formattedName}-next`,
                disabledClass: '.disabled:opacity-50'
            },
            onBreakpoint: (swiper: unknown) => {
                animatePagination(swiper)
            },
            onSlideChange: (swiper: unknown) => {
                animatePagination(swiper)
                handleSlideChange(swiper)
            },
            pagination: {
                el: `.${formattedName}-pagination`,
                clickable: true,
                renderBullet: (idx: number, className: string) => {
                    return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                }
            },
            breakpoints: {
                500: {
                    slidesPerView: numberOfSlides > 2 ? 2.2 : 2,
                    slidesPerGroup: 1
                },
                // 768: {
                //     slidesPerView: numberOfSlides > 3 ? 3.4 : 3,
                //     slidesPerGroup: 1
                // },
                1024: {
                    slidesPerView: numberOfSlides > 3 ? 3.6 : 3,
                    slidesPerGroup: 1
                },
                1440: {
                    slidesPerView: numberOfSlides > 4 ? 4.4 : 4,
                    slidesPerGroup: 1
                }
            },
            lazy: true
        }
        return deepMerge(
            baseConfig as Parameters<typeof deepMerge>[0],
            overrideCarouselConfig as Parameters<typeof deepMerge>[1]
        )
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [overrideCarouselConfig, formattedName, numberOfSlides])

    if (!content) return null

    const animatePagination = (swiper: unknown) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: unknown) => {
        animatePagination(swiper)
    }

    return (
        <>
            <div
                className={cn(
                    s['carousel-slider-container'],
                    'carousel-slider-container',
                    'overflow-hidden'
                )}
            >
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['prev'],
                        `${formattedName}-prev`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                    )}
                    aria-label={t('previous')}
                >
                    <ArrowRightIcon />
                </button>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['next'],
                        'scuf-button-primary',
                        'scuf-show-right-arrow',
                        `${formattedName}-next`
                    )}
                    aria-label={t('next')}
                >
                    <ArrowRightIcon />
                </button>

                {content.showRearShadow && (
                    <>
                        <div className={cn(s['faded-left'])} />
                        <div className={cn(s['faded-right'])} />
                    </>
                )}

                <Swiper
                    className={cn(s['swiper-container'], 'swiper-container')}
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    {...carouselConfig}
                >
                    {slides.map((prod, key) => (
                        <SwiperSlide key={key}>
                            {renderSlideItem(prod)}
                        </SwiperSlide>
                    ))}
                </Swiper>
            </div>
            {isMobile && (
                <div
                    className={cn(
                        s['pagination-container'],
                        'pagination-container',
                        s['pagination-container-mobile'],
                        'pagination-container-mobile'
                    )}
                    ref={containerRef}
                >
                    <div className="relative inline-block md:hidden">
                        <div
                            ref={paginationRef}
                            className={`${formattedName}-pagination flex gap-4`}
                        />
                        <div className="dots-line" />
                        <div
                            ref={activeDotRef}
                            className="active-animated-dot"
                        />
                    </div>
                </div>
            )}
        </>
    )
}

export default CarouselSlider
