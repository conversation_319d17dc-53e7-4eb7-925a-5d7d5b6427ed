.carousel-wrapper {
    @apply bg-cover;
    background-image: var(--mobile-bg-image);
    background-color: #00000000;

    @screen md {
        background-image: var(--desktop-bg-image);
    }
}

.block-two-tile-top-container {
    @apply flex items-center justify-between;
    padding-inline: 20px;
    padding-top: 36px;
    padding-bottom: 20px;
    @screen md {
        padding-inline: 80px;
    }

    .heading {
        @apply font-tomorrow font-medium;
        color: var(--primary);
        line-height: 100%;

        &.heading-h1 {
            font-size: 2.125rem;
        }
        &.heading-h2 {
            font-size: 2rem;
        }
        &.heading-h3 {
            font-size: 2rem;
        }
        &.heading-h4 {
            font-size: 2rem;
        }

        @screen md {
            &.heading-h1 {
                font-size: 3.375rem;
            }
            &.heading-h2 {
                font-size: 2.75rem;
            }
            &.heading-h3 {
                font-size: 2.25rem;
            }
            &.heading-h4 {
                font-size: 2rem;
            }
        }
    }
}

.block-two-tile-swiper-container {
    @apply relative;

    @screen md-max {
        :global {
            .block-two-tile-media {
                padding-inline: 20px;
            }
            .image-or-video {
                padding-top: 0;
            }
        }
    }
}

.swiper-container {
    :global {
        .swiper-slide-shadow-right {
            background-image: linear-gradient(
                90deg,
                rgba(0, 0, 0, 0.2) 0%,
                rgba(0, 0, 0, 0.8) 15%,
                rgba(0, 0, 0, 1) 100%
            );
        }

        .swiper-slide-shadow-left {
            background-image: linear-gradient(
                270deg,
                rgba(0, 0, 0, 0.2) 0%,
                rgba(0, 0, 0, 0.8) 15%,
                rgba(0, 0, 0, 1) 100%
            );
        }
    }
}

.carousel-nav-button {
    transform: translateY(-50%);
    width: 52px;
    height: 48px;
    border: none;
    outline: none;
    @apply z-10 top-1/2 absolute bg-primary flex items-center justify-center overflow-hidden;

    :global(#Oval-2) {
        stroke: transparent;
    }

    div {
        button {
            &:focus {
                outline: none;
            }
        }
    }

    &.prev,
    &.next {
        @screen md-max {
            @apply hidden;
        }
    }

    &.prev {
        left: 3%;
        svg {
            transform: rotateZ(180deg);
        }
    }

    &.next {
        right: 3%;
    }
}

.pagination-container {
    &.pagination-container {
        @apply flex items-center justify-around pb-10;
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--primary);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }
}
