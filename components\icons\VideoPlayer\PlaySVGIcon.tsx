import { FC, HTMLAttributes } from 'react'

interface PlaySVGIconProps extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
    color?: string
}

export const PlaySVGIcon: FC<PlaySVGIconProps> = ({
    width = 80,
    height = 91,
    color = 'white',
    ...props
}) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 80 91"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M3 82.3107V7.83226C3 4.10842 7.03958 1.78817 10.2562 3.66452L74.0944 40.9036C77.2867 42.7654 77.2867 47.3767 74.0944 49.2392L10.2562 86.4775C7.03958 88.3545 3 86.0346 3 82.3107Z"
            stroke={color}
            strokeWidth="6"
            strokeLinecap="round"
        />
    </svg>
)
