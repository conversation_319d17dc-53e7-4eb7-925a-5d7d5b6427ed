.container {
    @apply flex justify-center items-center w-full h-screen overflow-y-auto overflow-x-hidden absolute left-0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    visibility: hidden;

    &[data-show='true'] {
        z-index: 1000;
        visibility: visible;
    }
}

.dialog {
    max-width: 500px;
    background-color: #ffffff;
}

.form {
    @apply relative font-sofiaSans;

    &-header {
        @apply flex flex-col;
        gap: 1rem;
        padding: 1.5em 35px;
    }
    &-title {
        @apply font-tomorrow font-medium uppercase;
        font-size: 1.5rem;
        line-height: 100%;
        color: #000000;
    }
    &-content {
        font-size: 1rem;
        line-height: 1.5rem;
        color: var(--secondary-off-black);
    }
    &-close {
        @apply absolute cursor-pointer;
        right: 24px;
        top: 16px;
        color: #737373;
    }

    &-body {
        padding: 0 35px;
    }
    &-field {
        &-error {
            & .form-label {
                color: #e60000;
            }
            & .form-input {
                border: 1px solid #e60000 !important;
            }
        }
    }
    &-label {
        @apply font-normal;
        font-size: 1rem;
        line-height: 1.5rem;
        margin-bottom: 5px;
        color: var(--secondary-off-black);
    }
    &-input {
        @apply font-sofiaSans;
        background: #ffffff;
        background-clip: padding-box;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        color: var(--secondary-off-black);
        font-size: 1rem;
        line-height: 1.5rem;
        padding: 0 17px;
        vertical-align: baseline;
        box-sizing: border-box;
        width: 100%;
        height: 50px;

        &:not([disabled]):focus {
            box-shadow: none;
            border: 1px solid var(--secondary-off-black);
            outline: none;
        }
    }
    &-error {
        @apply font-tomorrow font-medium;
        font-size: 12px;
        color: #e60000;
    }
    &-submit-button {
        @apply flex items-center font-tomorrow font-medium leading-none uppercase;
        padding: 15px 30px;
        margin-top: 20px;
        font-size: 1rem;
        line-height: 0.875rem;

        @screen md-max {
            padding: 15px 20px;
        }

        &::before {
            margin-right: 10px;
            width: 18px;
            display: inline-block;

            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: inherit;
            line-height: inherit;
            color: inherit;
            content: '\e91e';
            font-family: 'Scuf-Icon';
            vertical-align: top;
            display: inline-block;
            font-weight: 400;
            overflow: hidden;
            speak: none;
            text-align: center;
        }
    }

    &-footer {
        @apply text-center;
        padding: 1.5em 35px;
    }
    &-link {
        color: var(--primary);
        text-decoration: none;
    }
}
