import AccountLayout from '@components/common/AccountLayout'
import { useUser } from '@corsairitshopify/pylot-auth-manager'
import { useCountries } from '@corsairitshopify/pylot-utils'
import type {
    GetStaticProps,
    GetStaticPropsContext,
    InferGetStaticPropsType
} from 'next'
import { serverSideTranslations } from '@pylot-data/serverSideTranslations'
import { useTranslation } from 'next-i18next'
import {
    AccountHeader,
    AccountSubHeader
} from '@corsairitshopify/pylot-account-header'
import { InformationCard } from '@corsairitshopify/pylot-account-information'
import { DefaultAddress } from '@corsairitshopify/corsair-address-book'
import { useUI } from '@corsairitshopify/pylot-ui/context'
import s from '@pagestyles/Account.module.scss'
import { WelcomeMessage } from '@components/WelcomeMessage'
import getContentJson from '@pylot-data/api/operations/get-content-json'
import { CmsBlock } from '@pylot-data/pylotschema'
import { SEO } from '@corsairitshopify/corsair-seo'
import { socialDefaultContent } from '@config/seo/defaultContents'
import {
    ONE_HOUR_IN_SECONDS,
    TEN_MINUTES_IN_SECONDS
} from '@lib/utils/pageTtls'

const WELCOME_MESSAGE_IDENTIFIER = 'account-welcome-message'

export const getStaticProps: GetStaticProps = async ({
    locale
}: GetStaticPropsContext) => {
    try {
        const welcomeMessageData = await getContentJson<CmsBlock>({
            queryVariables: {
                identifier: [WELCOME_MESSAGE_IDENTIFIER],
                contentType: 'cmsBlock'
            },
            locale: locale
        })

        return {
            props: {
                welcomeMessageData,
                ...(await serverSideTranslations(locale!, [
                    'common',
                    'account'
                ]))
            },
            revalidate: ONE_HOUR_IN_SECONDS
        }
    } catch (e) {
        console.log(`Error: Generating welcome message ${e}`)
        return {
            props: {
                ...(await serverSideTranslations(locale!, [
                    'common',
                    'account'
                ]))
            },
            revalidate: TEN_MINUTES_IN_SECONDS
        }
    }
}

export default function Account({
    welcomeMessageData
}: InferGetStaticPropsType<typeof getStaticProps>): JSX.Element {
    const { loading, customer, addresses } = useUser()
    const { countryNameLocale } = useCountries()

    const { LoadingIndicator } = useUI()
    const { t } = useTranslation(['account'])

    if (loading) {
        return (
            <div className="mx-auto max-w-xs">
                <LoadingIndicator />
            </div>
        )
    }

    return (
        <>
            <SEO socialDefaultContent={socialDefaultContent} />
            <div className={s['account-container']}>
                <AccountHeader title={t('account|My Dashboard')} />
                <div className={s['account-block']}>
                    <div className="hello-sub-heading">
                        {t('Hello')} {customer?.firstname} {customer?.lastname}
                        {t(',')}
                    </div>
                    <WelcomeMessage
                        initialBlockData={welcomeMessageData}
                        blockIdentifier={WELCOME_MESSAGE_IDENTIFIER}
                    />
                </div>
                <div className={s['account-block']}>
                    <AccountSubHeader
                        subHeading={t('account|Account Information')}
                    />
                    <div className={s['account-block-grid']}>
                        <InformationCard
                            customer={customer!}
                            canEditInfo={false}
                        />
                    </div>
                </div>
                <div className={s['account-block']}>
                    <AccountSubHeader
                        link="/account/address"
                        linkText={t('account|Manage Address')}
                        subHeading={t('account|Address Book') + t(' |')}
                    />
                    <DefaultAddress
                        addresses={addresses}
                        countryNameLocale={countryNameLocale}
                        hideRemove
                    />
                </div>
            </div>
        </>
    )
}

Account.Protected = true
Account.Layout = AccountLayout
