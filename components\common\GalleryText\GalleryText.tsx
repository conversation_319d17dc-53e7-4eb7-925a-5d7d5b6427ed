import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import { CSSProperties, useMemo } from 'react'
import { CTAButton } from '../CTA'
import CarouselGallery from './CarouselGallery'
import GalleryGrid from './GalleryGrid'
import s from './GalleryText.module.scss'
import { GalleryTextInterface } from './GalleryText.types'

type GalleryTextProps = {
    contents: GalleryTextInterface
}

const GalleryText: React.FC<GalleryTextProps> = ({
    contents
}: GalleryTextProps) => {
    const {
        description,
        displayType,
        generalInformation,
        galleryImages,
        heading,
        identifier,
        textPosition,
        socialMedia,
        button
    } = contents

    const { t } = useTranslation(['common'])

    const altText = useMemo(() => {
        const customAlt = galleryImages?.[0]?.context?.custom?.alt
        return customAlt ? t(`alt|${customAlt}`) : heading || 'Product image'
    }, [galleryImages, heading, t])

    return (
        <div className={cn(s['gallery-layout'])} id={identifier}>
            <div
                className={cn(s['gallery-container'])}
                style={
                    {
                        '--block-layout':
                            textPosition == 'Image Left' ? 'row' : 'row-reverse'
                    } as CSSProperties
                }
            >
                <div className={cn(s['heading'], s['heading-mobile'])}>
                    <div className="md-max:mb-10">{heading}</div>
                </div>
                <div className={cn(s['gallery-column'], s['image-only'])}>
                    {displayType == 'Carousel' ? (
                        <CarouselGallery
                            galleryImages={galleryImages}
                            identifier={identifier}
                        />
                    ) : (
                        <GalleryGrid
                            galleryImages={galleryImages}
                            identifier={identifier}
                        />
                    )}
                </div>
                <div className={cn(s['gallery-column'], s['text-only'])}>
                    <div className={cn(s['heading'], s['heading-desktop'])}>
                        {heading}
                    </div>
                    {generalInformation && generalInformation?.length > 0 && (
                        <div className={s['general-information']}>
                            <div
                                className={cn(
                                    `grid grid-cols-2 grid-rows-auto gap-x-4`,
                                    s['general-information-container']
                                )}
                            >
                                {generalInformation.map((section, index) => {
                                    return (
                                        <div
                                            key={index}
                                            className={
                                                s['general-information-item']
                                            }
                                        >
                                            <h5>{section.title}</h5>
                                            <p>{section.description}</p>
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    )}
                    <p className={s['description']}>{description}</p>
                    {socialMedia && socialMedia.length > 0 && (
                        <div className={s['follow-section']}>
                            <h4 className={s['label']}>Follow</h4>
                            <div className={s['social-media']}>
                                {socialMedia.map((item) => (
                                    <div
                                        key={item.identifier}
                                        className={s['social-media-item']}
                                    >
                                        <Link href={item.link}>
                                            <a>
                                                <CorsairImage
                                                    keepOrigin
                                                    alt={altText}
                                                    src={convertUrlFormat(
                                                        item.icon[0].secure_url
                                                    )}
                                                    height={24}
                                                    width={24}
                                                />
                                            </a>
                                        </Link>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                    {button && (
                        <div className={s['button']}>
                            <CTAButton {...button} />
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default GalleryText
