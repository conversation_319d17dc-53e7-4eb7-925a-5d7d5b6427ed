import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import {
    Dispatch,
    FC,
    ReactNode,
    SetStateAction,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react'
import s from './Dropdown.module.scss'

interface DropdownProps {
    amountOfProducts?: number
    callback?: Dispatch<SetStateAction<number>>
    children?: ReactNode
    setCurrentPage: Dispatch<SetStateAction<number>>
    totalProducts: number
    currentPage: number
    inlineDropdownLabel?: string
}

interface ViewInterface {
    min: number
    text: string
    value: number
}

interface ListItemInterface {
    key: number
    element: ViewInterface
}

const defaultView = [
    // {
    //     value: 12,
    //     text: `12 ${t('Items')}`,
    //     min: 0,
    //     default: true
    // },
    // {
    //     value: 36,
    //     text: `36 ${t('Items')}`,
    //     min: 13
    // },
    // {
    //     value: 60,
    //     text: `60 ${t('Items')}`,
    //     min: 37
    // }
    {
        value: 9,
        text: `9`,
        min: 0
    },
    {
        value: 15,
        text: `15`,
        min: 13,
        default: true
    },
    {
        value: 30,
        text: `30`,
        min: 37
    }
]

const Dropdown: FC<DropdownProps> = ({
    amountOfProducts,
    callback,
    children,
    setCurrentPage,
    totalProducts,
    currentPage,
    inlineDropdownLabel
}) => {
    const { t } = useTranslation('plp')

    const getDefaultItemCount = () => {
        return defaultView.find((item) => item.default)?.value
    }

    const [isVisible, setIsVisible] = useState(false)
    const [activeView, setActiveView] = useState<string | number | undefined>(
        getDefaultItemCount()
    )

    const dropdownRef = useRef<HTMLDivElement>(null)
    const router = useRouter()

    const { pageSize } = router?.query

    useEffect(() => {
        pageSize
            ? setActiveView(pageSize as string)
            : setActiveView(getDefaultItemCount())
    }, [router])

    /**
     * Used to handle click outside to close dropdown options
     */
    useEffect(() => {
        /**
         * Handle click outside
         * @param event mousedown even on document
         */
        const handleClickOutside = (event: MouseEvent) => {
            const { target } = event
            const element = target as HTMLElement

            if (target instanceof HTMLElement) {
                const { current } = dropdownRef

                if (current && !current.contains(element)) setIsVisible(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)

        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [dropdownRef])

    /**
     * Handle button click
     */
    const handleButtonClick = () => setIsVisible(!isVisible)

    /**
     * Handle list click
     */
    const handleListClick = (element: ViewInterface) => {
        setActiveView(element.text)
        setIsVisible(false)
        updateUrl(element.value)
        if (setCurrentPage) setCurrentPage(1)
        if (callback) callback(element.value)
    }

    /**
     * Update url
     * @param value selected hits per page
     */
    const updateUrl = (value: number) => {
        const urlSearchParams = new URLSearchParams(window.location.search)
        const params = Object.fromEntries(urlSearchParams.entries())

        router.push({
            pathname: window.location.pathname,
            query: {
                ...params,
                page: 1,
                pageSize: value
            }
        })
    }

    /**
     * Render list item
     * @param key key from map index
     * @param element element callback from map
     * @returns {JSX Element}
     */
    const ListItem = ({ key, element }: ListItemInterface) => {
        return (
            <li key={key}>
                <button
                    className={cn(s.option, 'text-left')}
                    onClick={() => handleListClick(element)}
                    type="button"
                >
                    {element.text}
                </button>
            </li>
        )
    }

    /**
     * Render list - this useMemo function decides which option to display
     */
    const renderList = useMemo(() => {
        return defaultView.map((element: ViewInterface, key: number) => {
            if (!amountOfProducts) return

            // Corrected as commented in the ticket SH-2450
            if (totalProducts >= element.min) {
                return <ListItem key={key} element={element} />
            }
        })
    }, [ListItem, amountOfProducts, currentPage, totalProducts])

    const isExposed = isVisible ? s.isExposed : ''

    return (
        <div ref={dropdownRef} className={s.dropdown}>
            {children && <span className={s.label}>{children}</span>}
            <div className={s.wrapper}>
                <button
                    className={`${s.button} ${isExposed} text-left`}
                    type="button"
                    onClick={handleButtonClick}
                >
                    {activeView}
                </button>
                {isVisible && defaultView.length && (
                    <ul className={s.menu}>
                        {inlineDropdownLabel && (
                            <li className={s['disable-label']}>
                                {inlineDropdownLabel}
                            </li>
                        )}
                        {renderList}
                    </ul>
                )}
            </div>
            <span className={s.label}>{t('per page')}</span>
        </div>
    )
}

export default Dropdown
