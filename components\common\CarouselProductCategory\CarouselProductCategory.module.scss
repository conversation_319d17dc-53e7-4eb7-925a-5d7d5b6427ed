.header-text-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.product-category-container {
    max-width: 1760px;
    margin: 0 auto;
    padding-inline: 20px;
    padding-block: 32px;
    // padding: 0 20px 100px 20px;

    &.bp-1366 {
        max-width: 1366px;
    }
}

.product-tiles-top-container {
    @apply flex items-center justify-between;

    // @screen md {
    //     @apply py-6;
    // }

    // @screen md-max {
    //     @apply py-4;
    // }

    .header {
        text-align: left;
        text-wrap: balance;
        font-size: 1.5rem;
        line-height: 100%;
        // font-size: 18px;
        // line-height: 20px;
        margin-bottom: 24px;

        // @screen md {
        //     font-size: 18px;
        //     line-height: 20px;
        // }

        &.large-heading {
            font-size: 3.375rem;
            margin: 72px 0px;

            @screen md-max {
                margin: 40px 0px;
                font-size: 2rem;
            }
        }

        @apply font-medium text-black;
    }

    .nav-buttons {
        @apply flex gap-2;
    }

    .carousel-nav-button {
        width: 34px;
        height: 34px;
        @apply flex items-center justify-center overflow-hidden;

        :global(#Oval-2) {
            stroke: transparent;
        }

        div {
            button {
                &:focus {
                    outline: none;
                }
            }
        }

        &.prev {
            left: 3%;

            @screen md-max {
                @apply hidden;
            }

            svg {
                transform: rotateZ(180deg);
            }
        }

        &.next {
            right: 3%;

            @screen md-max {
                @apply hidden;
            }
        }

        &.disabled {
            background-color: var(--secondary-off-black) !important;
            opacity: 30%;
            cursor: unset;

            &:hover svg path {
                fill: var(--white);
                stroke: var(--white);
            }
        }
    }
}

.pagination-container {
    @apply flex items-center justify-around;
    padding-top: 1.5rem;

    &.hide-pagination {
        @apply lg:hidden;
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: transparent;
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }

    .default-dot {
        background-color: var(--dark-bg-btn-primary);
    }
}

.slider-item-container {
    @apply flex flex-col;
    height: 100%;

    @screen xl-1280 {
        max-width: 428px;
    }

    .slider-item-text-block {
        padding: 1.2rem 1.2rem;

        &.noHorizonPadding {
            padding: 1.2rem 0;
        }

        @media screen and (min-width: 1100px) and (max-width: 1500px) {
            padding: 20px 18px;

            &.noHorizonPadding {
                padding: 1.2rem 0;
            }
        }

        @media screen and (min-width: 800px) and (max-width: 1100px) {
            padding: 16px;

            &.noHorizonPadding {
                padding: 1.2rem 0;
            }
        }

        @screen md-max {
            @apply pt-7 px-4 pb-12;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            flex-grow: 1;

            &.noHorizonPadding {
                @apply px-0;
            }
        }

        .slider-item-heading {
            @apply font-primary font-medium;
            font-size: 18px;
            line-height: 20px;
            margin-bottom: 12px;

            @media screen and (max-width: 1860px) {
                font-size: 1.5rem;
                line-height: 1.75rem;
            }

            @media screen and (max-width: 1500px) {
                font-size: 1.25rem;
                line-height: 1.5rem;
            }

            @media screen and (min-width: 1100px) and (max-width: 1500px) {
                font-size: 1.125rem;
                line-height: 1.375rem;
            }

            @screen md-max {
                font-size: 22px;
                line-height: 100%;
                letter-spacing: 0%;
            }
        }

        .slider-item-display-text {
            font-size: 16px;
            line-height: 20.26px;
            @apply font-bold;
        }

        .slider-item-cta {
            display: flex !important;
            align-items: center !important;

            .slider-icon {
                margin-left: 8px;
            }
        }

        .slider-item-cta-text {
            @apply font-primary font-medium;
            font-size: 16px;
            line-height: 20.26px;
        }

        h5 {
            @apply font-bold uppercase font-sofiaSans #{!important};
            letter-spacing: 0;
            font-size: 18px;
            line-height: 20px;
        }

        p {
            @apply font-normal uppercase font-sofiaSans #{!important};
            line-height: 16px;
            font-size: 12px;
        }
    }

    .slider-item-image-container {
        @screen md {
            height: 318px;
        }

        @screen lg-1181 {
            &.lg-img {
                height: 275px;
            }
        }

        @screen xl-1440 {
            &.lg-img {
                height: 428px;
            }
        }

        @screen md-max {
            &.lg-img {
                height: 306px;
            }

            height: 200px;
        }

        img {
            background-color: var(--img-bg-color);
        }
    }

    // Variant for article
    &.article-item {
        display: flex;
        flex-direction: column;
        width: 100%;
        cursor: pointer;

        .article-link {
            display: block;
            text-decoration: none;
        }

        .article-link {
            display: block;
            text-decoration: none;
        }

        .slider-item-image-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; // 16:9 aspect ratio
            overflow: hidden;
        }
    }

    &.show-animation {
        transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

        &:hover {
            transform: translateY(-5px);

            .slider-item-image-container img {
                transform: scale(1.05);
            }
        }

        .slider-item-image-container {
            img {
                transition: transform 0.45s cubic-bezier(0.2, 0, 0.2, 1);
                transform-origin: center center;
            }
        }
    }
}

.product-tiles-swiper-container {
    @apply relative;

    :global {
        .swiper-slide {
            height: auto;
        }
    }

    .carousel-nav-button {
        transform: translateY(-50%);
        width: 52px;
        height: 48px;
        @apply z-10 top-2/5 absolute flex items-center justify-center overflow-hidden;

        :global(#Oval-2) {
            stroke: transparent;
        }

        div {
            button {
                &:focus {
                    outline: none;
                }
            }
        }

        &.prev {
            left: 3%;

            @screen md-max {
                @apply hidden;
            }

            svg {
                transform: rotateZ(180deg);
            }
        }

        &.next {
            right: 3%;
        }
    }
}

.product-tiles-swiper-container {
    // .faded-right,
    // .faded-left {
    //     @apply h-full absolute top-0 block z-2;
    //     background: #0f0f0f;
    //     opacity: 0.4;
    //     width: 4.8%;

    //     @screen md-max {
    //         width: 10%;
    //     }
    // }

    // .faded-right {
    //     right: -1px;
    //     background: #0f0f0f;
    // }

    // .faded-left {
    //     @apply md-max:hidden;

    //     &.is-last-item {
    //         @apply md-max:block;
    //     }
    // }
    .faded-left {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to left, var(--gradient-stops));
        @apply absolute top-0 left-0 h-full w-1/12 z-2;
    }

    .faded-right {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to right, var(--gradient-stops));
        @apply absolute top-0 right-0 h-full w-1/12 z-2;
    }

    @screen md-max {

        .faded-left,
        .faded-right {
            display: none;
        }
    }
}