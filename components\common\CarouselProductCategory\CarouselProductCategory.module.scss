.pagination-container {
    @apply flex items-center justify-around;
    padding-top: 1.5rem;

    &.hide-pagination {
        @apply lg:hidden;
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: transparent;
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }

    .default-dot {
        background-color: var(--dark-bg-btn-primary);
    }
}

.slider-item-container {
    @apply flex flex-col h-full;

    .slider-item-text-block {
        @apply flex flex-col;
        row-gap: 12px;
        padding: 24px;

        .slider-item-heading {
            @apply font-primary font-medium;
            font-size: 1.5rem;
            line-height: 1.75rem;
        }

        .slider-item-description {
            @apply font-normal font-sofiaSans;
            font-size: 1rem;
            line-height: 1.5rem;
        }

        .slider-item-cta {
            display: flex !important;
            align-items: center !important;

            .slider-icon {
                margin-left: 8px;
            }
        }

        .slider-item-cta-text {
            @apply font-primary font-medium;
            font-size: 16px;
            line-height: 20.26px;
        }
    }

    .slider-item-image-container {
        @screen md {
            height: 318px;
        }

        @screen md-max {
            height: 200px;
        }

        img {
            background-color: var(--img-bg-color);
        }
    }
}
