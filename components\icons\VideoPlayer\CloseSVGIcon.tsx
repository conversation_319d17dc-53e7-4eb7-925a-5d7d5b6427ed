import { FC, HTMLAttributes } from 'react'

interface CloseSVGIconProps extends HTMLAttributes<SVGElement> {
    width?: string | number
    height?: string | number
    color?: string
}

export const CloseSVGIcon: FC<CloseSVGIconProps> = ({
    width = 15,
    height = 16,
    color = '#000000',
    ...props
}) => (
    <svg
        width={width}
        height={height}
        viewBox="0 0 15 16"
        fill={color}
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            d="M13.4629 2.01392L1.57874 13.9861"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="square"
        />
        <path
            d="M1.5791 2.01392L13.4632 13.9861"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="square"
        />
    </svg>
)
