/* eslint-disable @typescript-eslint/ban-types */
import { useBaseUrl } from '@config/hooks/useBaseUrl'
import { useStoreConfig } from '@config/hooks/useStoreConfig'
import { ArrowRight } from '@corsairitshopify/corsair-icons'
import CorsairImage from '@corsairitshopify/corsair-image'
import s from '@styles/common/navigation/MegaMenu.module.scss'
import cn from 'classnames'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import Link from 'next/link'
import { FC, useEffect, useRef, useState } from 'react'
import { MenuItemsType } from '../MegaMenu.d'
import { Steps, useMenuState } from '../MegaMenuContext'
import { NavigateBack } from '../MenuToolbar'
import { yellow } from '../utils/colors'
import { urlResolver } from '../utils/urlResolver'
//@ts-ignore
import { useTranslation } from 'next-i18next'
export type MenuSecondaryProps = {
    menuItems: Array<MenuItemsType>
    title: string
    columnsAmount: number
    activeMenuItem: number | null
    rootActive: boolean
    handleNavigateBack: () => void
    handleClickNavigateItem: () => void
    focusable: number | undefined
    handleHideSubMenu?: () => void
}

const MIN_COLUMNS = 1
const MAX_COLUMNS = 12

const DEFAULT_IMAGE_SIZES = {
    width: 72,
    height: 49
}

export const MenuSecondary: FC<MenuSecondaryProps> = ({
    columnsAmount,
    menuItems,
    title,
    activeMenuItem,
    rootActive,
    focusable,
    handleNavigateBack,
    handleHideSubMenu
}) => {
    const { showPushMenu, step } = useMenuState()
    const [activeSubMenuItem, setActiveSubMenuItem] = useState<number | null>(
        null
    )
    const menuItemsContainerRef = useRef<null | HTMLDivElement>(null)
    const { t } = useTranslation(['common'])
    const [startMenuAnimation, setStartMenuAnimation] = useState<boolean>(false)

    const hideSubmenu = () => {
        handleHideSubMenu?.()
    }

    const menuItemEvents = {
        onClick: hideSubmenu
    }

    useEffect(() => {
        setStartMenuAnimation(false)
        let timerHideContent: number
        if ((activeMenuItem || activeMenuItem === 0) && rootActive) {
            timerHideContent = window.setTimeout(() => {
                setStartMenuAnimation(true)
            }, 0)
        }
        return () => {
            clearTimeout(timerHideContent)
        }
    }, [rootActive, activeMenuItem])
    if (!menuItems?.length) return null
    const renderItems = menuItems?.map((menuItem, index) => {
        menuItem.url = `${menuItem?.url.startsWith('/') ? '' : '/'}${
            menuItem.url
        }`
        const {
            title,
            subTitle,
            url: hrefUrl,
            target,
            cloudinaryImage,
            submenuitems
        } = menuItem
        const hasChildren = !!submenuitems?.length
        const urlTarget = target === 'Blank' ? '_blank' : '_self'

        const onClickHandler = (index: number, hasChildren: boolean) => {
            const clickedTitle = title.toLowerCase().replace(/[\s&]/g, '')
            const childElements = document.getElementsByClassName(clickedTitle)
            const arrowToggles = document.querySelectorAll(
                `.${clickedTitle}-icon`
            ) as NodeListOf<HTMLElement>

            if (childElements && arrowToggles) {
                Array.from(childElements).forEach((dropdown) => {
                    dropdown.classList.toggle('hidden')
                    const expanded =
                        dropdown.getAttribute('aria-expanded') === 'true'
                    dropdown.setAttribute('aria-expanded', String(!expanded))
                })
                arrowToggles.forEach((arrowToggle) => {
                    arrowToggle.classList.toggle('-rotate-90')
                })
            }
            showPushMenu(false)
        }
        return (
            <li
                className={cn(
                    s['menu-item-container'],
                    activeSubMenuItem === index && s['active-menu-item']
                )}
                key={index}
            >
                <div className={s['desktop-visible']}>
                    <div className={s['menu-item-block']}>
                        <div
                            className={s['menu-item-block-image']}
                            {...menuItemEvents}
                        >
                            <Link href={hrefUrl}>
                                <a
                                    rel="noopener noreferrer"
                                    target={urlTarget}
                                    tabIndex={focusable}
                                    aria-label={
                                        cloudinaryImage?.[0]?.context?.custom
                                            ?.alt || ''
                                    }
                                >
                                    {cloudinaryImage?.[0]?.secure_url && (
                                        <CorsairImage
                                            src={convertUrlFormat(
                                                cloudinaryImage[0].secure_url
                                            )}
                                            width={
                                                cloudinaryImage?.[0]?.width ||
                                                DEFAULT_IMAGE_SIZES.width
                                            }
                                            height={
                                                cloudinaryImage?.[0]?.height ||
                                                DEFAULT_IMAGE_SIZES.height
                                            }
                                            alt={
                                                cloudinaryImage?.[0]?.context
                                                    ?.custom?.alt || ''
                                            }
                                        />
                                    )}
                                </a>
                            </Link>
                        </div>
                    </div>
                    <div className={s['menu-sub-menu-container']}>
                        <span
                            className={cn([
                                s['sub-nav-item-text'],
                                hasChildren && s['has-children']
                            ])}
                            {...menuItemEvents}
                        >
                            <Link href={hrefUrl}>
                                <a
                                    rel="noopener noreferrer"
                                    target={urlTarget}
                                    tabIndex={focusable}
                                >
                                    {title}
                                    {subTitle && (
                                        <div
                                            className={`${s['sub-nav-item-text--sub-title']}`}
                                        >
                                            {subTitle}
                                        </div>
                                    )}
                                </a>
                            </Link>
                        </span>
                        {submenuitems && (
                            <div className="flex flex-col">
                                {submenuitems.map((item, index) => (
                                    <a
                                        key={index}
                                        href={item.url}
                                        className="py-1"
                                        tabIndex={focusable}
                                        rel="noreferrer"
                                        target={
                                            item.openInANewTab
                                                ? '_blank'
                                                : '_self'
                                        }
                                    >
                                        {item.title}
                                    </a>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
                <div className={s['mobile-visible']}>
                    <div
                        className={s['menu-sub-menu-container']}
                        role="button"
                        tabIndex={focusable}
                        onClick={() => {
                            onClickHandler(index, hasChildren)
                        }}
                        onKeyPress={() => {
                            onClickHandler(index, hasChildren)
                        }}
                    >
                        <span
                            className={cn([
                                s['sub-nav-item-text'],
                                hasChildren && s['has-children']
                            ])}
                        >
                            <Link href={hrefUrl}>
                                <a
                                    rel="noopener noreferrer"
                                    target={urlTarget}
                                    tabIndex={focusable}
                                >
                                    <img
                                        className={s['menu-sub-nav-img']}
                                        src={cloudinaryImage?.[0].secure_url}
                                        alt={t(
                                            cloudinaryImage?.[0].context?.custom
                                                ?.alt || ''
                                        )}
                                    />
                                    <p className="flex items-end leading-5 gap-2">
                                        {title}
                                        {subTitle && (
                                            <p
                                                className={
                                                    s[
                                                        'sub-nav-item-text--sub-title'
                                                    ]
                                                }
                                            >
                                                {subTitle}
                                            </p>
                                        )}
                                    </p>
                                </a>
                            </Link>
                            {hasChildren && (
                                <ArrowRight
                                    className={cn(
                                        s['sub-nav-item-icon'],
                                        'transform rotate-90',
                                        `${title
                                            .toLowerCase()
                                            .replace(/\s/g, '')}-icon`
                                    )}
                                    fill={yellow}
                                />
                            )}
                        </span>
                        {submenuitems && (
                            <div
                                className={cn(
                                    s['submenu-container'],
                                    'flex flex-col pl-10 hidden',
                                    title.toLowerCase().replace(/[\s&]/g, '')
                                )}
                                aria-expanded="false"
                            >
                                {submenuitems.map((item, index) => (
                                    <a
                                        key={index}
                                        href={item.url}
                                        className="py-1"
                                        tabIndex={focusable}
                                        rel="noreferrer"
                                        target={
                                            item.openInANewTab
                                                ? '_blank'
                                                : '_self'
                                        }
                                    >
                                        {item.title}
                                    </a>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </li>
        )
    })

    return (
        <div
            ref={menuItemsContainerRef}
            className={cn(
                s['menu-items-container'],
                s['secondary-menu-container'],
                startMenuAnimation && s['menu-items-preanim'],
                startMenuAnimation && s['menu-items-animation'],
                step === Steps.Tertiary && s['no-scroll']
            )}
        >
            <NavigateBack
                handleNavigateBack={handleNavigateBack}
                title={title}
                focusable={focusable}
            />
            <ul
                className={cn(
                    s['menu-items'],
                    menuItems?.length === columnsAmount
                        ? s['menu-items-flexbox']
                        : columnsAmount &&
                              columnsAmount >= MIN_COLUMNS &&
                              columnsAmount <= MAX_COLUMNS &&
                              s[`columns-amount-${columnsAmount}`]
                )}
            >
                {renderItems}
            </ul>
            <button
                className={cn('sr-only', s['mobile-visible'])}
                onFocus={handleNavigateBack}
                tabIndex={focusable}
            />
        </div>
    )
}
