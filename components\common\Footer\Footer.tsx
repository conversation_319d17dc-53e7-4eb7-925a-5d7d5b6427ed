import s from '@commonStyles/footer/Footer.module.scss'
import {
    Facebook,
    Instagram,
    Twitch,
    Twitter,
    Youtube
} from '@components/icons/Social'
import { useBaseUrl } from '@config/hooks/useBaseUrl'
import { useStoreConfig } from '@config/index'
import {
    CTAType,
    useContentJson
} from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, ReactNode, useState } from 'react'
import { Newsletter } from '../Newsletter'
import { ColumnType, FooterData, SocialLinkType } from './Footer.types'

const CONTENT_IDENTIFIER = ['footer']
const CONTENT_TYPE = 'footer'
const urlPathSlash = '/'
const urlFullFormatHttps = 'https://'
const firstElementPosition = 0
const rootPathLastElementPosition = 3

const getNewPathIncludeRegionLanguage = (rootPath: string, oldPath: string) => {
    if (oldPath?.includes(urlFullFormatHttps)) {
        return oldPath
    } else if (oldPath?.indexOf(urlPathSlash) !== firstElementPosition) {
        return `${rootPath}${urlPathSlash}${oldPath}`
    } else {
        return `${rootPath}${oldPath}`
    }
}

const mappedIcon: Record<string, ReactNode> = {
    facebook: <Facebook width={30} height={30} />,
    instagram: <Instagram width={30} height={30} />,
    twitter: <Twitter width={30} height={30} />,
    twitch: <Twitch width={30} height={30} />,
    youtube: <Youtube width={30} height={30} />
}

const SocialLinks: FC<{ sociallinks: SocialLinkType[] }> = ({
    sociallinks
}): JSX.Element => {
    const { t } = useTranslation(['common'])

    return (
        <div className={s['social-section']}>
            <h3 className={s['section-title']}>{t('footer|Connect')}</h3>
            <div className={s['social']}>
                {sociallinks?.length > 0 &&
                    sociallinks.map((logo: SocialLinkType) => (
                        <a
                            key={logo.title}
                            title={logo.title}
                            href={logo.url}
                            target={logo.newTab ? '_blank' : '_self'}
                            rel="noreferrer"
                            aria-label={logo.title}
                        >
                            {mappedIcon[logo.title] ?? null}
                        </a>
                    ))}
            </div>
        </div>
    )
}

const Columns: FC<{ columns?: ColumnType[]; regionLanguagePath: string }> = ({
    columns,
    regionLanguagePath
}): JSX.Element => (
    <div className={s['links-cols']}>
        {columns?.length &&
            columns.map((column: ColumnType) => {
                return (
                    column.type === 'Footer Column' && (
                        <div className={s['column']} key={column.title}>
                            <h3 className={s['column-heading']}>
                                {column.title}
                            </h3>
                            {column.items.map((urlData: CTAType) => {
                                return (
                                    <a
                                        key={urlData.displayText}
                                        href={getNewPathIncludeRegionLanguage(
                                            regionLanguagePath,
                                            urlData.url
                                        )}
                                        title={urlData.displayText}
                                        className={s['column-link']}
                                        aria-label={urlData.displayText}
                                    >
                                        {urlData.displayText}
                                    </a>
                                )
                            })}
                        </div>
                    )
                )
            })}
    </div>
)

const CopyrightAndLinks: FC<{
    copyrightText: string
    copyrightLinks?: CTAType[]
    region: string
    language: string
}> = ({ copyrightText }): JSX.Element => (
    <span className={s['copyright-text']}>{copyrightText}</span>
)

const Footer = (): JSX.Element | null => {
    const { host = null } = typeof window !== 'undefined' ? window.location : {}
    const storeConfig = useStoreConfig()
    const baseUrl =
        process.env.NEXT_ENV === 'localhost' || host?.includes('localhost')
            ? storeConfig.base.url.localHostBaseUrl
            : storeConfig.base.url.baseUrl

    const { region, language } = useBaseUrl(baseUrl)
    const [regionLanguagePath, setRegionLanguagePath] = useState('')

    const getRootPath = (originalPath: string) => {
        const rootPathArray = originalPath.split(urlPathSlash)
        const rootPathArrayRegionLanguage = rootPathArray.slice(
            firstElementPosition,
            rootPathLastElementPosition
        )
        return rootPathArrayRegionLanguage.join(urlPathSlash)
    }

    const { data } = useContentJson<FooterData>(
        {
            identifier: CONTENT_IDENTIFIER,
            contentType: CONTENT_TYPE,
            options: {
                level: 3
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )

    if (!data) {
        return null
    }

    const {
        columns,
        copyrightLinks,
        copyrightText,
        sociallinks
    } = data[0].parsedEntries

    // eslint-disable-next-line react-hooks/rules-of-hooks
    React.useEffect(() => {
        setRegionLanguagePath(getRootPath(window.location.pathname))
    }, [])
    return (
        <footer className={s.footer}>
            <div>
                <div className={s['footer-container']}>
                    <div className={s['grid-rows']}>
                        <Columns
                            columns={columns}
                            regionLanguagePath={regionLanguagePath}
                        />
                        <div className={s['news-social-container']}>
                            <Newsletter />
                            <SocialLinks sociallinks={sociallinks} />
                        </div>
                    </div>
                </div>
            </div>

            <div className={cn(s['footer-copyright-block-wrapper'])}>
                <div className={cn(s['footer-copyright-block'])}>
                    <div className={s['copyright']}>
                        <div className={s['links']}>
                            <CopyrightAndLinks
                                copyrightText={copyrightText}
                                copyrightLinks={copyrightLinks}
                                region={region}
                                language={language}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    )
}

export default Footer
