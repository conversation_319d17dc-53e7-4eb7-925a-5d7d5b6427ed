import dynamic from 'next/dynamic'
import React from 'react'
import cn from 'classnames'

import { CarouselWrapperProps } from './CarouselWrapper.types'

import CarouselPillarsItem from '../CarouselPillarsItem/CarouselPillarsItem'
import CarouselProductCategoryItem from '../CarouselProductCategory/CarouselProductCategoryItem'
import CarouselStickyBanner from './CarouselStickyBanner'
import CarouselProductCustomizeItem from '../CarouselProductCustomize/CarouselProductCustomizeItem'
import CarouselProductTilesItem from '../CarouselProductTilesItem/CarouselProductTilesItem'
import CarouselProductGalleryItem from '../CarouselProductGallery/CarouselProductGalleryItem'

import s from './CarouselWrapper.module.scss'

const CarouselSlider = dynamic(() => import('./CarouselSlider'), { ssr: false })
const CarouselImageText = dynamic(
    () => import('@components/common/CarouselImageText'),
    { ssr: false }
)
const CarouselPillarsDoubleImage = dynamic(
    () =>
        import(
            '@components/common/CarouselPillarsDoubleImage/CarouselPillarsDoubleImage'
        ),
    { ssr: false }
)
const CarouselArticleItem = dynamic(
    () => import('@components/common/CarouselArticle/CarouselArticleItem'),
    { ssr: false }
)

const CarouselWrapper: React.FC<CarouselWrapperProps> = ({
    content
}: CarouselWrapperProps) => {
    const Element = () => {
        switch (content.displayType) {
            case 'products': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselProductTilesItem product={prod} />
                        )}
                    />
                )
            }
            case 'pillars': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselPillarsItem product={prod} />
                        )}
                    />
                )
            }
            case 'pillars/double-image': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselPillarsDoubleImage product={prod} />
                        )}
                        // overrideCarouselConfig={{
                        //     slidesPerView: 1,
                        //     breakpoints: {
                        //         768: {
                        //             slidesPerView: 2.2,
                        //             slidesPerGroup: 1,
                        //             spaceBetween: 20
                        //         }
                        //     }
                        // }}
                    />
                )
            }
            case 'products/category': {
                // return <CarouselProductCategory content={content} />
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselProductCategoryItem product={prod} />
                        )}
                    />
                )
            }
            case 'articles': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselArticleItem product={prod} />
                        )}
                    />
                )
            }
            case 'image/text': {
                return <CarouselImageText content={content} />
            }
            case 'image/text/cta': {
                return <CarouselImageText content={content} variants="cta" />
            }
            case 'gallery': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselProductGalleryItem product={prod} />
                        )}
                    />
                )
            }

            case 'products/customize': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselProductCustomizeItem product={prod} />
                        )}
                    />
                )
            }

            case 'list-card': {
                return (
                    <CarouselSlider
                        content={content}
                        renderSlideItem={(prod) => (
                            <CarouselProductCustomizeItem product={prod} />
                        )}
                    />
                )
            }

            default:
                return null
        }
    }

    const headingType = content?.headingType || 'h1'

    const Heading =
        !!content?.heading &&
        React.createElement(
            headingType,
            {
                className: cn(
                    s['heading'],
                    s[`heading-${headingType.toLowerCase()}`]
                ),
                style: {
                    color: content?.headingColor
                }
            },
            content?.heading
        )

    return (
        <>
            {content?.showStickyBanner && (
                <CarouselStickyBanner
                    identifier={content?.identifier}
                    label={content?.stickyBannerLabel}
                    buttonLabel={content?.stickyBannerBtnLabel}
                />
            )}
            <div
                id={content?.identifier}
                className={s['carousel-container']}
                style={getContainerStyle(content)}
            >
                {!!Heading && (
                    <div
                        className={cn(
                            s['carousel-top-container'],
                            'carousel-top-container'
                        )}
                    >
                        {Heading}
                    </div>
                )}
                <Element />
            </div>
        </>
    )
}

export default CarouselWrapper

const paddingPresets = {
    none: {
        desktopPaddingTop: 0,
        desktopPaddingBottom: 0,
        mobilePaddingTop: 0,
        mobilePaddingBottom: 0
    },
    tiny: {
        desktopPaddingTop: '2.25rem',
        desktopPaddingBottom: '2.5rem',
        mobilePaddingTop: '1rem',
        mobilePaddingBottom: '1rem'
    },
    small: {
        desktopPaddingTop: '3.25rem',
        desktopPaddingBottom: '3.5rem',
        mobilePaddingTop: '1.5rem',
        mobilePaddingBottom: '1.5rem'
    },
    medium: {
        desktopPaddingTop: '4rem',
        desktopPaddingBottom: '4.25rem',
        mobilePaddingTop: '2rem',
        mobilePaddingBottom: '2rem'
    },
    large: {
        desktopPaddingTop: '8rem',
        desktopPaddingBottom: '8.25rem',
        mobilePaddingTop: '3rem',
        mobilePaddingBottom: '3rem'
    }
}

const getContainerStyle = (
    content: CarouselWrapperProps['content']
): React.CSSProperties => {
    const desktopBgUrl = content.cloudinaryBackgroundImage?.[0]?.secure_url
    const mobileBgUrl =
        content.cloudinaryMobileBackgroundImage?.[0]?.secure_url || desktopBgUrl
    const desktopPadding =
        paddingPresets[content.padding || 'none'] || paddingPresets.none

    return {
        '--bg-color': content.backgroundColor || '#333132',
        '--desktop-bg-image': desktopBgUrl ? `url(${desktopBgUrl})` : undefined,
        '--mobile-bg-image': mobileBgUrl ? `url(${mobileBgUrl})` : undefined,
        '--desktop-pt': desktopPadding.desktopPaddingTop,
        '--desktop-pb': desktopPadding.desktopPaddingBottom,
        '--mobile-pt': desktopPadding.mobilePaddingTop,
        '--mobile-pb': desktopPadding.mobilePaddingBottom
    } as React.CSSProperties
}
