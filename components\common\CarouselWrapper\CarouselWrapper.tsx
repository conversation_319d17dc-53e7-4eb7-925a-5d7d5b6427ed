import dynamic from 'next/dynamic'
import { useMemo } from 'react'
import cn from 'classnames'

import CarouselPillarsItem from '../CarouselPillarsItem/CarouselPillarsItem'
import { CarouselWrapperProps } from './CarouselWrapper.types'

import sPillarsDoubleImage from '../CarouselProductTiles/CarouselProductTiles.module.scss'
import { useCheckScrollEnd } from 'hooks/useCheckScrollEnd'
import { createPortal } from 'preact/compat'
import { CTA } from '../CTA'
import { ATC_HEADER_ID } from '../Layout/Layout'
import s from './CarouselWrapper.module.scss'

const CarouselProductTiles = dynamic(
    () => import('@components/common/CarouselProductTiles'),
    { ssr: false }
)
const CarouselProductGallery = dynamic(
    () => import('@components/common/CarouselProductGallery'),
    { ssr: false }
)
const CarouselImageText = dynamic(
    () => import('@components/common/CarouselImageText'),
    { ssr: false }
)
const CarouselImageTextCTA = dynamic(
    () => import('@components/common/CarouselImageTextCTA'),
    { ssr: false }
)
const CarouselProductCategory = dynamic(
    () => import('@components/common/CarouselProductCategory'),
    { ssr: false }
)
const CarouselProductCustomize = dynamic(
    () => import('@components/common/CarouselProductCustomize'),
    { ssr: false }
)
const CarouselPillarsDoubleImage = dynamic(
    () =>
        import(
            '@components/common/CarouselPillarsDoubleImage/CarouselPillarsDoubleImage'
        ),
    { ssr: false }
)
const CarouselArticleItem = dynamic(
    () => import('@components/common/CarouselArticle/CarouselArticleItem'),
    { ssr: false }
)

const CarouselWrapper: React.FC<CarouselWrapperProps> = ({
    content
}: CarouselWrapperProps) => {
    const { isScrollEndOfBlock } = useCheckScrollEnd(
        content?.identifier ? `#${content?.identifier}` : null
    )

    const paddingPresets: Record<
        string,
        Partial<CarouselWrapperProps['content']>
    > = {
        none: {
            desktopPaddingTop: '0',
            desktopPaddingBottom: '0',
            mobilePaddingTop: '0',
            mobilePaddingBottom: '0'
        },
        tiny: {
            desktopPaddingTop: '2.25',
            desktopPaddingBottom: '2.5',
            mobilePaddingTop: '1',
            mobilePaddingBottom: '2'
        },
        small: {
            desktopPaddingTop: '3.25',
            desktopPaddingBottom: '3.5',
            mobilePaddingTop: '1.5',
            mobilePaddingBottom: '3'
        },
        medium: {
            desktopPaddingTop: '4',
            desktopPaddingBottom: '4.25',
            mobilePaddingTop: '2',
            mobilePaddingBottom: '4'
        },
        large: {
            desktopPaddingTop: '8',
            desktopPaddingBottom: '8.25',
            mobilePaddingTop: '3',
            mobilePaddingBottom: '6'
        }
    }

    const normalizedContent = {
        ...content,
        ...(paddingPresets[content.padding] ?? paddingPresets.none)
    }

    const Element = () => {
        switch (content.displayType) {
            case 'products': {
                return <CarouselProductTiles content={normalizedContent} />
            }
            case 'image/text/cta': {
                return <CarouselImageTextCTA content={normalizedContent} />
            }
            case 'image/text': {
                return <CarouselImageText content={normalizedContent} />
            }
            case 'gallery': {
                return <CarouselProductGallery content={normalizedContent} />
            }
            case 'pillars': {
                return (
                    <CarouselProductTiles
                        content={normalizedContent}
                        renderSlideItem={(prod) => (
                            <CarouselPillarsItem product={prod} />
                        )}
                        overrideCarouselConfig={{
                            breakpoints: {
                                500: {
                                    slidesPerView: 2.2,
                                    slidesPerGroup: 1
                                },
                                1024: {
                                    slidesPerView: 3.2,
                                    slidesPerGroup: 1,
                                    spaceBetween: 20
                                },
                                1440: {
                                    slidesPerView: 4.2,
                                    slidesPerGroup: 1,
                                    spaceBetween: 20
                                }
                            }
                        }}
                    />
                )
            }
            case 'pillars/double-image': {
                return (
                    <CarouselProductTiles
                        content={normalizedContent}
                        renderSlideItem={(prod) => (
                            <CarouselPillarsDoubleImage product={prod} />
                        )}
                        overrideCarouselConfig={{
                            slidesPerView: 1,
                            breakpoints: {
                                768: {
                                    slidesPerView: 2.2,
                                    slidesPerGroup: 1,
                                    spaceBetween: 20
                                }
                            }
                        }}
                        classNames={[sPillarsDoubleImage['double-image']]}
                    />
                )
            }
            case 'products/category': {
                return <CarouselProductCategory content={normalizedContent} />
            }
            case 'products/customize': {
                return <CarouselProductCustomize content={normalizedContent} />
            }
            case 'articles': {
                return (
                    <CarouselProductTiles
                        content={normalizedContent}
                        renderSlideItem={(prod) => (
                            <CarouselArticleItem product={prod} />
                        )}
                        overrideCarouselConfig={{
                            slidesPerView: 1.7,
                            breakpoints: {
                                1280: {
                                    slidesPerView: 4,
                                    slidesPerGroup: 1,
                                    spaceBetween: 20
                                }
                            }
                        }}
                    />
                )
            }
            default:
                return null
        }
    }

    const StickyBanner = useMemo(() => {
        const element =
            (content?.showStickyBanner &&
                global?.document &&
                createPortal(
                    <div
                        data-visible={isScrollEndOfBlock}
                        className={cn(s['sticky-header'])}
                    >
                        <div
                            className={cn(
                                'scuf-container',
                                s['sticky-header--content']
                            )}
                        >
                            {!!content?.stickyBannerLabel && (
                                <div
                                    className={cn(s['sticky-header--label'])}
                                    aria-label={content.stickyBannerLabel}
                                >
                                    {content.stickyBannerLabel}
                                </div>
                            )}
                            {!!content?.stickyBannerBtnLabel && (
                                <CTA
                                    className={cn(
                                        'scuf-button-primary',
                                        s['sticky-header--button']
                                    )}
                                    cta={{
                                        displayText:
                                            content.stickyBannerBtnLabel,
                                        openInANewTab: false,
                                        url: `#${content?.identifier}`,
                                        buttonType: 'primary'
                                    }}
                                />
                            )}
                        </div>
                    </div>,
                    document.querySelector(`#${ATC_HEADER_ID}`)!
                )) ||
            null
        return element
    }, [
        content?.identifier,
        content?.showStickyBanner,
        content.stickyBannerBtnLabel,
        content?.stickyBannerLabel,
        isScrollEndOfBlock
    ])

    return (
        <>
            {StickyBanner}
            <Element />
        </>
    )
}

export default CarouselWrapper
