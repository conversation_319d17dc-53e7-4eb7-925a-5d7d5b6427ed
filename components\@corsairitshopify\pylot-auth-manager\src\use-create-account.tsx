import { graphqlFetch } from '@pylot-data/graphqlFetch'
import type { CustomerInput, CustomerOutput } from '@pylot-data/fwrdschema'
import { CREATE_CUSTOMER } from './graphql/createCustomer'
import { useLogin } from './use-login'
import { useRouter } from 'next/router'
import { useAuthUI } from './AuthContext'

export type CreateCustomerResponse = {
    data: { createCustomer: CustomerOutput }
}

export const useCreateAccount = () => {
    const { login } = useLogin()
    const { locale } = useRouter()
    const { signUpCallbacks, clearAuthCallback } = useAuthUI()

    return {
        createAccount: async (params: CustomerInput) => {
            const createResult = await graphqlFetch<
                { input: CustomerInput },
                CreateCustomerResponse
            >({ query: CREATE_CUSTOMER, variables: { input: params }, locale })
            if (createResult.errors) throw createResult.errors
            await login({
                email: params.email!,
                password: params.password!
            })
            if (signUpCallbacks.length) {
                signUpCallbacks.forEach((callback) => {
                    if (typeof callback === 'function') {
                        callback()
                    }
                })
                clearAuthCallback('SIGNUP_VIEW')
            }
            return { createResult }
        }
    }
}
