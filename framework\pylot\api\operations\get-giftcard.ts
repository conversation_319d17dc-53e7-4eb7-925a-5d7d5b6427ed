import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import {
    OutputInterface,
    ProductVariablesInterface
} from '@pylot-data/api/operations/get-product'
// import { SearchCriteriaFilterActions } from '@corratech/corsair-filters-and-sort/src/index'
import { graphqlFetch } from '@pylot-data/graphqlFetch'
import { HeadersInit } from 'node-fetch'
import { SearchCriteriaFilterActions } from '@corsairitshopify/pylot-filters-and-sort'
import { giftcardQuery } from '@pylot-data/hooks/giftcard/graphql/giftcardQuery'

export const getGiftcard = async (
    locale: string
): Promise<ConfigurableProduct | SimpleProduct> => {
    const fetchOptions = {
        headers: ({
            'Cache-Control': 'private, no-store, max-age=0'
        } as unknown) as HeadersInit
    }
    const productData = await graphqlFetch<
        ProductVariablesInterface,
        OutputInterface
    >({
        query: giftcardQuery,
        variables: {
            searchCriteria: [
                {
                    attribute_code: 'url_key',
                    filter_action: SearchCriteriaFilterActions.EQ,
                    filter_value: 'gift-card-usd'
                }
            ]
        },
        fetchOptions,
        locale,
        disableCache: true,
        overridePylotQuery: 'products'
    })
    // @ts-ignore
    const products = productData?.data?.getGiftcard as
        | ConfigurableProduct
        | SimpleProduct

    return products
}

export default getGiftcard
