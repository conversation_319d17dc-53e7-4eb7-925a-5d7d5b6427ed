import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import s from './CarouselProductTiles.module.scss'
interface SlideProps {
    cslData: any
    key: number
}

const Slide = ({ cslData, key }: SlideProps): React.ReactElement => {
    const { t } = useTranslation(['common'])
    return (
        <div className={s['csl-wrapper']} key={key}>
            <div className={s['header-text-container']}>
                {cslData?.cloudinaryLogo?.[0]?.secure_url && (
                    <div className={s['logo-container']}>
                        <CorsairImage
                            keepOrigin
                            className={`${s['heading-logo']} object-contain`}
                            layout="fill"
                            src={convertUrlFormat(
                                cslData.cloudinaryLogo[0].secure_url
                            )}
                            alt={correctImageAlt(
                                t(
                                    `alt|${cslData.cloudinaryLogo[0].context?.custom?.alt}`
                                )
                            )}
                        />
                    </div>
                )}
                {cslData?.heading && (
                    <h3 className={s['heading']}>
                        {cslData?.heading?.toUpperCase()}
                    </h3>
                )}
            </div>
            <div className={s['photo-container']}>
                {cslData?.cloudinaryMainImage?.[0]?.secure_url && (
                    <div className={s['main-image']}>
                        <CorsairImage
                            keepOrigin
                            layout="fill"
                            src={convertUrlFormat(
                                cslData.cloudinaryMainImage[0].secure_url
                            )}
                            alt={correctImageAlt(
                                t(
                                    `alt|${cslData.cloudinaryMainImage[0].context?.custom?.alt}`
                                )
                            )}
                            objectFit="contain"
                            className="max-w-full h-auto"
                        />
                    </div>
                )}
                <div className={`${s['product-tiles-container']} md:h-full`}>
                    {cslData?.productImages?.map(
                        (productImage: any, index: number) => {
                            return (
                                <div
                                    className={s['product-tiles-item']}
                                    key={index}
                                >
                                    {productImage?.desktop?.cloudinaryImage?.[0]
                                        ?.secure_url && (
                                        <div
                                            className={`${s['product-tiles-img']}`}
                                            style={{
                                                backgroundImage: `url(${productImage?.desktop?.cloudinaryImage?.[0]?.secure_url})`,
                                                backgroundSize: 'contain',
                                                backgroundPosition: 'center',
                                                backgroundRepeat: 'no-repeat'
                                            }}
                                        />
                                    )}
                                    {productImage?.desktop?.cloudinaryImage?.[0]
                                        ?.secure_url &&
                                        productImage?.mobile
                                            ?.cloudinaryImage?.[0]
                                            ?.secure_url && (
                                            <div
                                                className={`${s['product-tiles-img']} ${s['product-tiles-img-mobile']}`}
                                                style={{
                                                    backgroundImage: `url(${productImage?.mobile?.cloudinaryImage?.[0]?.secure_url})`,
                                                    backgroundSize: 'contain',
                                                    backgroundPosition:
                                                        'center',
                                                    backgroundRepeat:
                                                        'no-repeat'
                                                }}
                                            />
                                        )}
                                    <p>{productImage?.desktopProductTitle}</p>
                                    <p>
                                        {
                                            productImage?.desktopProductDescription
                                        }
                                    </p>
                                    {productImage?.ctaButton?.displayText && (
                                        <a
                                            href={productImage?.ctaButton?.url}
                                            target={
                                                productImage.ctaButton
                                                    .openInANewTab
                                                    ? '_blank'
                                                    : '_self'
                                            }
                                            rel="noopener noreferrer"
                                            className={`${s['product-cta-button']} flex items-center`}
                                        >
                                            {
                                                productImage?.ctaButton
                                                    ?.displayText
                                            }
                                            <svg
                                                width="8"
                                                height="13"
                                                viewBox="0 0 8 13"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M1.75 12.5L0 10.75L4.5 6.25L0 1.75L1.75 0L8 6.25L1.75 12.5Z"
                                                    fill="#ECE81A"
                                                />
                                            </svg>
                                        </a>
                                    )}
                                </div>
                            )
                        }
                    )}
                </div>
            </div>
        </div>
    )
}

export default Slide
