import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import Link from 'next/link'
import CorsairModal from '../CorsairModal/CorsairModal'
import CorsairVideo from '../CorsairVideo/CorsairVideo'
import { HtmlContentPage } from '../HtmlContentPage'
import React, { useState, useMemo, useCallback, memo } from 'react'
import {
    CommonButtonContentProps,
    CommonButtonProps,
    LinkButtonProps,
    ModalButtonProps
} from './CTA.types'
import s from './CTAButton.module.scss'
import decode from '../../../lib/utils/htmlencoder'
import { btnType } from '@pylot-data/hooks/contentful/use-content-json'

// ChevronRight icon component
const ChevronRightIcon = memo(() => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 320 512"
        width={20}
        height={20}
        fill="currentColor"
        aria-hidden="true"
    >
        <path d="M310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z" />
    </svg>
))
ChevronRightIcon.displayName = 'ChevronRightIcon'

const sanitizeHTML = (html: string): string => (html ? decode(html) : '')

const getButtonTypeClass = (type?: btnType): string => {
    const classMap = {
        primary: 'scuf-button-primary',
        secondary: 'scuf-button-secondary',
        link: 'scuf-button-link',
        dark: 'scuf-button-dark'
    }
    return type
        ? classMap[type] || 'scuf-button-primary'
        : 'scuf-button-primary'
}

// Common button content component
const CommonButtonContent = memo<CommonButtonContentProps>(
    ({ displayText, textColor, children, iconPosition, className }) => {
        const { t } = useTranslation(['common'])
        return (
            <div
                className={cn(s['component-cta'], className, {
                    [s['icon-left']]: iconPosition
                })}
                style={{ color: textColor }}
            >
                <span>{t(displayText)}</span>
                {children}
            </div>
        )
    }
)
CommonButtonContent.displayName = 'CommonButtonContent'

// Common button component
const CommonButton = memo<CommonButtonProps>(
    ({
        displayText,
        textColor,
        buttonType,
        className,
        style,
        onClick,
        disabled = false,
        loading = false,
        showRightArrow,
        icon,
        iconPosition,
        'aria-label': ariaLabel
    }) => {
        const handleClick = useCallback(() => {
            if (!disabled && !loading && onClick) onClick()
        }, [disabled, loading, onClick])

        const buttonClassName = cn(
            'flex items-center',
            s['cta-button-root'],
            className?.root,
            getButtonTypeClass(buttonType),
            { [s['button-loading']]: loading }
        )

        return (
            <div>
                <button
                    type="button"
                    onClick={handleClick}
                    style={
                        {
                            ...style,
                            '--button-text-color': textColor
                        } as React.CSSProperties
                    }
                    className={buttonClassName}
                    aria-label={ariaLabel || displayText}
                    aria-disabled={disabled}
                    disabled={disabled || loading}
                >
                    <CommonButtonContent
                        displayText={displayText}
                        textColor={textColor}
                        iconPosition={iconPosition}
                        className={icon ? s['custom-icon'] : ''}
                    >
                        {icon && icon.length > 0 ? (
                            <span
                                className={cn(
                                    s['button-icon'],
                                    className?.icon
                                )}
                            >
                                <img
                                    src={icon[0].secure_url}
                                    alt=""
                                    role="presentation"
                                    loading="lazy"
                                />
                            </span>
                        ) : (
                            showRightArrow && (
                                <div className={s['component-cta-icon']}>
                                    <svg
                                        width="100%"
                                        height="100%"
                                        viewBox="0 0 24 24"
                                        fill={textColor}
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            clipRule="evenodd"
                                            d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"
                                            fill={textColor}
                                        />
                                    </svg>
                                </div>
                            )
                        )}
                    </CommonButtonContent>
                </button>
            </div>
        )
    }
)
CommonButton.displayName = 'CommonButton'

// Link button component
export const LinkButton = memo<LinkButtonProps>(
    ({
        url,
        openInANewTab = false,
        icon,
        className,
        style,
        buttonType,
        textColor,
        disabled = false,
        loading = false,
        showRightArrow,
        displayText,
        iconPosition,
        ...commonButtonProps
    }) => {
        // Don't render if URL is invalid
        // if (!isValidURL(url)) {
        //     console.warn(`CTAButton: Invalid URL provided: ${url}`)
        //     return null
        // }

        const linkClassName = cn(
            className?.root,
            s['cta-button-root'],
            'corsair-links',
            getButtonTypeClass(buttonType),
            { [s['button-loading']]: loading }
        )

        const linkStyle = {
            ...style,
            pointerEvents: disabled ? ('none' as const) : ('auto' as const),
            opacity: disabled ? 0.5 : 1
        } as React.CSSProperties

        return (
            <div
                className={cn(
                    className?.wrapper,
                    'relative',
                    s['component-cta-wrapper']
                )}
            >
                <Link href={url}>
                    <a
                        target={openInANewTab ? '_blank' : undefined}
                        rel={openInANewTab ? 'noreferrer noopener' : undefined}
                        className={linkClassName}
                        style={linkStyle}
                        aria-label={
                            commonButtonProps['aria-label'] || displayText
                        }
                        aria-disabled={disabled}
                        tabIndex={disabled ? -1 : 0}
                    >
                        <CommonButtonContent
                            displayText={displayText}
                            textColor={textColor}
                            iconPosition={iconPosition}
                            className={icon ? s['custom-icon'] : ''}
                        >
                            {icon && icon.length > 0 ? (
                                <span
                                    className={cn(
                                        className?.icon,
                                        s['button-icon']
                                    )}
                                >
                                    {icon.map((img, index) => (
                                        <img
                                            key={index}
                                            src={img.secure_url}
                                            alt=""
                                            role="presentation"
                                            loading="lazy"
                                        />
                                    ))}
                                </span>
                            ) : (
                                showRightArrow && (
                                    <div className={s['component-cta-icon']}>
                                        <svg
                                            width="100%"
                                            height="100%"
                                            viewBox="0 0 24 24"
                                            fill={textColor}
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                fillRule="evenodd"
                                                clipRule="evenodd"
                                                d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"
                                                fill={textColor}
                                            />
                                        </svg>
                                    </div>
                                )
                            )}
                        </CommonButtonContent>
                    </a>
                </Link>
            </div>
        )
    }
)
LinkButton.displayName = 'LinkButton'

// Modal button component
export const ModalButton = memo<ModalButtonProps>((props) => {
    const { t } = useTranslation(['common'])
    const [openModal, setOpenModal] = useState(false)

    const {
        popupVideoUrl,
        videoTranscript,
        popupHtmlContent,
        showRightArrow,
        className,
        ...commonButtonProps
    } = props
    const isYoutubeVideo =
        popupVideoUrl?.includes('youtube.com') ||
        popupVideoUrl?.includes('youtu.be')
    const sanitizedTranscript = videoTranscript
        ? sanitizeHTML(videoTranscript)
        : ''

    // Secure YouTube URL
    const secureVideoUrl = useMemo(() => {
        if (!popupVideoUrl) return ''
        if (isYoutubeVideo) {
            try {
                const url = new URL(popupVideoUrl)
                if (url.protocol !== 'https:') url.protocol = 'https:'
                return url.toString()
            } catch {
                console.warn('CTAButton: Invalid YouTube URL provided')
                return ''
            }
        }
        return popupVideoUrl
    }, [popupVideoUrl, isYoutubeVideo])

    return (
        <div
            className={cn(
                s['video-modal-button'],
                s['component-cta-wrapper'],
                className?.root
            )}
        >
            <CommonButton
                {...commonButtonProps}
                onClick={() => setOpenModal(true)}
                aria-haspopup="dialog"
                aria-expanded={openModal}
                showRightArrow={showRightArrow}
                className={{
                    root: s['video-modal-button--button']
                }}
            />
            <CorsairModal
                isOpen={openModal}
                toggleModal={setOpenModal}
                aria-label={`${commonButtonProps.displayText} modal`}
            >
                {popupHtmlContent && (
                    <HtmlContentPage content={popupHtmlContent} />
                )}

                {secureVideoUrl && isYoutubeVideo ? (
                    <iframe
                        width="100%"
                        height="100%"
                        src={secureVideoUrl}
                        title={`${commonButtonProps.displayText} - YouTube video player`}
                        style={{ border: 'none', minHeight: '400px' }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        allowFullScreen
                        loading="lazy"
                    />
                ) : secureVideoUrl ? (
                    <>
                        <CorsairVideo
                            secure_url={secureVideoUrl}
                            autoPlay
                            controls
                            aria-label={`${commonButtonProps.displayText} video`}
                        />
                        {!!sanitizedTranscript && (
                            <details
                                className={s['video-modal-button--transcript']}
                            >
                                <summary
                                    className={
                                        s[
                                            'video-modal-button--transcript-button'
                                        ]
                                    }
                                >
                                    {t('Transcript')}
                                    <span
                                        className={
                                            s[
                                                'video-modal-button--transcript-toggle-icon'
                                            ]
                                        }
                                    >
                                        <ChevronRightIcon />
                                    </span>
                                </summary>
                                <div
                                    className={
                                        s['video-modal-button--transcript-text']
                                    }
                                    dangerouslySetInnerHTML={{
                                        __html: sanitizedTranscript
                                    }}
                                    role="region"
                                    aria-label="Video transcript content"
                                />
                            </details>
                        )}
                    </>
                ) : null}
            </CorsairModal>
        </div>
    )
})
ModalButton.displayName = 'ModalButton'
