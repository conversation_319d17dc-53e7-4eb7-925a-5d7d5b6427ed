import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useEffect, useMemo, useRef } from 'react'
import CorsairVideo from '../CorsairVideo/CorsairVideo'

import s from './BlockTwoTile.module.scss'
import { BlockTwoTileContent } from './BlockTwoTileContent'
import BlockTwoTileMedia from './BlockTwoTileMedia'
import { BlockTwoTileResponse } from './BlockTwoTileTypes'

interface BlockTwoTileProps {
    content: BlockTwoTileResponse
}

const c = /*tw*/ {
    root: 'relative h-auto overflow-hidden BlockTwoTile-container',
    bgImageClasses: 'flex flex-wrap pt-0',
    layoutLeft:
        'flex items-center md:flex-row md:flex-wrap lg:flex-row lg:items-start',
    layoutRight: 'flex items-center lg:flex-row-reverse md:flex-row-reverse',
    disclaimerBlock: `
        ${s['info-disclaimer']}
        font-tomorrow font-light text-white my-2 text-center lg:text-left
`
}

export const leftImage = 'ImageLeft'

export const BlockTwoTile = ({ content }: BlockTwoTileProps) => {
    const animateRef = useRef(null)
    const {
        layoutPosition,
        layoutType,
        backgroundVideoBrightness = 0.5,
        inlineStyling,
        containerType
    } = content
    const { isOnScreen } = useOnScreen(animateRef, content.animation)
    const checkMedia = content?.cloudinaryImageVideo
    const bgImage = content?.cloudinaryBackgroundImage
    const mobileBgImage =
        content?.cloudinaryMobileBackgroundImage?.[0]?.secure_url
    const imageWidth = content?.imageWidth
    const imageHeight = content?.imageHeight
    const forceImageTakesFullHeight = content.forceImageTakesFullHeight
    const variants = content?.variants
    const containerProps = {
        layoutPosition,
        checkMedia,
        layoutType,
        imageWidth,
        imageHeight,
        forceImageTakesFullHeight
    }

    const matches = useMediaQuery('(min-width: 1024px)')

    useEffect(() => {
        const onScroll = () => {
            const blockTwoTileMedia = document.getElementById(
                'blockTwoTileMedia'
            )
            const blockTwoTileMediaRect = blockTwoTileMedia?.getBoundingClientRect()

            if (
                blockTwoTileMedia &&
                blockTwoTileMediaRect &&
                content?.animation &&
                matches
            ) {
                if (blockTwoTileMediaRect?.top <= screen.availHeight / 2) {
                    blockTwoTileMedia.classList.add(s['blockOnScreen'])
                }

                if (
                    blockTwoTileMediaRect?.top > screen.availHeight / 2 ||
                    blockTwoTileMediaRect?.top < 0
                ) {
                    blockTwoTileMedia.classList.remove(s['blockOnScreen'])
                }
            }
        }

        if (typeof window !== undefined) {
            window.addEventListener('scroll', onScroll)
        }

        return () => {
            if (typeof window !== undefined) {
                window.removeEventListener('scroll', onScroll)
            }
        }
    }, [])

    const layoutContainer = useMemo(() => {
        switch (containerType) {
            case 'Full Width':
                return cn(s['container-fluid'], 'container-fluid mx-auto')
            case 'Scuf Container':
                return 'scuf-container'
            default:
                return 'scuf-container'
        }
    }, [containerType])

    if (!content) {
        return null
    }

    const bgColor = { background: content.colorCodeGradient || '#000' }
    const styles = {
        backgroundImage: bgImage ? `url("${bgImage?.[0]?.secure_url}")` : '',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center'
    }
    const backgroundStyle =
        content?.cloudinaryBackgroundImage &&
        content.cloudinaryBackgroundImage.length > 0
            ? styles
            : bgColor
    // CSS variables for dynamic padding
    const paddingVariables = {
        '--padding-top-desktop':
            inlineStyling?.paddingTopDesktop !== undefined
                ? `${inlineStyling.paddingTopDesktop}px`
                : undefined,
        '--padding-bottom-desktop':
            inlineStyling?.paddingBottomDesktop !== undefined
                ? `${inlineStyling.paddingBottomDesktop}px`
                : undefined,
        '--padding-top-mobile':
            inlineStyling?.paddingTopMobile !== undefined
                ? `${inlineStyling.paddingTopMobile}px`
                : undefined,
        '--padding-bottom-mobile':
            inlineStyling?.paddingBottomMobile !== undefined
                ? `${inlineStyling.paddingBottomMobile}px`
                : undefined
    }

    // Filter out undefined values
    const dynamicStyles = Object.fromEntries(
        Object.entries(paddingVariables).filter(
            ([, value]) => value !== undefined
        )
    ) as React.CSSProperties

    const hasDynamicPadding = Object.keys(dynamicStyles).length > 0

    // Generate class name for horizontal padding
    const paddingsClassheading =
        (content?.heading && content.heading?.replace(/[^a-zA-Z0-9_]/g, '')) ??
        'BlockTwoTile'

    return (
        <div
            className={cn(
                c.root,
                s['BlockTwoTile-container'],
                s['negativeBottomMargin'],
                {
                    [s['dynamic-padding']]: hasDynamicPadding,
                    [s[
                        `BlockTwoTile-container--padding-${content.padding}`
                    ]]: !hasDynamicPadding,
                    [s[`BlockTwoTile-container--${variants}`]]: Boolean(
                        variants
                    )
                }
            )}
            id={content.identifier}
            style={{ ...backgroundStyle, ...dynamicStyles }}
        >
            {bgImage?.[0]?.resource_type.startsWith('video') &&
                bgImage?.[0]?.secure_url && (
                    <CorsairVideo
                        secure_url={
                            bgImage?.[0]?.secure_url
                                ? convertUrlFormat(bgImage?.[0]?.secure_url)
                                : ''
                        }
                        className={cn(
                            s['slider-video'],
                            c.bgImageClasses,
                            'lg:items-center object-cover absolute top-0 left-0;'
                        )}
                        fallbackImgUrl={
                            bgImage?.[0]?.secure_url
                                ? convertUrlFormat(bgImage?.[0]?.secure_url)
                                : ''
                        }
                        style={{
                            filter: `brightness(${backgroundVideoBrightness})`
                        }}
                    />
                )}

            <div
                className={cn(
                    s['background-image'],
                    c.bgImageClasses,
                    layoutPosition === leftImage ? c.layoutLeft : c.layoutRight,
                    layoutPosition === leftImage
                        ? s['layout-left']
                        : s['layout-right'],
                    layoutContainer,
                    'lg:items-center md-max:p-0',
                    {
                        [s['has-animate']]: content.animation,
                        [s['onScreen']]: isOnScreen
                    }
                )}
                ref={animateRef}
            >
                {checkMedia && checkMedia.length > 0 && (
                    <div
                        className={cn(
                            {
                                [s['blockTwoTileMedia']]:
                                    matches && content?.animation,
                                [s['sticky-position']]: content?.animation
                            },
                            `horizontalPadding-${paddingsClassheading}`,
                            'block-two-tile-media',
                            `w-full md:w-1/2 lg:w-1/2 flex justify-center`,
                            s['blockTwoTileMediaCustom']
                        )}
                        id="blockTwoTileMedia"
                    >
                        <BlockTwoTileMedia {...containerProps} />
                    </div>
                )}

                <BlockTwoTileContent content={content} />
            </div>
        </div>
    )
}

export default BlockTwoTile
