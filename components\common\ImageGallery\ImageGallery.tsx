import React, { VFC } from 'react'
import s from './ImageGallery.module.scss'
import { ImageGallerySystem } from '../types'
import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
type ImageGalleryProps = {
    content: ImageGallerySystem
}

const c = /*tw*/ {
    parent: 'bg-black',
    container: `md:p-12 py-12 flex items-center justify-center content-center ${s['gallery-row']}`,
    square: s.square,
    twoThree: s['two-three']
}

const ImageGallery: VFC<ImageGalleryProps> = ({
    content
}): JSX.Element | null => {
    const { t } = useTranslation(['common'])
    const ratio = `${content.ratio ? c.square : c.twoThree}`
    const {
        heading,
        subheading,
        headingsPosition,
        headingType,
        description
    } = content
    return (
        <div className={content.pageBackgroundImage ? '' : c.parent}>
            {(heading || subheading) && (
                <div className={s['header']}>
                    {subheading && (
                        <p
                            className={`${s['subheading']} ${
                                headingsPosition ? 'text-left' : ''
                            }`}
                        >
                            {subheading}
                        </p>
                    )}
                    {heading &&
                        (headingType === 'H1' ? (
                            <h1
                                className={`${s['heading']} ${
                                    headingsPosition ? 'text-left' : ''
                                }`}
                            >
                                {heading}
                            </h1>
                        ) : (
                            <h2
                                className={`${s['heading']} ${
                                    headingsPosition ? 'text-left' : ''
                                }`}
                            >
                                {heading}
                            </h2>
                        ))}
                    {description && (
                        <p className={s['description']}>{description}</p>
                    )}
                </div>
            )}
            <div className={c.container}>
                {content?.cloudinaryImages?.map((image, index) => {
                    return (
                        <div className={ratio} key={index}>
                            {image?.secure_url && (
                                <CorsairImage
                                    keepOrigin
                                    alt={correctImageAlt(
                                        t(`alt|${image.context?.custom?.alt}`)
                                    )}
                                    src={convertUrlFormat(image.secure_url)}
                                    width={image.width || 335}
                                    height={image.height || 335}
                                    className={s['image']}
                                />
                            )}
                        </div>
                    )
                })}
            </div>
        </div>
    )
}

export default ImageGallery
