.accordion-container {
    @apply font-tomorrow text-black bg-white;
    border: 1px solid #999999;

    .accordion-body {
        @apply max-h-0 overflow-hidden relative transform duration-300 transition-all bg-white text-black;
        z-index: 1;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0.05rem;

        .accordion-content {
            @apply text-black bg-white;
            white-space: pre-wrap;
        }
    }

    .active {
        .accordion-body {
            @apply transform duration-300 transition-all text-black bg-white;
            max-height: 100%;
            transition-timing-function: cubic-bezier(0.495, 0.03, 0.485, 0.22);
        }
    }

    .icon {
        transition: all 0.3s ease-out;
        transform-origin: center;
        @apply bg-white;
    }

    .close-icon {
        transform: rotateZ(180deg);
    }
}

.accordion-wrapper {
    padding: 18px 24px 18px 24px;
    @apply text-black bg-white #{!important};

    [class*='checkbox-container'] [class*='label-checkbox'] {
        @apply text-black;
    }

    .header {
        @apply w-full relative flex justify-between items-center;
        @apply text-black bg-white #{!important};
        .toggle {
            @apply flex outline-none bg-none text-left relative;
            padding-right: 24px;
            flex: 1;
            justify-content: unset;
            text-decoration: none;

            &:hover {
                text-decoration: none;
            }
        }

        .title {
            @apply font-primary font-semibold;
            font-size: 14px;
            color: black;
            line-height: 20px;
            letter-spacing: 0.05rem;
        }

        .icon {
            height: 20px;
        }
    }
}
