@mixin cta-button {
    @apply flex items-center z-50 font-medium leading-none m-auto;
    padding: 14px 30px;
    font-size: 18px;
    height: 46px;

    @screen md-max {
        padding: 14px 20px;
    }
}

@mixin background {
    transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    object-fit: cover;
}

.hero-container {
    height: var(--desktop-height);

    @screen md-max {
        height: var(--mobile-height);
    }

    .hero-background-image {
        @apply bg-cover bg-center;
        background-image: var(--mobile-bg-image);
        background-color: var(--bg-color);

        @screen md {
            background-image: var(--desktop-bg-image);
        }
    }
}

.hero-standard {
    padding-left: 2rem;
    padding-right: 2rem;
    transform: translateY(3.846rem);
    transition: transform 0.9s ease-in, opacity 0.85s ease-in;

    &.onScreen {
        opacity: 1;
        transform: translateY(0);
    }

    @screen lg {
        @apply m-auto;
    }

    @media screen and (max-width: 767px) {
        padding-left: 0;
        padding-right: 0;
    }

    &-logo {
        @apply m-auto flex items-center justify-center;
        margin-bottom: 30px;
    }

    &-wrapper {
        background-origin: content-box;
        background-size: cover !important;
        // background-position: center center !important;
    }

    &-disclaimer span {
        font-weight: 400;
        letter-spacing: 0.075em;
        background: none;
        font-size: 0.9rem;
    }

    &-belowImageText {
        margin: auto;
        font-size: 1rem;
        letter-spacing: inherit;
        line-height: 1.15rem;
        margin-top: 2rem;

        @screen md {
            font-size: 1.3rem;
            line-height: 2.15rem;
            width: 84%;
            max-width: 1200px;
        }

        @screen lg {
            letter-spacing: inherit;
        }
    }

    :global {
        .topTextLayout {
            padding-top: 0;

            @media screen and (max-width: 767px) {
                width: 95%;
                margin: auto;
            }
        }

        .alignleft {
            .topTextLayout {
                @apply text-left pl-0;

                &-description {
                    margin: unset;
                }
            }

            .discalimer {
                &-copy {
                    @apply text-left pl-0 ml-0;
                }
            }

            .heroStandardIconsBlock {
                justify-content: left;

                .heroStandardIcon {
                    justify-content: left;
                }
            }

            .heroStandardCTAButton {
                @apply ml-0;
            }

            .heroStandardLogo {
                @apply ml-0;
            }
        }
    }

    &-iconsBlock {
        grid-gap: 1rem;
        margin-top: 4rem;

        @screen sm {
            @apply flex-row;
            height: 3rem;
        }
    }

    &-icon {
        display: grid;
        grid-template-columns: 1fr 1fr;
        max-width: 360px;

        @screen sm {
            @apply flex;
        }

        span {
            margin-right: 1.25rem !important;
        }

        p {
            font-size: 0.75rem;
            line-height: 1rem;
            color: white;
            letter-spacing: 0.2rem;

            @screen sm {
                font-size: 0.875rem;
            }
        }
    }

    &-imageVideo {
        width: fit-content;
        margin-top: 1.25rem;
        min-width: 100vw;
        margin-left: calc(50% - 50vw);

        span {
            display: block !important;
        }

        @screen sm {
            min-width: unset;
            margin: 4rem auto auto;
        }
    }

    &-description {
        @apply m-0;
        width: 100%;
        color: #ffffff;
        font-size: var(--description-font-size);
        font-weight: 500;
        line-height: 30px;
        font-family: 'Sofia Sans Semi Condensed', 'Helvetica Neue', Helvetica,
            Arial, sans-serif;

        @media screen and (max-width: 911px) {
            font-size: 16px;
            letter-spacing: unset;
            line-height: 24px;
        }
    }

    &-cta {
        @apply flex flex-col justify-center;

        .suffix-icon {
            margin-left: 10px;
        }

        &-container {
            @apply w-fit font-tomorrow;
        }

        &-top {
            @apply items-center;
        }

        &-lower-left {
            @apply items-start;
        }

        &-lower-right {
            @apply items-end;
        }
    }

    &-cta-button-top,
    &-cta-button-background-top,
    &-cta-button-lower-left,
    &-cta-button-lower-right,
    &-cta-button-background-lower-left,
    &-cta-button-background-lower-right,
    &-cta-button-image-top,
    &-cta-button-image-lower-left,
    &-cta-button-image-lower-right {
        @include cta-button;
    }

    .video-banner {
        @apply w-full h-full object-center opacity-100;
        @include background;
    }

    @screen md {
        &-cta-button-lower-left {
            @include cta-button;
            @apply absolute;
            bottom: 280px;
            left: 70px;
        }

        &-cta-button-lower-right {
            @include cta-button;
            @apply absolute;
            bottom: 280px;
            right: 70px;
        }

        &-cta-button-background-lower-right {
            @include cta-button;
            @apply absolute bottom-10 right-5;
        }

        &-cta-button-background-lower-left {
            @include cta-button;
            @apply absolute bottom-10 left-5;
        }

        &-cta-button-image-top {
            @include cta-button;
        }

        &-cta-button-image-lower-left {
            @include cta-button;
            bottom: 60px;
            left: 40px;
        }

        &-cta-button-image-lower-right {
            @include cta-button;
            bottom: 60px;
            right: 40px;
        }
    }

    &-content {
        &.has-cta-button-with-verticalContentPaddings {
            &[content-position='top-left'] {
                &[vertical-padding-size='medium'] {
                    @apply absolute p-0 w-full;
                    top: 160px;
                }

                &[vertical-padding-size='small'] {
                    @apply relative p-0;
                    top: 20px;
                }

                &[horizon-padding-size='large'] {
                    @apply relative;
                    left: calc((100vw - 1366px) / 2 + 20px);
                }

                &[horizon-padding-size='small'] {
                    @media screen and (min-width: 767px) {
                        left: 2%
                    }
                }

                &[horizon-padding-size='medium'] {
                    @media screen and (min-width: 767px) {
                        padding-left: 80px;
                    }
                }

                &[vertical-padding-size='medium'],
                &[vertical-padding-size='small'],
                &[horizon-padding-size='large'],
                &[horizon-padding-size='small'],
                &[horizon-padding-size='medium'] {
                    @screen md-max {
                        top: 20px;
                    }
                }
            }

            &[content-position='bottom-left'] {
                &[vertical-padding-size='large'] {
                    @apply absolute p-0 w-full;
                    bottom: 130px;

                    @screen md-max {
                        bottom: 20px;
                    }
                }

                &[vertical-padding-size='medium'] {
                    @apply absolute p-0 w-full;
                    bottom: 80px;

                    @screen md-max {
                        bottom: 20px;
                    }
                }

                &[vertical-padding-size='small'] {
                    @apply absolute p-0;
                    bottom: 20px;

                    @screen md-max {
                        bottom: 20px;
                    }
                }

                &[horizon-padding-size='large'] {
                    @media screen and (min-width: 1366px) {
                        left: calc((100vw - 1366px) / 2);
                    }

                    @media screen and (min-width: 767px) and (max-width: 1365px) {
                        left: 2%;
                    }
                }

                &[horizon-padding-size='small'] {
                    @media screen and (min-width: 767px) {
                        left: 2%
                    }
                }

                &[horizon-padding-size='medium'] {
                    @media screen and (min-width: 767px) {
                        left: 80px;
                    }
                }

            }
        }

        &.no-cta-button-with-verticalContentPaddings {
            &[content-position='center-left'] {
                @apply absolute;

                @screen md {
                    @apply p-0;
                    top: 50%;
                    transform: translate(0%, -50%);
                }

                &[horizon-padding-size='large'] {
                    @apply absolute;

                    @media screen and (min-width: 1366px) {
                        left: calc((100vw - 1366px) / 2 + 20px);
                    }

                    @media screen and (min-width: 767px) and (max-width: 1365px) {
                        left: 2%;
                    }
                }

                @screen md-max {
                    bottom: 20px;
                    padding: 0px 20px;
                }
            }

            &[content-position='bottom-left'] {

                &[vertical-padding-size='medium'] {
                    @apply absolute p-0 ml-0;
                    bottom: 74px;
                }

                &[vertical-padding-size='large'] {
                    @apply absolute p-0 ml-0;
                    bottom: 190px;
                }

                &[vertical-padding-size='small'] {
                    @apply absolute p-0 ml-0;
                    bottom: 20px;
                }

                &[horizon-padding-size='medium'] {
                    padding-left: 80px;
                }

                &[horizon-padding-size='large'] {
                    @apply absolute;

                    @media screen and (min-width: 1366px) {
                        left: calc((100vw - 1366px) / 2 + 20px);
                    }

                    @media screen and (min-width: 767px) and (max-width: 1365px) {
                        left: 2%;
                    }
                }

                &[vertical-padding-size='medium'],
                &[vertical-padding-size='large'],
                &[vertical-padding-size='small'] {
                    @screen md-max {
                        bottom: 20px;
                        padding: 0px 20px;
                    }
                }
            }

            &[content-position='top-left'] {

                &[vertical-padding-size='medium'],
                &[vertical-padding-size='small'] {
                    @apply md:absolute p-0;
                    top: 160px;

                    @screen md-max {
                        bottom: 20px
                    }
                }
            }

            &[content-position='center-center'] {
                @apply absolute top-1/2 left-1/2 p-0 text-center;
                transform: translate(-50%, -50%);

                @screen md-max {
                    padding: 0 20px;
                    width: 100%;
                }

                :global {
                    .topTextLayout-heading {
                        margin: 0 auto;
                    }

                    .description-section {
                        width: 450px;

                        @screen md-max {
                            width: 100%;
                        }
                    }
                }
            }
        }

        :global {
            .topTextLayout {
                text-align: left;
                max-width: 1600px;
                margin: 0;

                @media screen and (max-width: 767px) {
                    padding: 0 24px;
                }

                &-heading {
                    margin: 0;
                    max-width: 800px;
                    width: min(60%, 850px);
                    font-size: clamp(2rem, 5vw, 4rem);
                    margin-bottom: 33px;

                    @media screen and (max-width: 767px) {
                        width: 100%;
                        margin-bottom: 20px;
                    }
                }

                h3.topTextLayout-heading {
                    @apply font-tomorrow;
                    font-size: 44px;
                    line-height: 120%;
                    letter-spacing: 0px;
                    vertical-align: middle;
                    text-transform: uppercase;
                    width: 440px;
                    margin-bottom: 12px;
                    text-wrap: unset;

                    @screen md-max {
                        width: 100%;
                        font-size: 32px;
                    }
                }

                .description-section {
                    @apply font-light;
                }

                &-only-content {
                    &[text-postion='center-left'] {
                        @screen md {
                            margin-left: 80px;
                        }

                        @media (min-width: 767px) {
                            // max-width: 416px;
                            max-width: 495px;
                        }
                    }

                    &[text-postion='bottom-left'] {
                        @media (min-width: 767px) {
                            max-width: 495px;
                        }
                    }

                    .topTextLayout-heading {
                        font-size: 32px;
                        line-height: 34px;
                        text-wrap: unset;
                        letter-spacing: normal;
                        width: 100%;
                        text-transform: unset;
                        margin-bottom: 20px;
                        // font-family: 'Sofia Sans Semi Condensed',
                        //     'Helvetica Neue', Helvetica, Arial, sans-serif !important;

                        @screen md-max {
                            // font-size: 24px;
                            // line-height: 34px;
                            // font-weight: 400;
                        }
                    }

                    h1.topTextLayout-heading {
                        @apply m-0 font-tomorrow font-medium uppercase;
                        font-style: normal;
                        letter-spacing: 0;
                        font-size: 44px;
                        line-height: 46px;
                        width: 410px;
                        text-wrap: unset;

                        @screen md-max {
                            font-size: 32px;
                            line-height: 34px;
                            width: 100%;
                        }
                    }

                    p.topTextLayout-heading {
                        @apply font-sans font-medium #{!important};
                        font-size: 20px;
                        line-height: 150%;
                        letter-spacing: 0px;
                        text-transform: none;

                        @screen md-max {
                            font-size: 16px;
                        }
                    }

                    .topTextLayout-subHeading {
                        text-transform: none;
                        line-height: 22px;
                        font-size: var(--font-size-desktop);
                        margin: 0;
                        font-family: 'Sofia Sans Semi Condensed',
                            'Helvetica Neue', Helvetica, Arial, sans-serif;
                        font-weight: var(--font-weight-desktop);

                        @screen md-max {
                            font-size: var(--font-size-mobile);
                        }
                    }

                    p.topTextLayout-heading {
                        @apply font-sofiaSans font-light mb-8 md-max:mb-4 ml-0;
                        font-size: 20px;
                        line-height: 150%;
                        letter-spacing: 0px;

                        @screen md-max {
                            font-size: var(--font-size-mobile);
                        }
                    }
                }

                &:has(> .description-section) {
                    @apply pb-5;

                    @screen md {
                        @apply pr-0;
                        width: 496px;
                    }
                }
            }

            .topTextLayout-heading.extraLarge {
                @apply font-tomorrow #{!important};
                font-size: 64px !important;
                line-height: 100% !important;
                text-transform: uppercase;
                width: 800px !important;
                margin-bottom: 0px;
                font-weight: 500 !important;

                @screen md-max {
                    font-size: 32px !important;
                    width: unset !important;
                }
            }
        }

        .hero-standard-logo {
            @apply justify-start pl-12;

            @media screen and (max-width: 767px) {
                @apply pl-6;
            }
        }

        .hero-standard-heading {
            margin-bottom: 32px;
        }

        .hero-standard-button-left-align {
            display: flex;
            // padding: 0 48px;
            position: relative;
            // left: 10px;

            @media screen and (max-width: 767px) {
                padding: 0 25px;
            }

            .hero-standard-cta-top {
                @apply items-start;
            }
        }

        @screen md {
            width: var(--content-width);
        }
    }

    &-only-content {
        padding: 0 290px;
    }
}

.scroll-cta-btn {
    bottom: 72px;
    width: 45px;
    height: 40px;
    background-color: #f4f84a;
    opacity: 0;
    transform: translateY(20px) translateX(-50%) skew(-20deg);
    transition: opacity 0.5s ease-in, transform 0.6s ease-out;
    transform-origin: center bottom;
    position: relative;
    overflow: hidden;

    >svg {
        transform: skew(20deg);
    }

    &.btnOnScreen {
        opacity: 1;
        transform: translateY(0) translateX(-50%) skew(-20deg);
    }

    &:not(:disabled):hover::after,
    &:not(:disabled):focus-visible::after {
        color: #3498db;
        transition: 300ms;
        transform: skew(0) scale(1, 1);
    }

    &:after {
        content: '';
        background: white;
        position: absolute;
        z-index: -1;
        display: block;
        transition: 300ms;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        transform: skew(-20deg) scale(0, 1);
    }

    @media screen and (max-width: 911px) {
        height: 35px;
        width: 40px;

        svg {
            height: 6px;
        }
    }
}