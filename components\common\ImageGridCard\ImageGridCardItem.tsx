import React, { useMemo } from 'react'
import { ImageGridCardItemType } from './ImageGridCard.types'
import s from './ImageGridCard.module.scss'
import CorsairImage from '@corsairitshopify/corsair-image'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import cn from 'classnames'
import Link from 'next/link'

const ImageGridCardItem: React.FC<ImageGridCardItemType> = (
    cardItem
): JSX.Element => {
    const {
        heading,
        headingColor,
        description,
        textColor,
        cloudinaryMainImage,
        backgroundColor,
        url
    } = cardItem

    const { t } = useTranslation(['common'])

    // Memoize styles to prevent unnecessary recalculations
    const containerStyles = useMemo(
        () => ({
            backgroundColor: backgroundColor || undefined
        }),
        [backgroundColor]
    )

    const headingStyles = useMemo(
        () => ({
            color: headingColor || undefined
        }),
        [headingColor]
    )

    const descriptionStyles = useMemo(
        () => ({
            color: textColor || undefined
        }),
        [textColor]
    )

    // Safely process image source with fallback
    const imgSrc = useMemo(
        () =>
            cloudinaryMainImage?.[0]?.secure_url
                ? convertUrlFormat(cloudinaryMainImage[0].secure_url)
                : '',
        [cloudinaryMainImage]
    )

    // Get alt text with fallback
    const altText = useMemo(() => {
        const customAlt = cloudinaryMainImage?.[0]?.context?.custom?.alt
        return customAlt ? t(`alt|${customAlt}`) : heading || 'Product image'
    }, [cloudinaryMainImage, heading, t])

    return (
        <a href={url || ''}>
            <div
                style={containerStyles}
                className={cn(
                    s['card-item'],
                    'card-item',
                    'flex flex-col items-start'
                )}
            >
                {imgSrc && (
                    <div
                        className={cn(s['image-container'], 'image-container')}
                    >
                        <CorsairImage
                            keepOrigin
                            src={imgSrc}
                            alt={altText}
                            objectFit="cover"
                            layout="fill"
                        />
                    </div>
                )}
                {heading && (
                    <h6
                        style={headingStyles}
                        className={cn(
                            s['heading'],
                            'heading',
                            'font-sofiaSans font-bold text-base md:text-xl'
                        )}
                    >
                        {heading}
                    </h6>
                )}
                {description && (
                    <p
                        style={descriptionStyles}
                        className={cn(
                            s['description'],
                            'description',
                            'font-sofiaSans font-normal text-base md:text-xl'
                        )}
                    >
                        {description}
                    </p>
                )}
            </div>
        </a>
    )
}

// Memoize the component to prevent unnecessary re-renders
export default React.memo(ImageGridCardItem)
