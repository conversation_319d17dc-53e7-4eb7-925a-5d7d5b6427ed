import {
    FilterExtraInforBlock,
    FilterExtraInforItem
} from '@components/corra/ProductListFilter/ProductListFilter.types'
import { CloseSVGIcon } from '@components/icons/VideoPlayer/CloseSVGIcon'
import Image from '@corsairitshopify/corsair-image'
import { Maybe } from '@pylot-data/fwrdschema'
import cn from 'classnames'
import { isNil } from 'lodash'
import { FC, HTMLAttributes } from 'react'
import styles from './FilterExtraInfo.module.scss'
import { useTranslation } from 'next-i18next'

interface Props extends HTMLAttributes<HTMLDivElement> {
    onChangeVisible: () => void
    selectedFilterInfo: Maybe<FilterExtraInforBlock>
}

const WHITE = '#ffffff'

interface FilterExtraInfoType extends FC<Props> {
    Item: FC<FilterExtraInforItem>
}

const ExtraIntroItem: FC<FilterExtraInforItem> = ({ description, title }) => (
    <div className={styles.ExtraInfoBlockWrapper}>
        <h5 aria-label={title}>{title}</h5>
        <p aria-label={description}>{description}</p>
    </div>
)

const FilterExtraInfo: FilterExtraInfoType = ({
    className,
    onChangeVisible,
    selectedFilterInfo,
    ...props
}) => {
    const { t } = useTranslation(['common'])

    return (
        <section
            className={cn(
                styles.FilterExtraInfoWrapper,
                {
                    [styles.ShowExtraInfo]: !isNil(selectedFilterInfo)
                },
                className
            )}
            {...props}
        >
            <div
                className={cn(styles.ContentWrapper, {
                    [styles.PaddingNone]: isNil(selectedFilterInfo)
                })}
            >
                <div className={styles.Header}>
                    <div className={styles.CloseWrapper}>
                        <button
                            onClick={() => onChangeVisible?.()}
                            aria-label={t('Close panel')}
                        >
                            <CloseSVGIcon color={WHITE} />
                        </button>
                    </div>
                    <h4 aria-label={selectedFilterInfo?.drawerTitle}>
                        {selectedFilterInfo?.drawerTitle}
                    </h4>
                </div>

                <div className={styles.Body}>
                    {selectedFilterInfo?.items?.map((el, index) => (
                        <ExtraIntroItem key={index + el.title} {...el} />
                    ))}
                </div>
            </div>
        </section>
    )
}

FilterExtraInfo.Item = ExtraIntroItem
export default FilterExtraInfo
