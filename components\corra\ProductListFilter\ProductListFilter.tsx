import { MultiRangeSlider } from '@components/common/MultiRangeSlider/MultiRangeSlider'
import { SliderInputValue } from '@components/common/MultiRangeSlider/MultiRangeSlider.types'
import {
    ApplyFilter,
    FilterInputData,
    FiltersProps
} from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { Aggregation, Maybe } from '@pylot-data/fwrdschema'
import { useContentJson } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { ATTR_PRICE_FILTER } from 'hooks/usePlpManager'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react'
import { MobileFilterQueryParams } from '../SortFilterMobile'
import { FilterBlock } from './FilterElements'
import { EXTRA_INFO_QUERY_PARAMS } from './ProductListFilter.constants'
import styles from './ProductListFilter.module.scss'
import {
    AdditionalInformationForFilterEntries,
    FilterExtraInforBlock
} from './ProductListFilter.types'
import { SidebarFilter } from './SidebarFilter'
import { getAvailableAggregationValues } from './util/getAvailableAggregationValues'
import {
    fillArrayWithLength,
    fromApplyFilterDataToUpdatedFilterData,
    getSortedAggregationListWithFeature
} from './util/transform'

export type ProductListFilterProps = Omit<
    FiltersProps,
    'header' | 'isMemoryPage' | 'id'
> & {
    sliderMaxValue?: number
}

interface CompsProps extends ProductListFilterProps {
    appliedFilters?: MobileFilterQueryParams
    updateAppliedFilters?: (val: MobileFilterQueryParams) => void
}

interface GetUpdatedFilterParams {
    filterData?: FilterInputData
    shouldApply?: boolean
    isSingleSelect?: boolean
    clearPriceSearchCriteria?: boolean
}

export const ProductListFilter = (props: CompsProps): JSX.Element => {
    const {
        aggregations,
        excludeFilter = [],
        category,
        isAccordion = true,
        isLoading = false,
        currencyCode = 'USD', // TODO: pull from config?
        plpManager,
        className,
        enableUnavailableOptions = false,
        isNonTransactional = false,
        isFilterByCompatibility = false,
        appliedFilterList,
        updateAppliedFilters,
        appliedFilters,
        showFilterCount,
        showCount = true,
        sliderMaxValue = 0
    } = props

    const { appliedFilterData = [], appliedSearchCritieria = [], filters } =
        appliedFilters ?? {}
    const extraFilterInfo = useContentJson<AdditionalInformationForFilterEntries>(
        EXTRA_INFO_QUERY_PARAMS,
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )
    const { t } = useTranslation(['plp', 'common'])
    const isMobile = useMediaQuery('(max-width: 767px)')
    const {
        plpState,
        setAggregations,
        setFiltersVisible,
        setSearchCriteria,
        setFilter
    } = plpManager
    const [expand, setExpand] = useState<boolean | number>(true)
    const [mobileExpand, setMobileExpand] = useState<number[]>(
        fillArrayWithLength(Object.keys(aggregations ?? {}).length)
    )
    const [selectedFilterInfo, setSelectedFilterInfo] = useState<
        Maybe<FilterExtraInforBlock>
    >(null)
    const sidebarFilterRef = useRef<HTMLDivElement | null>(null)
    const extraFilterInfoFields = useMemo(
        () => extraFilterInfo?.data?.[0]?.parsedEntries?.items ?? [],
        [extraFilterInfo?.data]
    )
    const router = useRouter()
    useEffect(() => {
        setExpand(true)
    }, [router.asPath])

    const getUpdatedFilterData = ({
        clearPriceSearchCriteria,
        filterData,
        isSingleSelect,
        shouldApply
    }: GetUpdatedFilterParams) => {
        const currentSearchCriteria = isMobile
            ? appliedSearchCritieria
            : plpState.appliedSearchCritieria
        const currentFilter = isMobile
            ? appliedFilterData
            : plpState.appliedFilterData
        const currentFilters = isMobile ? filters : plpState.filters

        const updatedSearchCriteria = clearPriceSearchCriteria
            ? currentSearchCriteria.filter(
                  (criteria) => criteria.attribute_code !== ATTR_PRICE_FILTER
              )
            : currentSearchCriteria

        return fromApplyFilterDataToUpdatedFilterData({
            currentFilter,
            currentFilters,
            currentSearchCriteria: updatedSearchCriteria,
            filterData,
            isSingleSelect,
            shouldApply
        })
    }

    const handleSeeMoreInfo = (data: Maybe<FilterExtraInforBlock>) => {
        setSelectedFilterInfo(data)
    }

    const applyFilter: ApplyFilter = (
        filterData,
        shouldApply,
        isSingleSelect
    ) => {
        const $body: HTMLBodyElement | null = document.querySelector('body')

        if ($body) {
            $body.style.pointerEvents = 'none'
        }

        setTimeout(() => {
            const updatedData = getUpdatedFilterData({
                filterData,
                shouldApply,
                isSingleSelect
            })

            if (isMobile) {
                updateAppliedFilters?.(updatedData)
            } else {
                setSearchCriteria(updatedData.appliedSearchCritieria)
                setFilter({
                    filters: updatedData.filters,
                    appliedFilterData: updatedData.appliedFilterData
                })
            }

            if ($body) {
                $body.style.pointerEvents = 'auto'
            }
        }, 100) // set to 100ms to prevent double click
    }
    const onChangePriceRange = (item: Aggregation) => (
        min: number,
        max: number
    ) => {
        const filterData = {
            requestVar: item.attribute_code,
            value_string: `${min}-${max}`,
            label: item?.label
        }

        const updatedData = getUpdatedFilterData({
            filterData,
            clearPriceSearchCriteria: true,
            isSingleSelect: true,
            shouldApply: true
        })

        if (isMobile) {
            updateAppliedFilters?.(updatedData)
        } else {
            setSearchCriteria(updatedData.appliedSearchCritieria)
            setFilter({
                filters: updatedData.filters,
                appliedFilterData: updatedData.appliedFilterData
            })
        }
    }

    // The first available list of aggregations is used to display filter options onwards
    useEffect(() => {
        if (
            ((category && category.uid !== plpManager.plpState.categoryUid) ||
                !plpState.aggregations ||
                plpState.aggregations.length === 0) &&
            aggregations
        ) {
            setAggregations(aggregations)
        }
    }, [
        aggregations,
        plpState.aggregations,
        setAggregations,
        category,
        plpManager.plpState.categoryUid,
        plpState.appliedFilterData,
        sidebarFilterRef.current
    ])

    // Attribute values from the latest loaded collection (define enabled filter options)
    const availableValues = useMemo(
        () => getAvailableAggregationValues(aggregations),
        [aggregations]
    )

    const aggregationList = useMemo(() => aggregations ?? [], [aggregations])

    const handleChange = (panel: number) => (
        _event: React.MouseEventHandler<HTMLButtonElement>,
        isExpanded: boolean
    ) => {
        if (isMobile) {
            setMobileExpand((prev) =>
                isExpanded
                    ? [...prev, panel]
                    : prev.filter((el) => el !== panel)
            )
        }
        setExpand(isExpanded ? panel : false)
    }

    const sliderPriceValue = useMemo<SliderInputValue>(() => {
        const currentSearchCriteria = isMobile
            ? appliedSearchCritieria
            : plpState.appliedSearchCritieria
        const sliderEl = currentSearchCriteria.find(
            (criteria) => criteria.attribute_code === ATTR_PRICE_FILTER
        )

        const priceAggregation = aggregationList.find(
            (item) => item?.attribute_code === ATTR_PRICE_FILTER
        )

        if (!sliderEl) {
            const finalVal = {
                min: 0,
                max: Math.ceil(
                    parseFloat(priceAggregation?.options?.[0]?.value ?? '0')
                )
            }
            return priceAggregation ? finalVal : null
        }
        try {
            const valueArr = sliderEl.filter_value.split('-')
            const val = {
                min: Math.ceil(parseFloat(valueArr[0])),
                max: Math.ceil(parseFloat(valueArr[1]))
            }
            return val
        } catch (_error) {
            return null
        }
    }, [
        aggregationList,
        appliedSearchCritieria,
        isMobile,
        plpState.appliedSearchCritieria
    ])

    const filtersList = useMemo(() => {
        const sortedAggregationListWithFeature = getSortedAggregationListWithFeature(
            aggregationList,
            isNonTransactional
        )

        return sortedAggregationListWithFeature.map((item, index) => {
            const isPriceRange = item?.attribute_code === ATTR_PRICE_FILTER
            const label = t(`plp-sidebar|${item?.label}`)
            const options = item?.options?.map((opt) => {
                if (!opt?.label) return opt
                const lbl = t(`plp-sidebar|${opt?.label}`)
                opt.label = lbl.includes('plp-sidebar|') ? opt?.label : lbl
                return opt
            })

            if (!item) {
                return undefined
            }

            return isPriceRange ? (
                <li>
                    <MultiRangeSlider
                        min={0}
                        max={sliderMaxValue}
                        title={label}
                        currencyCode={currencyCode ?? 'USD'}
                        onChangeValue={onChangePriceRange(item)}
                        value={sliderPriceValue}
                    />
                </li>
            ) : (
                <Fragment key={index}>
                    {!excludeFilter.includes(item.attribute_code) && (
                        <FilterBlock
                            name={
                                label.includes('plp-sidebar|')
                                    ? item.label
                                    : label
                            }
                            items={options}
                            availableValues={
                                availableValues[item.attribute_code] ?? []
                            }
                            requestVar={item.attribute_code}
                            index={index}
                            applyFilter={applyFilter}
                            isLoading={isLoading}
                            currencyCode={currencyCode}
                            showFilterCount={showFilterCount}
                            showCount={showCount}
                            appliedSearchCritieria={
                                isMobile
                                    ? appliedSearchCritieria
                                    : plpState.appliedSearchCritieria
                            }
                            appliedFilterData={
                                isMobile
                                    ? appliedFilterData
                                    : plpState.appliedFilterData
                            }
                            handleChange={handleChange}
                            expand={isMobile ? mobileExpand : expand}
                            enableUnavailableOptions={enableUnavailableOptions}
                            filterExtraInfo={extraFilterInfoFields}
                            onSeeExtraInfo={handleSeeMoreInfo}
                        />
                    )}
                </Fragment>
            )
        })
    }, [
        plpState.appliedSearchCritieria,
        aggregationList,
        excludeFilter,
        availableValues,
        applyFilter,
        isLoading,
        showFilterCount,
        currencyCode,
        expand,
        isNonTransactional,
        isMobile,
        appliedSearchCritieria,
        mobileExpand,
        sliderPriceValue,
        showCount,
        sliderMaxValue,
        extraFilterInfoFields
    ])

    return (
        <div className={cn(styles.FilterWrapper)}>
            <span className="sr-only">
                {t('The following checkbox filters the results when checked')}
            </span>
            <div className="md:hidden block">
                <SidebarFilter
                    filtersApplied={plpState.appliedSearchCritieria.length}
                    filtersVisible={plpState.filtersVisible}
                    setFiltersVisible={setFiltersVisible}
                    isAccordion={isAccordion}
                    filtersList={filtersList}
                    className={className}
                    applyFilter={applyFilter}
                    appliedFilterList={appliedFilterList}
                    isMobilePopup
                />
            </div>
            <div className="hidden md:block h-full" ref={sidebarFilterRef}>
                <SidebarFilter
                    filtersApplied={plpState.appliedSearchCritieria.length}
                    filtersVisible
                    setFiltersVisible={setFiltersVisible}
                    isAccordion={isAccordion}
                    filtersList={filtersList}
                    className={className}
                    isFilterByCompatibility={isFilterByCompatibility}
                    applyFilter={applyFilter}
                    appliedFilterList={appliedFilterList}
                />
            </div>
            {/* <FilterExtraInfo
                selectedFilterInfo={selectedFilterInfo}
                onChangeVisible={() => handleSeeMoreInfo(null)}
            /> */}
        </div>
    )
}
