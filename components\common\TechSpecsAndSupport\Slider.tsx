import React, { useMemo } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { A11y, Pagination } from 'swiper'

import s from './Slider.module.scss'
import { ImageSlide } from './types'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'

interface SliderProps {
    slides: ImageSlide[]
}

const Slider: React.FC<SliderProps> = ({ slides }) => {
    const { t } = useTranslation()

    const slidesWithId = useMemo(() => {
        return slides.map((slide, index) => ({
            ...slide,
            id: `tech-specs-image-${index}`
        }))
    }, [slides])

    return (
        <Swiper
            className={s['slider']}
            spaceBetween={20}
            slidesPerView={1.2}
            slidesPerGroup={1}
            speed={300}
            modules={[Pagination, A11y]}
            pagination={{
                clickable: true
            }}
            breakpoints={{
                992: {
                    slidesPerView: 3,
                    slidesPerGroup: 2
                }
            }}
        >
            {slidesWithId.map((slide, key) => (
                <SwiperSlide key={key + slide.id}>
                    <div className={s['slide']}>
                        <img
                            className={s['slide--image']}
                            src={convertUrlFormat(
                                slide?.image?.[0]?.secure_url
                            )}
                            alt={correctImageAlt(
                                t(
                                    `alt|${slide?.image?.[0]?.context?.custom?.alt}`
                                )
                            )}
                            loading="lazy"
                        />
                        <div className={s['slide--description-container']}>
                            <div
                                className={s['slide--description']}
                                aria-live="polite"
                                dangerouslySetInnerHTML={{
                                    __html: slide.description
                                }}
                            />
                        </div>
                    </div>
                </SwiperSlide>
            ))}
        </Swiper>
    )
}

export default Slider
