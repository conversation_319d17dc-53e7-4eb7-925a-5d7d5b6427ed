import { useAccount } from './use-account'
import { RequestInit } from '@vercel/fetch'
import { SWRConfiguration } from 'swr'

export const useUser = (
    swrOptions: SWRConfiguration = {},
    fetchOptions?: RequestInit
) => {
    const { data, error, isValidating } = useAccount(swrOptions, fetchOptions)
    const loading = !!(isValidating && !data)
    const isSignedIn = !error && data && !data.errors
    const customer = data?.data?.customer
    const addresses = customer?.addresses || []
    const isSubscribed = customer?.is_subscribed

    const getAddressById = (id: number) => {
        return addresses.filter((value) => {
            return value?.id === id
        })
    }

    return {
        loading,
        error,
        isSignedIn,
        customer,
        addresses,
        isSubscribed,
        getAddressById
    }
}
