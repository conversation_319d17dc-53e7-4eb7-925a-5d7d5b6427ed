import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'

export interface DriverProps {
    title: string
    softwareTitle: string
    softwareImage?: CloudinaryImage[]
    softwarePlatform: string
    releaseInfo: string
    releaseNotes?: string
    link: string
}

export interface DownloadDriversBlockProps {
    title: string
    heading?: string
    headingColor?: string
    headingType?: 'h1' | 'h2' | 'h3' | 'h4'
    backgroundColor?: string
    desktopBackgroundImage?: CloudinaryImage[]
    mobileBackgroundImage?: CloudinaryImage[]
    itemBackgroundColor?: string
    itemTextColor?: string
    paddingBlock?: 'none' | 'small' | 'medium' | 'large'
    modalHeading: string
    modalContent: string
    modalEmailLabel: string
    modalSkipLabel: string
    drivers?: DriverProps[]
    hideOnMobile?: boolean
}

export interface DownloadDriverCardProps {
    content: DriverProps
}

export interface DownloadDriverNewsletterSignupFormProps {
    title: string
    content: string
    emailLabel: string
    skipLabel: string
}

export interface NewsletterSignupFormFields {
    email: string
}
