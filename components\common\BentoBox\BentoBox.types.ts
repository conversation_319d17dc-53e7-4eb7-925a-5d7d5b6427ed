import { CTAType } from '@pylot-data/hooks/contentful/use-content-json'
import { CloudinaryImage } from '../CloudinaryMedia/Cloudinary'
import { inlineStyling } from '../ProfileGrid/types'

export interface BentoTileInterface {
    heading?: string
    description?: string
    textLocation: boolean
    cta?: CTAType
    backgroundImage?: CloudinaryImage[]
    bannerTypeTracking?: string
    bentoTiles?: BentoTileInterface[]
    contentWidth?: string
}

export type BentoTileGap = 'none' | 'tiny' | 'small' | 'medium' | 'large'
export type BentoTileLayout = '2' | '2-3'
export type BentoTileContentPosition = 'top-left' | 'bottom-left'

export interface BentoContentInterface {
    layout?: BentoTileLayout
    layoutGap?: BentoTileGap
    tiles?: BentoTileInterface[]
    tileContentPosition?: BentoTileContentPosition
    tileDesktopHeight?: string
    tileMobileHeight?: string
}

export interface BentoBoxInterface extends BentoContentInterface {
    heading?: string
    subheading?: string
    padding?: inlineStyling
    container?: string
    backgroundColor?: string
    disclaimerInfo?: string
    additionalInfo?: string
}

export interface BentoBoxProps {
    content?: BentoBoxInterface | null
}
