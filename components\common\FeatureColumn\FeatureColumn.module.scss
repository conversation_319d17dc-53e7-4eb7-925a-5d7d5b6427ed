.FeatureColumnWrapper {
    padding-block: 40px;
    background-color: var(--secondary-off-black);
    color: var(--primary);
    @apply w-full;

    .Title {
        margin-bottom: 40px;
        text-align: center;
    }

    .ItemWrapper {
        display: flex;

        @screen md-max {
            @apply flex-col;
        }
    }

    .ItemHorizontalWrapper {
        padding-top: 20px;
        justify-content: space-evenly;
    }

    .ButtonWrapper {
        @apply flex justify-center;
    }

    .ActionButton {
        margin-top: 40px;
        gap: 10px;
        @apply flex;

        .IconWrapper {
            width: 17px;
            height: 17px;
            @apply relative;
        }
    }
}

.FeatureColumnItemWrapper {
    display: flex;
    margin-bottom: 20px;
    padding-right: var(--padding-right);

    @screen md-max {
        @apply pr-0;
    }

    .ImageWrapper {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
        @apply relative;
    }

    h5 {
        @apply font-bold;
    }

    h5,
    p {
        @apply text-white m-0;
    }
}

.VerticalItem {
    @apply flex-1 flex-col;
}

.HorizontalItem {
    column-gap: 33px;
    @apply flex-row;
    @screen md {
        width: 30%;
    }
    @screen md-max {
        .ImageWrapper {
            width: 64px;
            height: 64px;
        }
    }

    @screen lg-max {
        padding-right: 0 !important;
    }

    h5 {
        @apply flex-1;
    }
}
