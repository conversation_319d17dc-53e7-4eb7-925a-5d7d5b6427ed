import { PLPManager } from '@corsairitshopify/corsair-filters-and-sort/src/FilterTypes'
import { SortEnum } from '@pylot-data/enums/SortEnum.d'
import { Maybe, SortField, SortFields } from '@pylot-data/fwrdschema'
import { isEmpty, isEqual } from 'lodash'
import { useTranslation } from 'next-i18next'
import { FC, useState } from 'react'
import { ActiveFilterView } from '../SortFilterMobile.types'
import styles from './SortGroup.module.scss'

const TRANSACTIONAL_FIELDS = ['price']

interface Props {
    plpManager: PLPManager
    isNonTransactional?: boolean
    sortFields: SortFields
    updateFilterViewType: (nextView: ActiveFilterView) => () => void
}

const getDefaultSortValue = (sortFields: SortFields) => {
    const defaultField = sortFields?.default
    const selectedOption = sortFields?.options?.find(
        (opt: any) => opt?.value === defaultField
    )
    const getSortItemValue = (value: string, direction?: SortEnum) => {
        return value !== 'relevance'
            ? { [value]: direction ?? SortEnum.Asc }
            : {}
    }
    if (!selectedOption || !selectedOption?.value) {
        return {}
    }
    const updatedValue =
        selectedOption?.value !== 'price'
            ? getSortItemValue(selectedOption?.value)
            : // TODO: need to handle with price - default its DESC
              getSortItemValue(selectedOption?.value, SortEnum.Desc)
    return updatedValue
}

const SortGroup: FC<Props> = ({
    plpManager,
    sortFields,
    isNonTransactional,
    updateFilterViewType
}) => {
    const [t] = useTranslation('common')
    const sortOptions = isNonTransactional
        ? sortFields?.options?.filter(
              (option) =>
                  option?.value && !TRANSACTIONAL_FIELDS.includes(option?.value)
          )
        : sortFields?.options

    const [activeValue, setActiveValue] = useState(
        isEmpty(plpManager.plpState.sort)
            ? getDefaultSortValue(sortFields)
            : plpManager.plpState.sort
    )

    const onSelectSort = (val: Record<string, SortEnum>) => {
        setActiveValue(val)
        plpManager.applySort(val)
        updateFilterViewType(ActiveFilterView.None)()
    }

    const getSortItem = (
        value: string,
        label: string,
        direction?: SortEnum
    ) => {
        const sortValue =
            value !== 'relevance' ? { [value]: direction ?? SortEnum.Asc } : {}

        return (
            <li>
                <button
                    onClick={() => onSelectSort(sortValue)}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            onSelectSort(sortValue)
                        }
                    }}
                    aria-label={t(label)}
                    className={
                        isEqual(sortValue, activeValue)
                            ? styles.SelectedSort
                            : ''
                    }
                >
                    {t(label)}
                </button>
            </li>
        )
    }

    const renderSortOption = (opt: Maybe<SortField>) => {
        if (!opt || !opt.value) {
            return null
        }
        return opt?.value !== 'price' ? (
            getSortItem(opt?.value, String(opt?.label))
        ) : (
            <>
                {getSortItem(opt?.value, 'Price - Low to High', SortEnum.Asc)}
                {getSortItem(opt?.value, 'Price - High to Low', SortEnum.Desc)}
            </>
        )
    }

    return (
        <ul className={styles.SortWrapper}>
            {sortOptions?.map(renderSortOption)}
        </ul>
    )
}

export default SortGroup
