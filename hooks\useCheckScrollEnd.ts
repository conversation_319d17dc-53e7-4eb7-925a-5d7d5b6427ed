import { useCallback, useEffect, useState } from 'react'

interface ReturnType {
    isScrollEndOfBlock: boolean
}

export const useCheckScrollEnd = (
    elementQuerySelector: string | null
): ReturnType => {
    const [isScrollEndOfBlock, setIsScrollEndOfBlock] = useState(false)

    const onScroll = useCallback(() => {
        if (!elementQuerySelector) return

        const $element = document.querySelector(elementQuerySelector)
        if (!$element) return

        const bottom = $element.getBoundingClientRect().bottom
        setIsScrollEndOfBlock(bottom <= 0)
    }, [elementQuerySelector])

    useEffect(() => {
        window.addEventListener('scroll', onScroll)
        return () => {
            window.removeEventListener('scroll', onScroll)
        }
    }, [onScroll])

    return { isScrollEndOfBlock }
}
