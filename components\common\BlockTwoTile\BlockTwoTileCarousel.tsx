import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { useMemo, useRef } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { A11y, Navigation, Pagination } from 'swiper'

import ArrowRightIcon from '@components/icons/Home/ArrowRightIcon'
import { useMediaQuery } from '@lib/hooks/useMediaQuery'
import { generateRandomString } from 'helpers/stringHelper'
import { executeAnimatePagination } from 'helpers/paginationHelpers'

import BlockTwoTile from './BlockTwoTile'
import { BlockTwoTileCarouselProps } from './BlockTwoTileTypes'
import { JsonToCss } from '../HeroBanner/Util/JsonToCss'

import s from './BlockTwoTileCarousel.module.scss'

const BlockTwoTileCarousel: React.FC<BlockTwoTileCarouselProps> = ({
    content
}) => {
    const {
        carouselItems = [],
        heading,
        headingType = 'h1',
        headingColor
    } = content

    const { t } = useTranslation(['common'])
    const activeDotRef = useRef<HTMLDivElement | null>(null)
    const containerRef = useRef<HTMLDivElement | null>(null)
    const paginationRef = useRef<HTMLDivElement | null>(null)
    const isMobile = useMediaQuery('(max-width: 767px)')

    const formattedName = useMemo(() => {
        return generateRandomString(10)
    }, [])

    const animatePagination = (swiper: any) => {
        executeAnimatePagination({
            swiper,
            activeDot: activeDotRef.current,
            swiperPagination: paginationRef?.current?.children
        })
    }

    const handleSlideChange = (swiper: any) => {
        animatePagination(swiper)
    }

    const stylesDesktop = {
        ...(content?.desktopPaddingTop && {
            'padding-top': `${content?.desktopPaddingTop}rem;`
        }),
        ...(content?.desktopPaddingBottom && {
            'padding-bottom': `${content?.desktopPaddingBottom}rem;`
        })
    }

    const stylesMobile = {
        ...(content?.mobilePaddingTop && {
            'padding-top': `${content?.mobilePaddingTop}rem;`
        }),
        ...(content?.mobilePaddingBottom && {
            'padding-bottom': `${content?.mobilePaddingBottom}rem;`
        })
    }

    const Heading =
        !!heading &&
        React.createElement(
            headingType,
            {
                className: cn(
                    s['heading'],
                    s[`heading-${headingType.toLowerCase()}`]
                ),
                style: {
                    color: headingColor
                }
            },
            heading
        )

    return (
        <div
            className={cn(s['carousel-wrapper'])}
            style={getBackgroundStyle(content)}
        >
            <style jsx>{`
                            @media screen and (min-width: 768px){
                                .verticalPaddings-${formattedName} ${JsonToCss(
                stylesDesktop
            )}
                            }
                            @media screen and (max-width: 767px) {
                                .verticalPaddings-${formattedName} ${JsonToCss(
                stylesMobile
            )}
                            }
                        `}</style>
            {!!Heading && (
                <div className={cn(s['block-two-tile-top-container'])}>
                    {Heading}
                </div>
            )}
            <div className={cn(s['block-two-tile-swiper-container'])}>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['prev'],
                        `${formattedName}-prev`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                    )}
                    aria-label={t('previous')}
                >
                    <ArrowRightIcon />
                </button>
                <button
                    className={cn(
                        s['carousel-nav-button'],
                        s['next'],
                        `${formattedName}-next`,
                        'scuf-button-primary',
                        'scuf-show-right-arrow'
                    )}
                    aria-label={t('next')}
                >
                    <ArrowRightIcon />
                </button>
                <Swiper
                    key={
                        isMobile
                            ? `${formattedName}-mobile`
                            : `${formattedName}-desktop`
                    }
                    className={cn(s['swiper-container'])}
                    modules={[Navigation, Pagination, A11y]}
                    mousewheel
                    spaceBetween={20}
                    slidesPerView={1}
                    slidesPerGroup={1}
                    loop
                    centeredSlides
                    onBreakpoint={animatePagination}
                    onSlideChange={(swiper) => {
                        animatePagination(swiper)
                        handleSlideChange(swiper)
                    }}
                    navigation={{
                        prevEl: `.${formattedName}-prev`,
                        nextEl: `.${formattedName}-next`,
                        disabledClass: '.disabled:opacity-50'
                    }}
                    pagination={{
                        el: `.${formattedName}-pagination`,
                        clickable: true,
                        renderBullet: (idx, className) => {
                            return `<span key={${idx}} class="${className} ${formattedName}-bullet-${idx} custom-dot-container"></span>`
                        }
                    }}
                >
                    {carouselItems.map((carousel, index) => {
                        return (
                            <SwiperSlide key={index}>
                                <BlockTwoTile content={carousel} />
                            </SwiperSlide>
                        )
                    })}
                </Swiper>
            </div>
            <div
                className={cn(
                    s['pagination-container'],
                    s['pagination-container-mobile']
                )}
                ref={containerRef}
            >
                <div className="inline-block relative">
                    <div
                        ref={paginationRef}
                        className={`${formattedName}-pagination flex gap-4`}
                    />
                    <div className="dots-line" />
                    <div ref={activeDotRef} className="active-animated-dot" />
                </div>
            </div>
        </div>
    )
}

export default BlockTwoTileCarousel

const getBackgroundStyle = (
    content: BlockTwoTileCarouselProps['content']
): React.CSSProperties => {
    const backgroundColor = content.backgroundColor || '#333132'
    const mobileUrl = content.mobileBackgroundImage?.[0]?.secure_url
    const desktopUrl = content.desktopBackgroundImage?.[0]?.secure_url

    return {
        '--desktop-bg-image': `url(${desktopUrl})`,
        '--mobile-bg-image': `url(${mobileUrl || desktopUrl})`,
        backgroundColor
    } as React.CSSProperties
}
