.notFound {
    .wrapper {
        transform: translate(-50%, -50%);
        padding: 0 8%;
        top: 50%;
        @screen md-max {
            top: 61%;
        }
    }

    .background {
        min-height: 722px;

        @screen lg {
            min-height: 861px;
        }
    }

    .logo {
        width: 134px;

        @screen md-max {
            width: 75px;
        }
    }

    .heading {
        font-size: 40px;
        line-height: 48px;
        padding: 0 0 0.5rem;
        text-shadow: 0 0 5px #000;
        margin: 20px 0px;
        @screen lg {
            padding: 0 0 1rem;
            line-height: 96px;
            font-size: 96px;
        }
    }
    .subheading {
        text-shadow: 0 0 5px #000;
    }
    .copy {
        font-size: 12px;
        letter-spacing: 0.05rem;
        line-height: 16px;
        text-shadow: 0 0 5px #000;

        @screen lg {
            line-height: 24px;
            font-size: 18px;
        }
    }

    .cta {
        padding: 16px 48px;
        margin: 2rem auto;
        background-color: var(--primary);

        @screen lg {
            margin: 2rem auto;
            max-width: 264px;
        }
    }
}
