import CorsairImage from '@corsairitshopify/corsair-image'
import { Button } from '@corsairitshopify/pylot-ui'
import Link from '@corsairitshopify/pylot-ui/src/Link'
import cn from 'classnames'
import { convertUrlFormat, correctImageAlt } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { useRef, useState } from 'react'
import CorsairModal from '../CorsairModal/CorsairModal'
import CorsairVideo from '../CorsairVideo/CorsairVideo'
import s from './BlockTwoTile.module.scss'
import { twoTileCta } from './BlockTwoTileTypes'
import { HtmlContentPage } from '../HtmlContentPage'
type CTAProps = {
    ctaProps: twoTileCta
    fontColor: string
}

const c = /*tw*/ {
    ctaTextDescription: 'flex justify-center font-tomorrow font-normal',
    ctaAnchor: 'flex justify-center items-center lg:justify-start'
}

export const BlockTwoTileCta = ({ ctaProps, fontColor }: CTAProps) => {
    const { t } = useTranslation(['common'])
    const styles = {
        width: 87,
        height: 45
    }
    const [openCtaModal, setOpenCtaModal] = useState(false)
    const ctaBackgroundExist = ctaProps.ctaBackground !== 'None'
    const openModalBtnRef = useRef<HTMLButtonElement>(null)
    if (ctaProps.meta.contentType === 'componentCta') {
        //check for an url is from cloudinary or youtube
        const isCloudinaryVideo = ctaProps.url?.includes('assets.corsair.com')
        const isYoutubeVideo = ctaProps?.url?.includes('youtube.com')
        const isHtmlContent = ctaProps?.popupHtmlContent
        return (
            <>
                <CorsairModal
                    isOpen={openCtaModal}
                    toggleModal={setOpenCtaModal}
                >
                    {isCloudinaryVideo && (
                        <CorsairVideo
                            secure_url={ctaProps.url!}
                            autoPlay={false}
                            controls
                        />
                    )}
                    {isYoutubeVideo && (
                        <iframe
                            width="100%"
                            height="100%"
                            src={ctaProps.url}
                            title="YouTube video player"
                            frameBorder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            allowFullScreen
                        />
                    )}
                    {isHtmlContent && (
                        <HtmlContentPage content={ctaProps.popupHtmlContent!} />
                    )}
                </CorsairModal>
                {ctaProps.openInPopup && (
                    <button
                        onClick={() => setOpenCtaModal(true)}
                        ref={openModalBtnRef}
                        className={s['component-cta-wrapper']}
                        aria-label={ctaProps.displayText}
                    >
                        <div
                            className={cn(
                                s['component-cta'],
                                ctaBackgroundExist &&
                                    s['component-cta-background']
                            )}
                            style={{
                                color: ctaProps.textColor,
                                backgroundColor: !ctaBackgroundExist
                                    ? 'unset'
                                    : ctaProps.ctaBackground?.toLowerCase(),
                                border:
                                    ctaProps.ctaBorder === 'None'
                                        ? 'none'
                                        : `1px solid ${ctaProps.ctaBorder?.toLowerCase()}`
                            }}
                        >
                            <span>{ctaProps.displayText}</span>
                            {!ctaBackgroundExist && (
                                <div className={s['component-cta-icon']}>
                                    <svg
                                        width="100%"
                                        height="100%"
                                        viewBox="0 0 24 24"
                                        fill={ctaProps.textColor}
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            clipRule="evenodd"
                                            d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"
                                            fill={ctaProps.textColor}
                                        />
                                    </svg>
                                </div>
                            )}
                        </div>
                    </button>
                )}
                {!ctaProps.openInPopup && (
                    <a
                        href={ctaProps.url!}
                        target={ctaProps.openInANewTab ? '_blank' : '_self'}
                        rel="noopener noreferrer"
                        className={cn(
                            'corsair-links',
                            s['component-cta-wrapper']
                        )}
                    >
                        <div
                            className={cn(
                                s['component-cta'],
                                ctaBackgroundExist &&
                                    s['component-cta-background'],
                                ctaBackgroundExist &&
                                    ctaProps.ctaBackground?.toLowerCase() ===
                                        'orange' &&
                                    'scuf-button-primary'
                            )}
                            style={{
                                color: ctaProps.textColor,
                                backgroundColor: !ctaBackgroundExist
                                    ? 'unset'
                                    : ctaProps.ctaBackground?.toLowerCase(),
                                border:
                                    ctaProps.ctaBorder === 'None'
                                        ? 'none'
                                        : `1px solid ${ctaProps.ctaBorder?.toLowerCase()}`
                            }}
                        >
                            <span>{ctaProps.displayText}</span>
                            {!ctaBackgroundExist && (
                                <div className={s['component-cta-icon']}>
                                    <svg
                                        width="100%"
                                        height="100%"
                                        viewBox="0 0 24 24"
                                        fill={ctaProps.textColor}
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            clipRule="evenodd"
                                            d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"
                                            fill={ctaProps.textColor}
                                        />
                                    </svg>
                                </div>
                            )}
                        </div>
                    </a>
                )}
            </>
        )
    }
    return (
        <div
            className={cn(
                s[`cta-position-${ctaProps.descriptionPosition}`],
                'mb-10'
            )}
        >
            {ctaProps?.cta && (
                <Link href={ctaProps.cta?.url}>
                    <a
                        href={ctaProps.cta?.url}
                        target={
                            ctaProps.cta?.openInANewTab ? '_blank' : '_self'
                        }
                        rel="noopener noreferrer"
                        className={`${c.ctaAnchor} ${
                            ctaProps.cta?.url.includes('youtube')
                                ? 'popup-youtube'
                                : 'corsair-links'
                        }`}
                    >
                        <Button
                            className={cn(
                                s[`cta-button-${ctaProps.descriptionPosition}`],
                                s['cta-button-base-styles'],
                                'w-full'
                            )}
                            dangerouslySetInnerHTML={{
                                __html: ctaProps.cta?.displayText
                            }}
                        />
                    </a>
                </Link>
            )}
            <div
                className={
                    s[`cta-position-text-${ctaProps.descriptionPosition}`]
                }
            >
                {ctaProps?.cloudinaryImage?.[0]?.secure_url && (
                    <div
                        className={cn(
                            'relative mb-3',
                            s[
                                `description-image-${ctaProps.descriptionPosition}`
                            ]
                        )}
                        style={styles}
                    >
                        <CorsairImage
                            keepOrigin
                            src={convertUrlFormat(
                                ctaProps?.cloudinaryImage?.[0]?.secure_url
                            )}
                            alt={correctImageAlt(
                                t(
                                    `alt|${ctaProps?.cloudinaryImage?.[0]?.context?.custom?.alt}`
                                )
                            )}
                            layout="fill"
                            objectFit="contain"
                        />
                    </div>
                )}
                <p
                    className={cn(
                        c.ctaTextDescription,
                        s[`description-${ctaProps.descriptionPosition}`]
                    )}
                    dangerouslySetInnerHTML={{
                        __html: ctaProps.ctaRichText!
                    }}
                    style={{ color: fontColor }}
                />
            </div>
        </div>
    )
}
