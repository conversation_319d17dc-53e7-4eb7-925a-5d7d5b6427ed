import type { Maybe } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { PlpAccordionExpanFocusController } from 'helpers/AdaHelper'
import { useTranslation } from 'next-i18next'
import React, { cloneElement, useEffect, useState } from 'react'
import { ChevronDown, ChevronUp, ChevronsDown, ChevronsUp } from 'react-feather'
import s from './Accordion.module.scss'
export type OnAccordionChange = (
    event: React.MouseEventHandler<HTMLButtonElement>,
    isExpanded: boolean
) => void
export interface IAccordion {
    children: JSX.Element
    title?: JSX.Element | Maybe<string>
    isOpen?: boolean
    iconOpen?: JSX.Element
    iconClose?: JSX.Element
    onChange?: OnAccordionChange
    className?: string
    classNames?: {
        root?: string
        iconAccordion?: string
        iconOpen?: string
    }
}

const Accordion = (props: IAccordion): JSX.Element => {
    const {
        children,
        isOpen = false,
        title,
        iconOpen = <ChevronUp className="h-5" />,
        iconClose = <ChevronDown />,
        className = '',
        classNames,
        onChange
    } = props

    const [opened, setOpened] = useState(isOpen)
    const showMoreSizeInitial = 7
    const [showMoreSize, setShowMoreSize] = useState(showMoreSizeInitial)
    const [loadingShowMore, setLoadingShowMore] = useState(false)
    const { t } = useTranslation(['common'])

    useEffect(() => {
        if (isOpen !== opened) {
            setOpened(isOpen)
        }
    }, [isOpen, opened])

    const handleChange = React.useCallback(
        (event) => {
            setOpened(!opened)

            if (onChange) {
                onChange(event, !opened)
            }
        },
        [opened, onChange]
    )

    const handleChangeShowMoreSize = async () => {
        setLoadingShowMore(true)

        if (children.props.items.length <= showMoreSize) {
            PlpAccordionExpanFocusController(
                showMoreSizeInitial,
                showMoreSizeInitial,
                'collapse'
            )
            await setShowMoreSize(showMoreSizeInitial)
        } else {
            PlpAccordionExpanFocusController(
                showMoreSize + showMoreSizeInitial,
                showMoreSizeInitial,
                'expan'
            )
            await setShowMoreSize(showMoreSize + showMoreSizeInitial)
        }
        setLoadingShowMore(false)
    }

    return (
        <div
            className={cn(
                s['accordion-container'],
                className,
                classNames?.root
            )}
        >
            <div
                className={cn(
                    s['accordion-wrapper'],
                    'accordion-wrapper',
                    opened && cn(s['active'], 'active')
                )}
            >
                <div
                    aria-label={
                        typeof title === 'object'
                            ? title?.props?.children[0]
                            : title
                    }
                    className={cn(s['header'], 'accordion-header mb-4')}
                    onClick={handleChange}
                    onKeyPress={handleChange}
                    role="button"
                    tabIndex={0}
                    aria-expanded={isOpen}
                    aria-controls={(typeof title === 'object'
                        ? title?.props?.children[0]
                        : title
                    )
                        ?.toLowerCase()
                        .replaceAll(' ', '-')}
                >
                    <h2 className={cn(s['title'], 'accordion-header-title')}>
                        {title}
                    </h2>
                    <div
                        className={cn(
                            s['icon'],
                            'accordion-icon',
                            {
                                [s['close-icon']]: !isOpen,
                                [cn(classNames?.iconOpen)]: !isOpen
                            },
                            classNames?.iconAccordion
                        )}
                    >
                        {cloneElement(iconOpen)}
                    </div>
                </div>
                <div
                    className={cn(
                        s['accordion-body'],
                        `${opened ? 'block' : 'hidden'} accordion-body mb-4`
                    )}
                    id={(typeof title === 'object'
                        ? title?.props?.children[0]
                        : title
                    )
                        ?.toLowerCase()
                        .replaceAll(' ', '-')}
                >
                    <fieldset
                        className={cn(
                            s['accordion-content'],
                            'accordion-content'
                        )}
                        aria-hidden={opened ? 'false' : 'true'}
                    >
                        <legend className="sr-only">{title}</legend>
                        {!loadingShowMore &&
                            cloneElement(children, { setOpened, showMoreSize })}
                    </fieldset>
                    {children.props &&
                        children.props.items &&
                        children.props.items.length > 0 && (
                            <div
                                className={cn(
                                    s['icon'],
                                    'accordion-icon flex items-center justify-center'
                                )}
                                onClick={handleChangeShowMoreSize}
                                onKeyPress={handleChangeShowMoreSize}
                                role="button"
                                tabIndex={0}
                                aria-label={t('common|See more')}
                            >
                                {children.props.items.length <=
                                showMoreSizeInitial ? (
                                    ''
                                ) : children.props.items.length <=
                                  showMoreSize ? (
                                    <ChevronsUp />
                                ) : (
                                    <ChevronsDown />
                                )}
                            </div>
                        )}
                </div>
            </div>
        </div>
    )
}

export default Accordion
