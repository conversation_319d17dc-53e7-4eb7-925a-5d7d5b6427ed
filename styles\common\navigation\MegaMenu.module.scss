.sub-nav-item-text {
    // @apply font-sofiaSans;

    &--sub-title {
        @apply text-xs uppercase font-light tracking-normal;
    }
}

.sub-nav-container {
    .sub-nav-item-text {
        @apply font-sofiaSans;
    }
}

@media (min-width: 912px) {
    .mobile-visible {
        @apply hidden;
    }

    .desktop-visible {
        @apply block transition-all duration-150;
    }

    .mega-menu-root {
        @apply order-1 z-30 bg-transparent;
        width: calc(100%);
        top: 0;
        max-width: 1360px;

        > button {
            @apply hidden;
        }
    }

    .back-icon {
        @apply hidden;
    }

    .sub-nav-items {
        @apply flex font-tomorrow font-medium py-6 px-6 mx-auto justify-center;
        font-size: 18px;
        letter-spacing: 0.05rem;
        column-gap: 48px;
        max-width: 800px;

        @media (max-width: 912px) {
            column-gap: 20px;
        }

        &:before {
            @apply block absolute top-0 w-full h-full z-40 transition-all duration-150;
            left: 0;
            content: "";
            background-color: unset;
        }
    }

    .sub-nav-item {
        > button,
        > span {
            @apply relative cursor-default z-50 font-medium uppercase;
            // letter-spacing: 0.05rem;
            letter-spacing: 0;
            font-size: 1rem;
            line-height: 1.5625; // 25px

            &:after {
                @apply absolute left-0 w-full bg-repeat;
                background-image: url(/icons/navbar-hover.svg);
                content: "";
                transform: scaleX(0);
                transition: 150ms transform;
                bottom: -10px;
                height: 7px;
            }

            &:focus {
                outline: none;
            }

            &:focus-visible {
                outline: 1px solid white;
                outline-offset: 1px;
                border-radius: 2px;
            }

            &.show-indicator::after {
                transform: scaleX(1);
                border: none;
            }
        }
        > span {
            &:hover::after {
                transform: scaleX(1);
            }
        }
        // > span {
        //     @apply relative cursor-default z-50 font-medium uppercase;
        //     // letter-spacing: 0.05rem;
        //     letter-spacing: 0;
        //     font-size: 1rem;
        //     // line-height: 1.375;

        //     &:after {
        //         @apply absolute left-0 w-full bg-repeat;
        //         background-image: url(/icons/navbar-hover.svg);
        //         content: '';
        //         transform: scaleX(0);
        //         transition: 150ms transform;
        //         bottom: -10px;
        //         height: 7px;
        //     }

        //     &:hover::after {
        //         transform: scaleX(1);
        //     }
        // }
        &-icon {
            @apply hidden;
        }
    }
    .sub-nav-item-text {
        @apply text-center;
        max-width: 100px;
        min-width: 60px;
    }

    .sub-nav-container {
        @apply flex overflow-hidden md:overflow-visible absolute w-full left-0 justify-center transition-all h-full duration-500;
        background-color: var(--secondary-steel-gray);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        padding: 0 !important;
        height: fit-content;
        top: 72px !important;
        transform: translateY(-42rem);
        border-bottom: 1px solid #575656;
        box-shadow: 0px 4px 34px 0px #000000f2;
        transition: transform 0.5s ease;

        > div {
            @apply justify-start;

            @media (min-width: 768px) {
                padding: 15px 25px;
            }

            @media (min-width: 912px) and (max-width: 1100px) {
                @apply px-0;
            }
        }

        > div.menu-content-block {
            max-width: 300px;
        }
    }

    .menu-items {
        @apply grid grid-cols-5 bg-transparent mx-auto items-center justify-center;
        column-gap: 12px;
        row-gap: 24px;

        .menu-item-container {
        }

        li {
            @apply block h-full relative cursor-default;
            max-width: 130px;

            > .desktop-visible {
                @apply flex mx-auto flex-col items-center;
                max-width: 250px;
                &:hover,
                &:focus-visible {
                    .menu-sub-menu-container {
                        a {
                            color: white !important;
                            transform: scale(1.1);
                        }
                    }
                }

                .menu-item-block {
                    @apply block cursor-default;
                    margin-bottom: 10px;
                    max-width: 150px;
                    min-width: 80px;
                    border-radius: 4px;

                    .menu-item-block-image {
                        @apply flex items-center h-full mx-auto my-0 w-full justify-center;

                        img {
                            object-fit: cover;
                        }
                    }
                }

                .menu-sub-menu-container {
                    @apply flex flex-col pt-1 justify-center;

                    @media (min-width: 912px) and (min-width: 1280px) {
                        @apply text-left;
                    }

                    > span {
                        @apply flex justify-center;

                        > a,
                        > div {
                            @apply mb-1 leading-5 tracking-normal font-bold uppercase inline-block;
                            font-size: 16px;
                        }
                    }

                    > div {
                        @apply mb-0;

                        div {
                            @apply mb-0;

                            + div {
                                @apply block pt-3;
                            }

                            a {
                                @apply m-0 leading-tight tracking-wide font-normal text-sm;
                            }
                        }
                    }

                    a {
                        @apply transition-all duration-300 block;
                        font-size: 0.9rem;
                        color: #c4c4c4;
                        @media (max-height: 780px) {
                            padding-top: 0.125rem;
                            padding-bottom: 0.125rem;
                        }
                    }
                }
            }
        }
        &.menu-items-flexbox {
            @apply flex gap-5 flex-wrap;
            align-items: start;
        }
        &.columns-amount {
            &-1 {
                @apply grid-cols-1;
            }

            &-2 {
                @apply grid-cols-2;

                li {
                    min-width: 210px;
                }
            }

            &-3 {
                @apply grid-cols-3;

                li {
                    min-width: 210px;
                }
            }

            &-4 {
                @apply grid-cols-4;

                li {
                    > div {
                        max-width: 150px;
                    }
                }
            }

            &-5 {
                @apply grid-cols-5;

                li {
                    > div {
                        @apply md:h-full;
                        max-width: 250px;
                    }
                }
            }

            &-6 {
                @apply grid-cols-6;
            }

            &-7 {
                @apply grid-cols-7;
            }

            &-8 {
                @apply grid-cols-8;
            }

            &-9 {
                @apply grid-cols-9;
            }

            &-10 {
                @apply grid-cols-10;
            }

            &-11 {
                @apply grid-cols-11;
            }

            &-12 {
                @apply grid-cols-12;
            }
        }
    }

    .secondary-container,
    .footer-nav-items,
    .sub-nav-items-secondary {
        @media (min-width: 912px) {
            @apply hidden;
        }
    }

    .root-active {
        .active-menu-item {
            .sub-nav-container {
                @apply pt-4 pb-4 pl-11 pr-11 z-30 flex;
            }

            .sub-nav-animation {
                transform: translateY(0);
            }

            &.active-menu-item {
                span:after {
                    transform: scaleX(1);
                }
            }
        }
        .sub-nav-items {
            &::before {
                @apply bg-primary transition-all duration-150;
            }
        }
    }

    .menu-content-block {
        @apply w-1/6 relative;
        background-image: none !important;
        margin: 1.25rem 1.5rem 1.25rem 1.5rem !important;
        padding: 1rem !important;
        background-size: cover;
        background-color: white;

        > div {
            @apply w-4/5 mb-2;
            max-width: 140px;
        }

        h2 {
            @apply font-tomorrow text-black text-xl mb-2;
            letter-spacing: 0.05rem;
            line-height: 1.25rem;
        }

        p {
            @apply font-normal text-sm mb-4 tracking-wide text-black;
        }

        a {
            @apply font-tomorrow uppercase text-sm text-yellow font-medium;

            img {
                @apply ml-2 mt-1;
                height: 12px;
                width: 7px;
            }
        }
    }

    .menu-items-container {
        @apply w-full px-2;
    }

    .menu-items-animation {
        @apply opacity-100;
    }

    .menu-top-bar,
    .back-container,
    .menu-account-links,
    .all-nav-item-text {
        display: none !important;
    }

    .callout-container {
        @apply relative overflow-hidden;
        gap: 1.5rem;
        padding: 26px 48px;

        @media (min-width: 912px) and (max-width: 1100px) {
            max-width: 390px !important;
        }

        .callout-column {
            max-width: 215px;
            min-width: 111px;
        }

        .callout-column-title {
            @apply text-base italic;
            margin-bottom: 14px;
        }

        .menu-callout-item {
            @apply block px-0 mx-0 mt-0 w-full relative overflow-hidden;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: unset;

            &:last-child {
                margin-bottom: 0px;
            }

            @media (min-width: 912px) and (max-width: 1100px) {
                width: 111px;
                height: 51px;
            }

            .menu-callout-img {
                @apply object-contain block;
            }

            .menu-callout-img-desc {
                @apply absolute text-white font-medium;
                font-size: 16px;
                line-height: 20.26px;
                padding: 3px 10px;
                background-color: rgba(0, 0, 0, 0.61);
                bottom: 10px;
            }
        }
    }
}

@media (max-width: 1320px) and (min-width: 768px) {
    .menu-content-block + div .menu-items.columns-amount-4 {
        @apply pl-0 max-w-full;
    }
}

@media (min-width: 912px) and (max-width: 1280px) {
    .menu-content-block {
        width: 250px !important;
    }
}

@media (max-width: 911px) {
    .desktop-visible,
    .sub-nav-inactive,
    .callout-container,
    .menu-content-block,
    .menu-item-block {
        @apply hidden;
    }

    .invert-color {
        @apply transition-all duration-150;
        filter: invert(1);
    }

    .mobile-visible {
        @apply block;

        .menu-sub-menu-container {
            @apply text-black;
        }

        [class*="sub-nav-items-secondary"] div h2 {
            @apply text-black;
        }
    }

    .mega-menu-root {
        padding-left: 22px;
        order: 1;

        &:after {
            @apply absolute h-full;
            transform: translateX(-110%);
            content: "";
            z-index: 99;
            transition: transform 400ms;
            top: -5px;
            left: -8px;
            width: calc(100% + 8px);
        }

        &.secondary,
        &.tertiary {
            .menu-container {
                @apply overflow-hidden;
            }
        }
    }

    .sub-nav-active {
        @apply flex;
    }

    .hamburger-menu {
        @apply flex flex-col justify-between;
        width: 22px;
        height: 16px;
    }

    .hamburger-line {
        @apply bg-white w-full;
        height: 2px;
    }

    .menu-top-bar {
        @apply absolute top-0 right-0 z-50 flex items-center justify-center;
        padding: 21px;
    }

    .back-container {
        @apply items-center sticky top-0 bg-primary flex text-black justify-between uppercase font-medium;
        z-index: 1;
        font-size: 16px;
        padding: 21px;

        > div {
            @apply flex items-center relative;
            left: -5px;
            column-gap: 4.62px;

            > span {
                @apply font-tomorrow;
                font-size: 18px;
                line-height: 21.6px;
            }
        }
    }

    .all-nav-item-text {
        a,
        span {
            @apply capitalize;
        }
    }

    .back-button {
        transform: rotate(180deg);
    }

    .menu-container {
        @apply bg-primary;
        transform: translateX(-110%);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
        box-shadow: 0px 4px 34px 0px #000000f2;
    }

    .primary-menu-container,
    .secondary-menu-container,
    .tertiary-menu-container {
        transform: translateX(110%);
    }

    .menu-container,
    .primary-menu-container,
    .secondary-menu-container,
    .tertiary-menu-container {
        @apply fixed overflow-x-hidden bg-primary;
        width: 100%;
        z-index: 999;
        -ms-overflow-style: none;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none;
        }

        &.no-scroll {
            @apply overflow-hidden;
        }
    }

    .primary-menu-container {
        @apply top-0;
    }

    .tertiary-menu-container,
    .secondary-menu-container {
        @apply left-0 top-0 pb-28;
        height: 100vh;
    }

    .secondary-container {
        @apply max-w-full overflow-hidden;

        > div {
            @apply grid-cols-2 grid;
            padding: 10px 20px;
            padding-bottom: 0px;

            // grid-template-columns: 1fr 1fr;
            > div {
                @apply flex w-full pl-3 border-l;
                margin-bottom: 20px;

                > a {
                    img {
                        @apply opacity-75;
                        width: 100% !important;
                    }
                }
            }
        }
    }

    .menu-container {
        @apply left-0;
        width: 100vw;
        height: 100vh;
        top: 0;
    }

    .menu-container,
    .primary-menu-container,
    .secondary-menu-container,
    .tertiary-menu-container {
        transition: transform 500ms;
    }

    .primary-menu-container {
        @apply relative;
        -ms-overflow-style: none;
        scrollbar-width: none;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .sub-nav-items-secondary,
    .sub-nav-items {
        @apply text-black;
        padding: 21px;
    }

    .sub-nav-item {
        margin-bottom: 22px;

        &:last-child {
            margin-bottom: 0;
        }
    }
    .sub-nav-items {
        margin-top: 67px;
        padding: 0px;
        .sub-nav-item {
            padding: 20px;
            border-top: 1px solid var(--secondary-off-black);
            margin-bottom: 0px;
            &:last-child {
                border-bottom: 1px solid var(--secondary-off-black);
            }
        }
    }

    .sub-nav-items-secondary {
        @apply relative px-5 overflow-hidden flex flex-col;

        .callout-column {
            .callout-column-title {
                font-size: 16px;
                margin-bottom: 10px;
                @apply italic;
            }

            .callout-items-container {
                @apply flex items-center justify-between;
                margin-bottom: 24px;
            }
        }

        .menu-callout-item {
            @apply block px-0 mx-0 mt-0 w-full relative;
            width: calc(33.33% - 0.5rem);

            .menu-callout-img {
                @apply object-contain block w-full;
            }

            .menu-callout-img-desc {
                @apply absolute text-white font-medium;
                font-size: 16px;
                line-height: 20.26px;
                padding: 3px 10px;
                background-color: rgba(0, 0, 0, 0.61);
                top: 7px;
            }
        }
    }

    .sub-nav-item-text {
        @apply relative font-tomorrow font-medium cursor-pointer flex items-center outline-none;
        font-size: 18px;
        letter-spacing: 0.48px;
        height: 21.6px;

        img,
        svg {
            @apply transition-all duration-300;
        }

        &:hover {
            svg {
                @apply -mr-2;
            }
        }

        > a {
            @apply flex items-center;
            color: black;
            align-items: end;
        }

        > a,
        span {
            > a {
                @apply md:absolute md:w-full md:h-full md:flex md:items-center md:top-0;
            }
        }

        img {
            width: 30px;
            height: 30px;
        }
    }
    .secondary-menu-container,
    .tertiary-menu-container {
        background-color: var(--secondary-steel-gray);
        a {
            @apply text-white;
        }
    }
    .mega-menu-root {
        .secondary-menu-container {
            .menu-items {
                padding: 0 20px;

                li {
                    margin-bottom: 10px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }

                .sub-nav-item-text {
                    @apply h-full;

                    img {
                        min-width: 80px;
                        min-height: 51px;
                        object-fit: scale-down;
                        margin-right: 20px;
                    }

                    a {
                        flex: 1;

                        > p {
                            @apply uppercase font-light w-full;
                            font-size: 1.125rem;
                            letter-spacing: 0;
                        }
                    }
                }
            }
        }
    }

    .sub-nav-item-icon {
        @apply block;
    }

    .footer-nav-items {
        @apply pt-6;

        .footer-nav-item {
            @apply mb-4 pl-7;

            .footer-nav-item-text span {
                @apply font-medium text-sm;
                color: #c8c8c8 !important;
            }
        }
    }

    .active-push-menu {
        .menu-container {
            transform: translateX(0);
        }

        &.mega-menu-root {
            &:after {
                transform: translateX(0%);
                height: 9999%;
                max-height: max-content;
            }
        }

        &.secondary {
            .menu-container {
                overflow: hidden;

                .primary-menu-container {
                    .menu-account-links-container,
                    .sub-nav-item:not(.active-menu-item),
                    .sub-nav-items-secondary,
                    .secondary-container,
                    .active-menu-item button:not(.back-button):not(.close-button-container),
                    .menu-top-bar {
                        opacity: 0;
                        pointer-events: none;
                    }

                    .sub-nav-items {
                        border-bottom-color: transparent;
                    }
                }
            }

            .active-menu-item {
                .secondary-menu-container {
                    transform: translateX(0);
                    -ms-overflow-style: none;
                    scrollbar-width: none;

                    &::-webkit-scrollbar {
                        display: none;
                    }
                }
            }
        }

        &:after,
        .primary-menu-container {
            transform: none;
        }

        &:after {
            @apply overflow-hidden;
        }

        &.tertiary {
            .active-menu-item .active-menu-item {
                .tertiary-menu-container {
                    transform: translateX(0);
                }
            }
        }
    }

    .has-children {
        > a {
            @apply pointer-events-none relative;
        }
    }

    .menu-account-links-container {
        @apply flex w-full bg-black;
    }

    .menu-account-links {
        @apply flex text-white px-6 items-center w-full;
        height: 50px;

        > div {
            order: 3;
            border: unset;
            flex-grow: 1;
            justify-content: flex-end;

            > button {
                margin: unset;
            }
        }

        > li {
            @apply cursor-pointer relative text-center;

            button {
                @apply text-white uppercase font-normal;
                color: white !important;
                font-size: 14px;
                line-height: 17.72px;
                margin-right: 40px;
            }
        }
    }

    .nav-arrow-right {
        @apply mr-5;

        &:before {
            transform-origin: 100% 0%;
            transform: rotate(45deg);
        }

        &:after {
            transform-origin: 100% 100%;
            transform: rotate(-45deg);
        }
    }

    .nav-arrow-left {
        &:before {
            transform-origin: 0% 0%;
            transform: rotate(45deg);
        }

        &:after {
            transform-origin: 0% 100%;
            transform: rotate(-45deg);
        }
    }

    .close-button-container {
        svg {
            width: 24px;
        }
    }

    .menu-close-btns {
        @apply cursor-pointer opacity-100 relative top-0;

        &:before,
        &:after {
            @apply bg-secondary left-1/2 opacity-50 absolute top-1/2;
            content: "";
            height: 1px;
            transform-origin: 50%;
            transition: 250ms width;
            width: calc(100% - 20px);
        }

        &:before {
            transform: translateX(-50%) translateY(-50%) rotate(45deg);
        }

        &:after {
            transform: translateX(-50%) translateY(-50%) rotate(-45deg);
        }
    }
}

@media (max-width: 768px) {
    .mega-menu-root {
        z-index: 55 !important;
    }
}

@media (max-width: 350px) {
    .secondary-container {
        div > div > div {
            @apply grid-cols-1;
        }
    }
}

@keyframes moveGradient {
    to {
        background-position: 100% 50%;
    }
}
