import CorsairImage from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { convertUrlFormat } from 'helpers/cloudinaryHelper'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { ProductsSlideType } from '../CarouselWrapper/CarouselWrapper.types'
import s from './CarouselPillars.module.scss'

export type CarouselProductTilesItemProps = {
    product: ProductsSlideType
}

const CarouselPillarsItem: FC<CarouselProductTilesItemProps> = ({
    product
}) => {
    const { t } = useTranslation(['common'])
    return (
        <div
            className={cn(s['slider-item-container'])}
            style={{
                backgroundColor:
                    product?.backgroundColor ?? 'var(--secondary-off-black)'
            }}
        >
            <div className={cn(s['slider-item-image-container'])}>
                <CorsairImage
                    keepOrigin
                    src={convertUrlFormat(
                        product.cloudinaryMainImage?.[0]?.secure_url
                    )}
                    alt={t(
                        `alt|${product.cloudinaryMainImage?.[0]?.context?.custom?.alt}`
                    )}
                    layout="fill"
                    objectFit="cover"
                />
            </div>
            <div className={s['slider-item-text-block']}>
                <h5
                    className={s['slider-item-heading']}
                    style={{
                        color: product.headingColor ?? 'var(--white)'
                    }}
                >
                    {product.heading}
                </h5>

                {product?.description && (
                    <p
                        className={s['slider-item-description']}
                        style={{
                            color: product?.textColor ?? 'var(--white)'
                        }}
                        dangerouslySetInnerHTML={{
                            __html: product.description
                        }}
                    />
                )}
            </div>
        </div>
    )
}

export default CarouselPillarsItem
