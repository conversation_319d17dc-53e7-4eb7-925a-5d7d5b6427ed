.container {
    @apply bg-center bg-cover bg-no-repeat relative;

    .contentWrapper {
        max-width: 420px;
        height: 550px;
        margin: auto;
        padding-inline: 20px;
        @screen md {
            height: 700px;
        }

        @screen 2xl {
            height: 900px;
        }
    }
    .main {
        max-width: 354px;
        width: auto;
        margin: auto;
    }
    .headline {
        @apply font-tomorrow font-medium uppercase;
        color: var(--primary);
        padding-top: 40px;
        font-size: 18px;
    }

    .title {
        @apply text-white font-tomorrow font-medium uppercase;
        font-size: 24px;
        line-height: 26px;
    }

    .description {
        @apply text-white font-sofiaSans;
        margin-top: 10px;
        line-height: 24px;
        font-size: 16px;
        @screen md {
            margin-top: 20px;
        }
    }

    .signUpForm {
        margin-top: 30px;
        @screen md {
            margin-left: 10px;
            margin-right: 10px;
        }
        [class*="newsletter-form"] {
            @apply w-full max-w-none flex-none m-0;
            h3,
            p {
                @apply hidden;
            }

            button {
                background-color: var(--secondary-off-black);
                @apply w-2/5 text-white font-tomorrow font-medium;
                font-size: 16px;
                &:disabled,
                &[disabled] {
                    opacity: 0.5;
                    pointer-events: none;
                    cursor: default;
                }
                border: 1px solid white;
                border-radius: 5px;
            }

            input {
                @apply font-sofiaSans text-base font-normal;
                border: 1px solid var(--colspan-light-bg);
                border-radius: 5px;
                color: var(--secondary-off-black);
            }

            [class*="newsletter-form-input-wrapper"] {
                @apply flex-1;

                [class*="success-input"] {
                    border: 1px solid var(--colspan-light-bg) !important;
                    @apply bg-white;
                }
            }

            [class*="response-message-success"] {
                color: var(--white) !important;
                bottom: -38px !important;
            }
            [class*="response-message-error"] {
                color: var(--white) !important;
                bottom: -38px !important;
            }
        }

        .CheckBoxWrapper {
            gap: 8px;
            @apply flex items-start;

            input {
                width: 25px;
                height: 25px;
                outline: none;
                border: 1px solid var(--colspan-light-bg);
                border-radius: 5px;

                &[type="checkbox"] + label {
                    @apply relative cursor-pointer block no-underline;
                    min-height: 25px;
                    padding: 0 0 0 25px;
                    &:before {
                        @apply absolute left-0 top-0 block bg-white;
                        content: "";
                        width: 25px;
                        height: 25px;
                        border: 1px solid var(--colspan-light-bg);
                        border-radius: 5px;
                    }
                }

                &[type="checkbox"]:checked + label::before {
                    background-color: var(--secondary-off-black);
                    border: 1px solid var(--secondary-off-black);
                }
                &[type="checkbox"] + label::after {
                    @apply overflow-hidden text-center absolute opacity-0 font-normal inline-block text-white;
                    font-size: 14px;
                    line-height: 14px;
                    content: "\e907";
                    font-family: "Scuf-Icon";
                    margin: 0;
                    top: 5.5px;
                    left: 5.5px;
                }

                &[type="checkbox"]:checked + label::after {
                    opacity: 1;
                }
            }
            p {
                font-size: 12px;
                line-height: 16px;
                @apply flex-1 m-0 text-white font-sofiaSans;
            }
        }
    }
}
