.header-text-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.product-customize-top-container {
    @apply flex items-center justify-between;
    padding-inline: 20px;
    margin-bottom: 28px;
    @screen md {
        margin-bottom: 65px;
        padding-inline: 80px;
    }
    .header {
        text-align: left;
        text-wrap: balance;
        font-size: 32px;
        line-height: 38.4px;
        @screen md {
            font-size: 54px;
            line-height: 65px;
        }
        @apply font-medium text-white;
    }
}

.pagination-container {
    &.pagination-container {
        @screen md-max {
            @apply flex items-center justify-around;
            margin-top: 40px;
        }
    }

    :global {
        .dots-line {
            position: absolute;
            top: 50%;
            width: calc(100% + 42px);
            left: -21px;
            height: 1px;
            z-index: 0;
            background-color: var(--white);
            transform: translateY(-50%);
        }

        .active-animated-dot {
            z-index: 2;
            position: absolute;
            top: 50%;
            width: 14px;
            height: 14px;
            background-color: var(--primary);
            border: 1px solid var(--white);
            left: 0px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .custom-dot-container {
            @apply relative;
            margin: 0 !important;
            z-index: 1;
            width: 11px;
            height: 11px;
            background-color: var(--secondary-off-black);
            border: 1px solid var(--white);
            border-radius: 50%;
            opacity: 1;
        }

        .swiper-pagination {
            display: flex;
            justify-content: center;
            padding: 0;
            list-style: none;
            bottom: -35px;
        }

        .swiper-pagination .swiper-pagination-bullet {
            margin: 0 3px;
        }
    }
}

.product-customize-swiper-container {
    // .faded-right,
    // .faded-left {
    //     @apply h-full absolute top-0 block z-2;
    //     background: #0f0f0f;
    //     opacity: 0.4;
    //     width: 4.8%;

    //     @screen md-max {
    //         width: 15%;
    //     }
    // }

    // .faded-right {
    //     right: -1px;
    //     background: #0f0f0f;
    // }
    .faded-left {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to left, var(--gradient-stops));
        @apply absolute top-0 left-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .faded-right {
        --color-surface: #000000;
        --gradient-to-position: ;
        --gradient-from-position: ;
        --gradient-stops: var(--gradient-from), var(--gradient-to);
        --gradient-from: transparent var(--gradient-to-position);
        --gradient-to: var(--color-surface) var(--gradient-to-position);
        background-image: linear-gradient(to right, var(--gradient-stops));
        @apply absolute top-0 right-0 h-full w-1/12 z-2;
        @apply opacity-100;
    }

    .prev:disabled,
    .prev:disabled ~ .faded-left {
        @apply opacity-0;
    }
    .prev:disabled ~ .swiper-container {
        // margin-left: 20px;
        transform: translateX(20px);
        @screen md {
            // margin-left: 80px;
            transform: translateX(80px);
        }
    }
    .next:disabled,
    .next:disabled ~ .faded-right {
        @apply opacity-0;
    }
    .next:disabled ~ .swiper-container {
        // margin-right: 20px;
        transform: translateX(-20px);
        @screen md {
            // margin-right: 80px;
            transform: translateX(-80px);
        }
    }
    .prev:disabled + .next:disabled {
        & ~ .swiper-container {
            transform: translateX(0px) !important;
            margin-inline: 20px;
            @screen md {
                margin-inline: 80px;
            }
        }
    }

    @screen md-max {
        .faded-left,
        .faded-right {
            display: none;
        }
    }

    .slider-item-container {
        @apply flex flex-col h-full;
        // padding: 32px 20px;
        padding: 0 20px;

        @screen lg {
            max-width: 429px;
            padding: 0 32px;
        }

        @screen md-max {
            // height: 306px;
        }

        .slider-item-image-container {
            height: 200px;

            @screen lg-max {
                margin-top: 32px;
            }

            @screen lg {
                height: 320px;
            }
        }

        .slider-item-divider {
            @apply w-full bg-white;
            height: 1px;
            margin-block: 24px;
        }

        .slider-item-text-block {
            flex-grow: 1;

            .slider-item-name {
                @apply font-tomorrow font-medium tracking-normal align-middle uppercase;
                font-size: 1.875rem;
                line-height: 120%;
                margin-bottom: 12px;
            }
            .slider-item-compatibility {
                @apply font-sofiaSans font-semibold tracking-normal uppercase;
                font-size: 1.25rem;
                line-height: 150%;
                margin-bottom: 36px;
            }
            .slider-item-description {
                @apply font-sofiaSans font-light tracking-normal;
                font-size: 1rem;
                line-height: 150%;
                line-clamp: 3;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .build-your-own-btn {
            border: 1px solid var(--secondary-off-black);
            box-sizing: border-box;
            margin-block: 32px;
            @apply w-full px-0;

            button {
                width: 100%;
                padding-left: 0 !important;
                padding-right: 0 !important;
            }

            span {
                font-size: 16px;
                line-height: 24px;
                gap: 16px;
                @apply flex items-center font-medium;
            }

            @screen lg-max {
                svg {
                    display: none;
                }
            }
        }
    }

    @apply relative;

    .swiper-container {
        transition: transform 0.1s linear;

        @screen md {
            transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        }
        // &.last-item-reached {
        //     margin-right: 20px;
        //     transition: left 0.35s ease-in-out;
        //     @screen md {
        //         margin-right: 80px;
        //     }
        // }

        // &.first-item-reached {
        //     margin-left: 20px;
        //     transition: left 0.35s ease-in-out;
        //     @screen md {
        //         margin-left: 80px;
        //     }
        // }

        :global(.swiper-slide) {
            height: auto;
        }
    }

    .carousel-nav-button {
        transform: translateY(-50%);
        width: 52px;
        height: 48px;
        @apply z-10 top-1/2 absolute flex items-center justify-center overflow-hidden;

        :global(#Oval-2) {
            stroke: transparent;
        }

        div {
            button {
                &:focus {
                    outline: none;
                }
            }
        }

        &.prev {
            left: 3%;
            @screen md-max {
                @apply hidden;
            }

            svg {
                transform: rotateZ(180deg);
            }
        }

        &.next {
            right: 3%;
            @screen md-max {
                @apply hidden;
            }
        }
    }
}
