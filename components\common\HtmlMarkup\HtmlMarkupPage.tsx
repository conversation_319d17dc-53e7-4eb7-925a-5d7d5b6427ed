import React from 'react'
import { IHtmlMarkup } from '../types'
import decode from '../../../lib/utils/htmlencoder'
import s from './HtmlMarkupPage.module.scss'

interface HtmlMarkupcontent {
    content: IHtmlMarkup
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
const HtmlMarkup = ({ content }: HtmlMarkupcontent) => {
    const encodedHtml = decode(content.markup)?.replaceAll('<p></p>', '<br/>')

    return (
        <div
            dangerouslySetInnerHTML={{
                __html: encodedHtml
            }}
            className={s['custom-cms-page']}
        />
    )
}

export default HtmlMarkup
