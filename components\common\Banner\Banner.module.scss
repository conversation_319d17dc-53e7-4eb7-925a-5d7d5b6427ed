.hero-banner {
    &--section {
        @apply relative bg-cover bg-center;
        background-image: var(--mobile-bg-image);
        background-color: var(--bg-color);
        height: var(--mobile-bg-height);

        @screen md {
            background-image: var(--desktop-bg-image);
            height: var(--desktop-bg-height);
        }
    }

    &--content {
        transform: translateY(3.846rem);
        transition: transform 0.9s ease-in, opacity 0.85s ease-in;

        &.onScreen {
            opacity: 1;
            transform: translateY(0);
        }

        @screen md-max {
            @apply h-full flex flex-col;
            width: min(var(--mobile-content-width), 100%);
            padding: 20px;

            &[data-mobile-position="left"],
            &[data-mobile-position="right"],
            &[data-mobile-position="center"] {
                @apply justify-center;
            }

            &[data-mobile-position="bottom-left"],
            &[data-mobile-position="bottom-center"],
            &[data-mobile-position="bottom-right"] {
                @apply justify-end;
            }

            &[data-mobile-position="top-left"],
            &[data-mobile-position="top-center"],
            &[data-mobile-position="top-right"] {
                @apply justify-start;
            }

            &[data-mobile-position="center"],
            &[data-mobile-position="top-center"],
            &[data-mobile-position="bottom-center"] {
                @apply items-center text-center;
            }

            &[data-mobile-position="left"],
            &[data-mobile-position="top-left"],
            &[data-mobile-position="bottom-left"] {
                @apply items-start text-left;
            }

            &[data-mobile-position="right"],
            &[data-mobile-position="top-right"],
            &[data-mobile-position="bottom-right"] {
                @apply items-end text-right;
            }
        }

        @screen md {
            @apply absolute;
            --vertical-position: 8%;
            width: min(var(--desktop-content-width),100%);

            &[data-desktop-position="left"] {
                top: 50%;
                transform: translateY(-50%);
            }
            &[data-desktop-position="top-left"] {
                top: var(--vertical-position);
            }
            &[data-desktop-position="bottom-left"] {
                bottom: var(--vertical-position);
            }

            &[data-desktop-position="left"],
            &[data-desktop-position="top-left"],
            &[data-desktop-position="bottom-left"] {
                text-align: left;
                left: 3%;

                // @media (min-width: 1366px) {
                //     left: calc((100vw - 1366px) / 2 + 20px);
                // }

                @screen xl-1440 {
                    left: 80px;
                }
            }

            &[data-desktop-position="right"] {
                top: 50%;
                transform: translateY(-50%);
            }
            &[data-desktop-position="top-right"] {
                top: var(--vertical-position);
            }
            &[data-desktop-position="bottom-right"] {
                bottom: var(--vertical-position);
            }

            &[data-desktop-position="right"],
            &[data-desktop-position="top-right"],
            &[data-desktop-position="bottom-right"] {
                text-align: right;
                right: 3%;

                // @media (min-width: 1366px) {
                //     right: calc((100vw - 1366px) / 2 + 20px);
                // }

                @screen xl-1440 {
                    right: 80px;
                }
            }

            &[data-desktop-position="center"] {
                text-align: center;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
            &[data-desktop-position="top-center"] {
                text-align: center;
                left: 50%;
                top: var(--vertical-position);
                transform: translateX(-50%);
            }
            &[data-desktop-position="bottom-center"] {
                text-align: center;
                left: 50%;
                bottom: var(--vertical-position);
                transform: translateX(-50%);
            }
        }

        .hero-banner--sub-heading {
            @apply font-sofiaSans;
            font-size: var(--sub-heading-font-size-mobile);
            font-weight: var(--sub-heading-font-weight-mobile);
            line-height: 1.5rem;
            color: var(--white);
            padding-right: 5px;
            margin-top: 20px;
            white-space: pre-wrap;

            @media (min-width: 767px) {
                font-size: var(--sub-heading-font-size-desktop);
                font-weight: var(--sub-heading-font-weight-desktop);
                line-height: 1.875rem;
                padding-right: 20px;
                margin-top: 20px;
            }
        }

        .hero-banner--cta {
            @apply flex flex-col gap-4;

            @screen md {
                @apply flex-row gap-8;
            }
        }
    }

    &--heading {
        @apply font-tomorrow font-medium uppercase;
        line-height: 110%;
        font-size: var(--heading-font-size-desktop);
        font-weight: var(--heading-font-weight-desktop);
        margin-bottom: 10px;

        @screen md-max {
            letter-spacing: 0;
            color: var(--primary);
            font-size: var(--heading-font-size-mobile);
            font-weight: var(--heading-font-weight-mobile);
            line-height: 100%;
        }
    }



    &--button {
        margin-top: 20px;

        @media (min-width: 767px) {
            margin-top: 40px;
        }
    }
}
